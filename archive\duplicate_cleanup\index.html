<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊图片上传图床工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }

        .header {
            background: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 20px;
        }

        .file-drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .progress {
            height: 20px;
            border-radius: 10px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .result-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .result-item.error {
            border-left-color: #dc3545;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            font-weight: 600;
            margin-right: 5px;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 15px 15px 15px;
            padding: 30px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .status-processing {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .history-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
        }

        .table tbody td {
            padding: 15px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .url-input-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }

            .feature-card {
                margin: 10px 0;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-cloud-upload"></i> 亚马逊图片上传图床工具</h1>
                <p class="mb-0">快速上传产品图片并生成亚马逊格式映射表</p>
            </div>

            <!-- 主要内容 -->
            <div class="p-4">
                <!-- 导航标签 -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="upload-tab" data-bs-toggle="tab"
                            data-bs-target="#upload-panel" type="button" role="tab">
                            <i class="bi bi-cloud-upload"></i> 上传图片
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="url-tab" data-bs-toggle="tab" data-bs-target="#url-panel"
                            type="button" role="tab">
                            <i class="bi bi-link-45deg"></i> URL映射
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-panel"
                            type="button" role="tab">
                            <i class="bi bi-clock-history"></i> 历史记录
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="template-tab" data-bs-toggle="tab" data-bs-target="#template-panel"
                            type="button" role="tab">
                            <i class="bi bi-file-earmark-excel"></i> 模板填充
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="test-tab" data-bs-toggle="tab" data-bs-target="#test-panel"
                            type="button" role="tab">
                            <i class="bi bi-gear"></i> 系统测试
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="mainTabContent">
                    <!-- 图片上传面板 -->
                    <div class="tab-pane fade show active" id="upload-panel" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-images"></i>
                                    </div>
                                    <h4 class="text-center mb-4">批量上传图片</h4>
                                    <div class="alert alert-primary">
                                        <h6><i class="bi bi-list-ol"></i> 上传流程说明</h6>
                                        <ol class="mb-0">
                                            <li><strong>选择图片文件或文件夹</strong> - 支持拖拽上传</li>
                                            <li><strong>设置上传目录</strong> - 指定图床存储路径</li>
                                            <li><strong>选择任务列表</strong> - 可选，用于自动填充商品信息</li>
                                            <li><strong>开始上传</strong> - 批量上传并生成映射表</li>
                                        </ol>
                                    </div>
                                    <div class="alert alert-info">
                                        <small><i class="bi bi-info-circle"></i>
                                            <strong>图片任务列表说明:</strong> 可选择包含ASIN、SKU、MSKU信息的Excel文件，用于自动填充映射表中的商品信息。
                                        </small>
                                    </div>

                                    <!-- 文件拖拽区域 -->
                                    <div class="file-drop-zone" id="fileDropZone">
                                        <i class="bi bi-folder2-open"
                                            style="font-size: 48px; color: #667eea; margin-bottom: 20px;"></i>
                                        <h5><i class="bi bi-1-circle-fill text-primary"></i> 第一步：拖拽图片文件夹到此处或点击选择图片</h5>
                                        <p class="text-muted">支持 PNG, JPG, JPEG, GIF, BMP, WEBP 格式</p>
                                        <p class="text-muted">文件名格式: ASIN_类型.扩展名 (如: B07XXXXX_MAIN.jpg)</p>
                                        <p class="text-warning"><small><i class="bi bi-exclamation-triangle"></i>
                                                建议选择包含所有图片的文件夹，与桌面版本操作一致</small></p>
                                        <input type="file" id="fileInput" multiple accept="image/*" webkitdirectory
                                            style="display: none;">
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                id="selectFilesBtn">
                                                <i class="bi bi-images"></i> 选择图片文件
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="selectFolderBtn">
                                                <i class="bi bi-folder2-open"></i> 选择图片文件夹 (推荐)
                                            </button>
                                        </div>
                                        <input type="file" id="filesInput" multiple accept="image/*"
                                            style="display: none;">
                                    </div>

                                    <!-- 上传设置 -->
                                    <div class="mt-4" id="uploadSettings" style="display: none;">
                                        <div class="alert alert-warning">
                                            <h6><i class="bi bi-folder-plus"></i> 第二步：设置上传目录</h6>
                                            <p class="mb-3">
                                                <strong>请输入要在图床创建的一级目录名</strong><br>
                                                <small class="text-muted">与桌面版本操作完全一致，这是必须的步骤</small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="text" class="form-control" id="uploadFolder"
                                                        placeholder="请输入目录名（不能包含/或\，不能为空）">
                                                    <small class="text-muted">
                                                        <i class="bi bi-lightbulb"></i>
                                                        示例：产品图片、新品上传、DEFAULT、测试图片 等
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-success w-100" id="confirmFolderBtn"
                                                        disabled>
                                                        <i class="bi bi-arrow-right"></i> 下一步
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 任务列表文件选择 -->
                                    <div class="mt-4" id="taskListSection" style="display: none;">
                                        <div class="alert alert-info">
                                            <h6><i class="bi bi-file-earmark-excel"></i> 第三步：选择图片任务列表 (可选)</h6>
                                            <p class="mb-3">
                                                <strong>选择包含ASIN、SKU、MSKU信息的Excel文件</strong><br>
                                                <small class="text-muted">
                                                    用于自动填充映射表中的商品信息，与桌面版本功能完全一致
                                                </small>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="file" class="form-control" id="taskListFile"
                                                        accept=".xlsx,.xls,.xlsm">
                                                    <small class="text-muted mt-1 d-block">
                                                        <i class="bi bi-info-circle"></i>
                                                        Excel文件应包含ASIN、SKU、MSKU等列
                                                    </small>
                                                </div>
                                                <div class="col-md-4">
                                                    <button class="btn btn-primary w-100" id="startUploadBtn">
                                                        <i class="bi bi-cloud-upload"></i> 开始上传
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="alert alert-light mt-3 mb-0">
                                                <small class="text-muted">
                                                    <i class="bi bi-lightbulb"></i>
                                                    <strong>提示：</strong>如果不需要任务列表，可直接点击"开始上传"按钮
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 选中的文件列表 -->
                                    <div id="selectedFiles" class="mt-4" style="display: none;">
                                        <h6>已选择的文件:</h6>
                                        <div id="fileList" class="list-group"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 上传进度 -->
                                <div class="feature-card" id="progressCard" style="display: none;">
                                    <h5><i class="bi bi-activity"></i> 上传进度</h5>
                                    <div class="progress mb-3">
                                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">
                                        </div>
                                    </div>
                                    <div id="progressText">准备上传...</div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            成功: <span id="successCount">0</span> |
                                            失败: <span id="failCount">0</span> |
                                            总计: <span id="totalCount">0</span>
                                        </small>
                                    </div>
                                </div>

                                <!-- 上传结果 -->
                                <div class="feature-card" id="resultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 上传完成</h5>
                                    <div id="uploadResults"></div>
                                    <div class="mt-3" id="downloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadExcel">
                                            <i class="bi bi-download"></i> 下载映射表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- URL映射面板 -->
                    <div class="tab-pane fade" id="url-panel" role="tabpanel">
                        <div class="url-input-area">
                            <h5 class="mb-3">粘贴URL生成映射表</h5>
                            <p class="text-muted">每行粘贴一个图片URL，系统会自动提取ASIN和图片类型。</p>
                            <textarea id="urlInput" class="form-control" rows="8" placeholder="https://example.com/image/B07XXXXX_MAIN.jpg
https://example.com/image/B07XXXXX_PT01.jpg"></textarea>
                            <button class="btn btn-primary mt-3" id="parseUrlBtn">
                                <i class="bi bi-file-earmark-spreadsheet"></i> 生成Excel映射表
                            </button>
                            <div id="url-parse-result" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- 历史记录面板 -->
                    <div class="tab-pane fade" id="history-panel" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-clock-history"></i> 历史映射表</h4>
                            <button class="btn btn-outline-primary" id="refreshHistory">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>

                        <div class="history-table">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>文件大小</th>
                                        <th>修改时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="historyTableBody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 模板填充面板 -->
                    <div class="tab-pane fade" id="template-panel" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-file-earmark-excel"></i>
                                    </div>
                                    <h4 class="text-center mb-4">填充亚马逊模板</h4>
                                    <p class="text-muted text-center">上传模板文件和商品报告，自动填充模板数据</p>

                                    <div class="alert alert-primary">
                                        <h6><i class="bi bi-list-ol"></i> 填充流程说明</h6>
                                        <ol class="mb-0">
                                            <li><strong>选择亚马逊模板文件</strong> - 要填充的Excel模板</li>
                                            <li><strong>选择商品分类报告</strong> - 包含商品数据的报告文件</li>
                                            <li><strong>选择图片映射文件</strong> - 可选，包含图片URL映射</li>
                                            <li><strong>选择产品信息文件</strong> - 可选，包含包装尺寸等信息</li>
                                            <li><strong>设置市场和选项</strong> - 选择目标市场和处理选项</li>
                                            <li><strong>开始填充</strong> - 自动处理并生成结果文件</li>
                                        </ol>
                                    </div>

                                    <!-- 必需文件上传区域 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="templateFile" class="form-label">亚马逊模板文件 <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="templateFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择要填充的亚马逊模板Excel文件</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="reportFile" class="form-label">商品分类报告 <span
                                                        class="text-danger">*</span></label>
                                                <input type="file" class="form-control" id="reportFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">选择包含商品数据的分类报告文件</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 可选文件上传区域 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="mappingFile" class="form-label">图片映射文件 (可选)</label>
                                                <input type="file" class="form-control" id="mappingFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">包含SKU和图片URL映射的Excel文件</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="productInfoFile" class="form-label">产品信息文件 (可选)</label>
                                                <input type="file" class="form-control" id="productInfoFile"
                                                    accept=".xlsx,.xls,.xlsm">
                                                <div class="form-text">包含包装尺寸、重量等信息的文件</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 设置选项 -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="marketSelect" class="form-label">目标市场</label>
                                                <select class="form-select" id="marketSelect">
                                                    <option value="US">美国 (US)</option>
                                                    <option value="UK">英国 (UK)</option>
                                                </select>
                                                <div class="form-text">选择目标销售市场，影响默认值设置</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check mt-4">
                                                    <input class="form-check-input" type="checkbox" id="useProductInfo"
                                                        checked>
                                                    <label class="form-check-label" for="useProductInfo">
                                                        使用产品信息文件数据
                                                    </label>
                                                    <div class="form-text">启用后将使用产品信息文件中的包装数据</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button class="btn btn-primary" id="fillTemplateBtn" disabled>
                                            <i class="bi bi-gear"></i> 开始填充模板
                                        </button>
                                        <button class="btn btn-outline-secondary" id="clearTemplateBtn">
                                            <i class="bi bi-trash"></i> 清空选择
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试功能 -->
                                <div class="feature-card mt-4">
                                    <h5><i class="bi bi-file-text"></i> 测试报告文件</h5>
                                    <p class="text-muted">上传商品报告文件，测试数据结构和内容</p>

                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="file" class="form-control" id="testReportFile"
                                                accept=".xlsx,.xls,.xlsm">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-outline-primary w-100" id="testReportBtn" disabled>
                                                <i class="bi bi-search"></i> 测试报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 模板填充结果 -->
                                <div class="feature-card" id="templateResultCard" style="display: none;">
                                    <h5><i class="bi bi-check-circle"></i> 处理结果</h5>
                                    <div id="templateResults"></div>
                                    <div class="mt-3" id="templateDownloadSection" style="display: none;">
                                        <button class="btn btn-success w-100" id="downloadTemplateResult">
                                            <i class="bi bi-download"></i> 下载结果
                                        </button>
                                    </div>
                                </div>

                                <!-- 报告测试结果 -->
                                <div class="feature-card" id="reportTestCard" style="display: none;">
                                    <h5><i class="bi bi-clipboard-data"></i> 报告分析</h5>
                                    <div id="reportTestResults"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统测试面板 -->
                    <div class="tab-pane fade" id="test-panel" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="bi bi-wifi"></i>
                                    </div>
                                    <h5 class="text-center">API连接测试</h5>
                                    <p class="text-muted text-center">测试图床API是否正常工作</p>
                                    <button class="btn btn-primary w-100" id="testApiBtn">
                                        <i class="bi bi-play-circle"></i> 开始测试
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-card" id="testResultCard" style="display: none;">
                                    <h5><i class="bi bi-clipboard-data"></i> 测试结果</h5>
                                    <div id="testResults"></div>
                                </div>
                            </div>
                        </div>

                        <!-- API配置信息 -->
                        <div class="feature-card mt-4">
                            <h5><i class="bi bi-gear"></i> 当前配置</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>API地址:</strong> <span id="apiUrl">Loading...</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>API密钥:</strong> <span id="apiKey">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-3">处理中，请稍候...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 自定义JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <script>
        // 初始化应用
        let amazonUploader;

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('[HTML调试] DOMContentLoaded 事件触发');

            // 确保只初始化一次
            if (!window.amazonUploader) {
                console.log('[HTML调试] 首次初始化 AmazonImageUploader');
                window.amazonUploader = new AmazonImageUploader();
            } else {
                console.log('[HTML调试] AmazonImageUploader 已存在，跳过初始化');
            }

            // 加载配置信息
            loadConfig();
        });

        // 工具函数 - 保留一些必要的函数供其他功能使用
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = show ? 'flex' : 'none';
            }
        }

        function showError(message) {
            alert('错误: ' + message);
        }

        function showSuccess(message) {
            alert('成功: ' + message);
        }

        // 加载配置信息
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('apiUrl').textContent = result.config.api_url;
                    document.getElementById('apiKey').textContent = result.config.api_key;
                }
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        }

        // 在页面加载完成后加载配置
        document.addEventListener('DOMContentLoaded', function () {
            loadConfig();
            setupTemplateFeatures(); // 初始化模板填充功能
        });

        // 模板填充功能设置
        function setupTemplateFeatures() {
            const templateFile = document.getElementById('templateFile');
            const reportFile = document.getElementById('reportFile');
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');
            const clearTemplateBtn = document.getElementById('clearTemplateBtn');
            const testReportFile = document.getElementById('testReportFile');
            const testReportBtn = document.getElementById('testReportBtn');

            // 文件选择事件
            templateFile.addEventListener('change', updateTemplateBtnState);
            reportFile.addEventListener('change', updateTemplateBtnState);
            testReportFile.addEventListener('change', updateTestReportBtnState);

            // 按钮事件
            fillTemplateBtn.addEventListener('click', fillTemplate);
            clearTemplateBtn.addEventListener('click', clearTemplateFiles);
            testReportBtn.addEventListener('click', testReportFile);
        }

        function updateTemplateBtnState() {
            const templateFile = document.getElementById('templateFile');
            const reportFile = document.getElementById('reportFile');
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');

            fillTemplateBtn.disabled = !(templateFile.files.length > 0 && reportFile.files.length > 0);
        }

        function updateTestReportBtnState() {
            const testReportFile = document.getElementById('testReportFile');
            const testReportBtn = document.getElementById('testReportBtn');

            testReportBtn.disabled = testReportFile.files.length === 0;
        }

        function clearTemplateFiles() {
            document.getElementById('templateFile').value = '';
            document.getElementById('reportFile').value = '';
            document.getElementById('mappingFile').value = '';
            document.getElementById('productInfoFile').value = '';
            document.getElementById('marketSelect').value = 'US';
            document.getElementById('useProductInfo').checked = true;
            document.getElementById('templateResultCard').style.display = 'none';
            updateTemplateBtnState();
        }

        async function fillTemplate() {
            const templateFile = document.getElementById('templateFile').files[0];
            const reportFile = document.getElementById('reportFile').files[0];
            const mappingFile = document.getElementById('mappingFile').files[0];
            const productInfoFile = document.getElementById('productInfoFile').files[0];
            const market = document.getElementById('marketSelect').value;
            const useProductInfo = document.getElementById('useProductInfo').checked;

            if (!templateFile || !reportFile) {
                showError('请选择模板文件和商品分类报告文件');
                return;
            }

            showLoading(true);

            const formData = new FormData();
            formData.append('template_file', templateFile);
            formData.append('report_file', reportFile);

            // 添加可选文件
            if (mappingFile) {
                formData.append('mapping_file', mappingFile);
            }
            if (productInfoFile) {
                formData.append('product_info_file', productInfoFile);
            }

            // 添加选项参数
            formData.append('market', market);
            formData.append('use_product_info', useProductInfo ? 'true' : 'false');

            try {
                const response = await fetch('/api/fill-template', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showTemplateResults(result);
                    showSuccess('模板填充处理完成');
                } else {
                    showError('模板填充失败: ' + result.error);
                }
            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        async function testReportFile() {
            const reportFile = document.getElementById('testReportFile').files[0];

            if (!reportFile) {
                showError('请选择报告文件');
                return;
            }

            showLoading(true);

            const formData = new FormData();
            formData.append('report_file', reportFile);

            try {
                const response = await fetch('/api/test-report', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showReportTestResults(result.test_results);
                    showSuccess('报告文件分析完成');
                } else {
                    showError('报告测试失败: ' + result.error);
                }
            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function showTemplateResults(result) {
            const templateResultCard = document.getElementById('templateResultCard');
            const templateResults = document.getElementById('templateResults');

            templateResultCard.style.display = 'block';

            let resultHtml = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> 处理完成</h6>
                    <p class="mb-0">${result.message}</p>
                </div>
            `;

            // 显示处理统计信息
            if (result.stats) {
                resultHtml += `
                    <div class="alert alert-info">
                        <h6><i class="bi bi-bar-chart"></i> 处理统计</h6>
                        <ul class="mb-0">
                            <li>处理行数: ${result.stats.processed_rows || 0}</li>
                            <li>匹配行数: ${result.stats.matched_rows || 0}</li>
                            <li>空值行数: ${result.stats.empty_rows || 0}</li>
                        </ul>
                    </div>
                `;
            }

            templateResults.innerHTML = resultHtml;

            if (result.download_url) {
                const downloadSection = document.getElementById('templateDownloadSection');
                const downloadBtn = document.getElementById('downloadTemplateResult');

                downloadSection.style.display = 'block';
                downloadBtn.onclick = () => {
                    window.open(result.download_url, '_blank');
                };
            }
        }

        function showReportTestResults(testResults) {
            const reportTestCard = document.getElementById('reportTestCard');
            const reportTestResults = document.getElementById('reportTestResults');

            reportTestCard.style.display = 'block';

            let html = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> 文件分析结果</h6>
                    <p><strong>文件名:</strong> ${testResults.file_name}</p>
                    <p><strong>工作表数量:</strong> ${testResults.total_sheets}</p>
                </div>
            `;

            if (testResults.sheet_names && testResults.sheet_names.length > 0) {
                html += '<div class="mt-3"><h6>工作表详情:</h6>';

                testResults.sheet_names.forEach(sheetName => {
                    const sheetData = testResults.sheets_data[sheetName];
                    if (sheetData) {
                        html += `
                            <div class="border rounded p-2 mb-2">
                                <strong>${sheetName}</strong><br>
                        `;

                        if (sheetData.error) {
                            html += `<small class="text-danger">错误: ${sheetData.error}</small>`;
                        } else {
                            html += `
                                <small class="text-muted">
                                    行数: ${sheetData.rows} | 列数: ${sheetData.columns}<br>
                                    列名: ${sheetData.column_names ? sheetData.column_names.join(', ') : '无'}
                                </small>
                            `;
                        }

                        html += '</div>';
                    }
                });

                html += '</div>';
            }

            reportTestResults.innerHTML = html;
        }
    </script>
</body>

</html>