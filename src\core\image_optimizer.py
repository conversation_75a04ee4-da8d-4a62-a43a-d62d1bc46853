# -*- coding: utf-8 -*-
"""
图片优化模块
提供图片压缩、格式转换、尺寸调整等功能
"""

import os
import io
from PIL import Image
import logging

# 设置日志
logger = logging.getLogger(__name__)

class ImageOptimizer:
    """图片优化处理器"""
    
    def __init__(self, quality=90, max_size=2000, enable_optimization=False):
        """
        初始化图片优化器
        
        参数:
            quality (int): 压缩质量（1-100）
            max_size (int): 最大尺寸（像素）
            enable_optimization (bool): 是否启用优化
        """
        self.quality = quality
        self.max_size = max_size
        self.enable_optimization = enable_optimization
    
    def optimize_image(self, file_path, quality=None, max_size=None):
        """
        优化图片大小，保证图片质量，返回优化后的图片数据
        
        参数:
            file_path (str): 原始图片路径
            quality (int, optional): 压缩质量，使用默认值如果未指定
            max_size (int, optional): 最大尺寸，使用默认值如果未指定
        
        返回:
            bytes: 优化后的图片数据
        """
        # 如果未启用优化，直接返回原图
        if not self.enable_optimization:
            with open(file_path, 'rb') as f:
                return f.read()
        
        # 使用传入的参数或默认参数
        quality = quality or self.quality
        max_size = max_size or self.max_size
        
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 跳过非图片文件或GIF动画
        if ext not in ['.jpg', '.jpeg', '.png', '.webp'] or ext == '.gif':
            with open(file_path, 'rb') as f:
                return f.read()
        
        try:
            # 打开图片
            img = Image.open(file_path)
            
            # 检查图片是否需要调整大小
            needs_resize = False
            if max_size and (img.width > max_size or img.height > max_size):
                needs_resize = True
                if img.width > img.height:
                    new_width = max_size
                    new_height = int(img.height * (max_size / img.width))
                else:
                    new_height = max_size
                    new_width = int(img.width * (max_size / img.height))
            
            # 检查文件大小
            original_size = os.path.getsize(file_path)
            original_too_large = original_size > 2 * 1024 * 1024  # 超过2MB
            
            # 如果图片不需要调整大小且大小适中，则使用原图
            if not needs_resize and not original_too_large:
                with open(file_path, 'rb') as f:
                    return f.read()
            
            # 调整尺寸（如果需要）
            if needs_resize:
                img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 保存到内存
            output = io.BytesIO()
            
            # 根据文件类型选择保存格式和质量
            if ext in ['.jpg', '.jpeg']:
                # JPEG使用较高质量
                save_quality = quality
                img.save(output, format='JPEG', quality=save_quality, optimize=True)
            elif ext == '.png':
                # PNG使用无损压缩
                img.save(output, format='PNG', optimize=True)
            elif ext == '.webp':
                # WebP使用较高质量
                save_quality = quality
                img.save(output, format='WEBP', quality=save_quality)
            else:
                # 其他格式
                img.save(output, format=img.format, quality=quality)
            
            # 获取优化后的图片数据
            optimized_data = output.getvalue()
            
            # 如果优化后比原图还大，则使用原图
            if len(optimized_data) >= original_size:
                with open(file_path, 'rb') as f:
                    return f.read()
            
            # 打印优化信息
            optimized_size = len(optimized_data)
            save_percent = (1 - optimized_size / original_size) * 100
            if save_percent > 5:  # 如果节省超过5%的空间
                logger.info(f"优化图片 {os.path.basename(file_path)}: "
                          f"{original_size/1024:.1f}KB → {optimized_size/1024:.1f}KB "
                          f"(节省 {save_percent:.1f}%)")
            
            return optimized_data
            
        except Exception as e:
            logger.warning(f"图片优化失败: {e}，使用原始图片")
            with open(file_path, 'rb') as f:
                return f.read()
    
    def get_image_info(self, file_path):
        """
        获取图片信息
        
        参数:
            file_path (str): 图片文件路径
            
        返回:
            dict: 图片信息字典
        """
        try:
            with Image.open(file_path) as img:
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'file_size': os.path.getsize(file_path)
                }
        except Exception as e:
            logger.error(f"获取图片信息失败: {e}")
            return None
    
    def validate_image(self, file_path):
        """
        验证图片文件是否有效
        
        参数:
            file_path (str): 图片文件路径
            
        返回:
            bool: 是否为有效图片
        """
        try:
            with Image.open(file_path) as img:
                img.verify()  # 验证图片完整性
            return True
        except Exception:
            return False
    
    def convert_format(self, file_path, target_format='JPEG', quality=None):
        """
        转换图片格式
        
        参数:
            file_path (str): 原始图片路径
            target_format (str): 目标格式 (JPEG, PNG, WEBP等)
            quality (int, optional): 压缩质量
            
        返回:
            bytes: 转换后的图片数据
        """
        quality = quality or self.quality
        
        try:
            with Image.open(file_path) as img:
                # 如果是RGBA模式转换为JPEG，需要先转换为RGB
                if target_format.upper() == 'JPEG' and img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                output = io.BytesIO()
                
                if target_format.upper() == 'JPEG':
                    img.save(output, format='JPEG', quality=quality, optimize=True)
                elif target_format.upper() == 'PNG':
                    img.save(output, format='PNG', optimize=True)
                elif target_format.upper() == 'WEBP':
                    img.save(output, format='WEBP', quality=quality)
                else:
                    img.save(output, format=target_format.upper())
                
                return output.getvalue()
                
        except Exception as e:
            logger.error(f"格式转换失败: {e}")
            # 如果转换失败，返回原始文件数据
            with open(file_path, 'rb') as f:
                return f.read()

# 创建默认的图片优化器实例
default_optimizer = ImageOptimizer()

# 兼容性函数 - 为了保持与旧版本Web应用的兼容性
def optimize_image(image_path, output_path=None, quality=85, max_size=(1600, 1600)):
    """
    优化图片的兼容性函数
    
    参数:
        image_path (str): 输入图片路径
        output_path (str, optional): 输出路径
        quality (int): 压缩质量
        max_size (tuple): 最大尺寸
        
    返回:
        bytes: 优化后的图片数据
    """
    # 获取最大尺寸的数值（取较大的一个）
    if isinstance(max_size, tuple) and len(max_size) >= 2:
        max_size_value = max(max_size[0], max_size[1])
    elif isinstance(max_size, (int, float)):
        max_size_value = max_size
    else:
        max_size_value = 1600
    
    return default_optimizer.optimize_image(image_path, quality=quality, max_size=max_size_value) 