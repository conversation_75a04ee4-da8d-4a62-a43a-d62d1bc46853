#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - 统一配置管理
集中管理所有配置项，自动创建必要目录
"""

import os
import sys
from pathlib import Path

class Config:
    """配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        # 项目根目录
        self.PROJECT_ROOT = self._get_project_root()
        
        # 目录配置
        self.DATA_DIR = os.path.join(self.PROJECT_ROOT, 'data')
        self.OUTPUT_DIR = os.path.join(self.PROJECT_ROOT, 'output')
        self.CONFIG_DIR = os.path.join(self.PROJECT_ROOT, 'config')
        
        # 数据子目录
        self.UPLOADS_DIR = os.path.join(self.DATA_DIR, 'uploads')
        self.SAMPLES_DIR = os.path.join(self.DATA_DIR, 'samples')
        self.CACHE_DIR = os.path.join(self.DATA_DIR, 'cache')
        
        # 输出子目录
        self.RESULTS_DIR = os.path.join(self.OUTPUT_DIR, 'results')
        self.LOGS_DIR = os.path.join(self.OUTPUT_DIR, 'logs')
        self.TEMP_DIR = os.path.join(self.OUTPUT_DIR, 'temp')
        
        # Web应用配置
        self.WEB_TEMPLATES_DIR = os.path.join(self.PROJECT_ROOT, 'src', 'web', 'templates')
        self.WEB_STATIC_DIR = os.path.join(self.PROJECT_ROOT, 'src', 'web', 'static')
        
        # 应用配置
        self.MAX_CONTENT_LENGTH = 2 * 1024 * 1024 * 1024  # 2GB
        self.SECRET_KEY = 'amazon-image-uploader-web-2024'
        
        # 上传配置
        self.DEFAULT_MAX_WORKERS = 10
        self.DEFAULT_UPLOAD_DELAY = 0.1
        self.ENABLE_IMAGE_OPTIMIZATION = True
        self.MAX_RETRIES = 3
        
        # 支持的文件格式
        self.ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        
        # 自动创建目录
        self._ensure_directories()
        
        # 加载API配置
        self._load_api_config()
    
    def _get_project_root(self):
        """获取项目根目录"""
        # 从当前文件位置向上查找项目根目录
        current_file = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_file)
        
        # 向上查找包含requirements.txt的目录
        while current_dir != os.path.dirname(current_dir):
            if os.path.exists(os.path.join(current_dir, 'requirements.txt')):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        
        # 如果找不到，使用当前文件的上上级目录
        return os.path.dirname(os.path.dirname(current_dir))
    
    def _ensure_directories(self):
        """确保所有必要目录存在"""
        directories = [
            self.DATA_DIR, self.OUTPUT_DIR,
            self.UPLOADS_DIR, self.SAMPLES_DIR, self.CACHE_DIR,
            self.RESULTS_DIR, self.LOGS_DIR, self.TEMP_DIR
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
            except Exception as e:
                print(f"⚠️ 创建目录失败 {directory}: {e}")
    
    def _load_api_config(self):
        """加载API配置"""
        try:
            # 添加config目录到路径
            if self.CONFIG_DIR not in sys.path:
                sys.path.insert(0, self.CONFIG_DIR)
            
            # 尝试导入API配置
            try:
                from mohe_config import API_KEY, API_URL, FILE_PARAM_NAME, UPLOAD_MODE, UPLOAD_FORMAT
                self.API_KEY = API_KEY
                self.API_URL = API_URL
                self.FILE_PARAM_NAME = FILE_PARAM_NAME
                self.UPLOAD_MODE = UPLOAD_MODE
                self.UPLOAD_FORMAT = UPLOAD_FORMAT
                print("✅ API配置加载成功")
                
            except ImportError:
                # 使用默认配置
                print("⚠️ 未找到API配置文件，使用默认配置")
                self.API_KEY = ""
                self.API_URL = "https://img.mohecdn.com/upload"
                self.FILE_PARAM_NAME = "file"
                self.UPLOAD_MODE = "cdn"
                self.UPLOAD_FORMAT = "json"
                
        except Exception as e:
            print(f"❌ 加载API配置失败: {e}")
            # 设置默认值
            self.API_KEY = ""
            self.API_URL = "https://img.mohecdn.com/upload"
            self.FILE_PARAM_NAME = "file"
            self.UPLOAD_MODE = "cdn"
            self.UPLOAD_FORMAT = "json"
    
    def get_history_folder(self):
        """获取历史文件夹路径（兼容性方法）"""
        return self.RESULTS_DIR
    
    def get_upload_folder(self):
        """获取上传文件夹路径"""
        return self.UPLOADS_DIR
    
    def get_temp_folder(self):
        """获取临时文件夹路径"""
        return self.TEMP_DIR

# 创建全局配置实例
config = Config()

# 为了向后兼容，导出常用配置
PROJECT_ROOT = config.PROJECT_ROOT
UPLOAD_FOLDER = config.UPLOADS_DIR
TEMP_FOLDER = config.TEMP_DIR
HISTORY_FOLDER = config.RESULTS_DIR

# API配置
API_KEY = config.API_KEY
API_URL = config.API_URL
FILE_PARAM_NAME = config.FILE_PARAM_NAME
UPLOAD_MODE = config.UPLOAD_MODE
UPLOAD_FORMAT = config.UPLOAD_FORMAT

# 应用配置
MAX_CONTENT_LENGTH = config.MAX_CONTENT_LENGTH
SECRET_KEY = config.SECRET_KEY
ALLOWED_EXTENSIONS = config.ALLOWED_EXTENSIONS

# 上传配置
DEFAULT_MAX_WORKERS = config.DEFAULT_MAX_WORKERS
DEFAULT_UPLOAD_DELAY = config.DEFAULT_UPLOAD_DELAY
ENABLE_IMAGE_OPTIMIZATION = config.ENABLE_IMAGE_OPTIMIZATION
MAX_RETRIES = config.MAX_RETRIES

def ensure_history_dir():
    """确保历史目录存在（兼容性函数）"""
    os.makedirs(config.RESULTS_DIR, exist_ok=True)
    return config.RESULTS_DIR

if __name__ == '__main__':
    print("🔧 配置管理模块测试")
    print(f"项目根目录: {config.PROJECT_ROOT}")
    print(f"数据目录: {config.DATA_DIR}")
    print(f"输出目录: {config.OUTPUT_DIR}")
    print(f"结果目录: {config.RESULTS_DIR}")
    print(f"API URL: {config.API_URL}")
    print("✅ 配置加载完成") 