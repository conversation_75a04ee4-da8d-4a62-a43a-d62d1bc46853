# 亚马逊图片上传图床工具 - Web服务启动脚本
# 用于快速启动Web服务

# 设置编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 显示欢迎信息
Write-Host "`n===========================================" -ForegroundColor Cyan
Write-Host "  亚马逊图片上传图床工具 - Web服务启动器" -ForegroundColor Cyan
Write-Host "=========================================== `n" -ForegroundColor Cyan

# 检查Python环境
Write-Host "🔍 检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version
    Write-Host "✅ 已找到Python: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ 未找到Python，请确保已安装Python并添加到PATH环境变量" -ForegroundColor Red
    Write-Host "   您可以从 https://www.python.org/downloads/ 下载安装Python" -ForegroundColor Red
    pause
    exit
}

# 检查依赖
Write-Host "`n🔍 检查项目依赖..." -ForegroundColor Yellow
if (!(Test-Path -Path "requirements.txt")) {
    Write-Host "⚠️ 未找到requirements.txt文件，跳过依赖检查" -ForegroundColor Yellow
}
else {
    Write-Host "正在安装依赖，请稍候..." -ForegroundColor Yellow
    python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ 部分依赖可能安装失败，但将继续尝试启动服务" -ForegroundColor Yellow
    }
    else {
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
    }
}

# 检查配置文件
Write-Host "`n🔍 检查配置文件..." -ForegroundColor Yellow
if (!(Test-Path -Path "mohe_config.py")) {
    if (Test-Path -Path "mohe_config.example.py") {
        Write-Host "未找到配置文件，正在从示例创建..." -ForegroundColor Yellow
        Copy-Item -Path "mohe_config.example.py" -Destination "mohe_config.py"
        Write-Host "✅ 已创建配置文件 mohe_config.py" -ForegroundColor Green
        Write-Host "   请根据需要修改配置文件中的API密钥等信息" -ForegroundColor Yellow
    }
    else {
        Write-Host "⚠️ 未找到配置文件和示例文件，服务可能无法正常工作" -ForegroundColor Red
    }
}
else {
    Write-Host "✅ 已找到配置文件 mohe_config.py" -ForegroundColor Green
}

# 确保必要的目录存在
Write-Host "`n🔍 检查必要目录..." -ForegroundColor Yellow
$directories = @("uploads", "temp", "历史映射表", "填充后的模板")
foreach ($dir in $directories) {
    if (!(Test-Path -Path $dir)) {
        New-Item -ItemType Directory -Path $dir | Out-Null
        Write-Host "✅ 已创建目录: $dir" -ForegroundColor Green
    }
}

# 启动Web服务
Write-Host "`n🚀 正在启动Web服务..." -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "  服务启动后，请访问: http://localhost:5000" -ForegroundColor Cyan
Write-Host "  按Ctrl+C可以停止服务" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "`n"

# 启动浏览器
Start-Process "http://localhost:5000"

# 启动Python Web服务
python web_app.py 