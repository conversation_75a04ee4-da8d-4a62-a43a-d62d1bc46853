#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Excel模板填充Web工具
专门用于填充亚马逊Excel模板文件的Web应用程序
"""

import os
import sys
import pandas as pd
import time
import traceback
import tempfile
import shutil
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import threading
from openpyxl import load_workbook
from collections import Counter
import xlwings as xw

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'excel-template-filler-2024'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB最大文件大小

# 启用CORS
CORS(app)

# 配置文件夹
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = '填充后的模板'
TEMP_FOLDER = 'temp'

# 确保目录存在
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, TEMP_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# 全局变量存储处理进度
processing_progress = {}
processing_lock = threading.Lock()

def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xlsm', 'xls'}

def update_progress(task_id, stage, progress, message="", error=None):
    """更新处理进度"""
    with processing_lock:
        processing_progress[task_id] = {
            'stage': stage,
            'progress': progress,
            'message': message,
            'error': error,
            'timestamp': time.time()
        }

def fill_excel_template(template_path, report_path, output_path, task_id, max_rows=300):
    """
    填充Excel模板文件
    
    Args:
        template_path: 模板文件路径
        report_path: 商品报告文件路径  
        output_path: 输出文件路径
        task_id: 任务ID用于进度跟踪
        max_rows: 最大处理行数，默认300
    """
    try:
        update_progress(task_id, "初始化", 0, "开始处理模板填充...")
        
        # 步骤1：读取商品分类报告
        update_progress(task_id, "读取报告", 10, "正在读取商品分类报告...")
        print(f"📋 读取商品分类报告: {report_path}")
        
        # 按照您的需求：固定读取Template工作表第3行作为表头
        excel_file = pd.ExcelFile(report_path, engine='openpyxl')
        sheet_names = excel_file.sheet_names
        print(f"📋 工作表列表：{sheet_names}")
        
        # 检查是否包含Template工作表
        if 'Template' not in sheet_names:
            raise Exception(f"商品报告文件中未找到'Template'工作表。可用工作表：{sheet_names}")
        
        # 固定读取Template工作表，第3行作为表头（header=2）
        print(f"🔍 读取Template工作表，第3行作为表头...")
        try:
            report_df = pd.read_excel(report_path, sheet_name='Template', header=2, engine='openpyxl')
            print(f"✅ 成功读取：列数={len(report_df.columns)}, 行数={len(report_df)}")
            print(f"📊 前10个列名：{list(report_df.columns)[:10]}")
            
            # 限制读取前300行数据
            if len(report_df) > max_rows:
                print(f"⚠️ 数据行数({len(report_df)})超过限制，只处理前{max_rows}行")
                report_df = report_df.head(max_rows)
            
            used_header = 2
            used_sheet = 'Template'
            
        except Exception as e:
            raise Exception(f"读取Template工作表失败：{str(e)}")
        
        if len(report_df.columns) == 0 or len(report_df) == 0:
            raise Exception("Template工作表没有有效数据")
        
        update_progress(task_id, "处理数据", 20, f"成功读取报告，共{len(report_df)}行数据")
        
        # 数据有效性检查
        valid_rows = 0
        for col in report_df.columns:
            if not pd.isna(col) and not str(col).startswith('Unnamed'):
                non_null_count = report_df[col].notna().sum()
                if non_null_count > valid_rows:
                    valid_rows = non_null_count
        
        print(f"📈 有效数据行数：{valid_rows}")
        
        if valid_rows == 0:
            raise Exception("Template工作表中没有有效的数据行")
        
        # 步骤2：读取模板文件结构
        update_progress(task_id, "分析模板", 30, "正在分析模板文件结构...")
        print(f"📄 分析模板文件: {template_path}")
        
        # 创建临时文件副本
        temp_dir = tempfile.mkdtemp()
        temp_template = os.path.join(temp_dir, f"temp_template_{int(time.time())}.xlsm")
        shutil.copy2(template_path, temp_template)
        
        # 分析工作表结构
        excel_file = pd.ExcelFile(temp_template, engine='openpyxl')
        sheet_names = excel_file.sheet_names
        print(f"📋 工作表列表：{sheet_names}")
        
        # 确定Template工作表
        if 'Template' not in sheet_names:
            raise Exception(f"模板文件中未找到'Template'工作表。可用工作表：{sheet_names}")
        
        template_sheet_name = 'Template'
        
        # 读取模板表头（第3行）
        print(f"📊 读取Template工作表第3行表头...")
        template_header_df = pd.read_excel(temp_template, sheet_name=template_sheet_name, 
                                         engine='openpyxl', header=2, nrows=0)
        template_headers = template_header_df.columns.tolist()
        print(f"📊 模板表头包含 {len(template_headers)} 列")
        
        # 验证模板表头
        if not template_headers or all(pd.isna(col) or str(col).startswith('Unnamed') for col in template_headers[:5]):
            raise Exception("模板文件Template工作表第3行未包含有效的列名")
        
        # 步骤3：数据匹配与合并
        update_progress(task_id, "匹配数据", 50, "正在进行数据匹配...")
        
        print(f"📊 准备填充 {len(report_df)} 行数据到模板")
        
        # 字段映射 - 精确匹配
        print("🔄 开始字段映射...")
        matched_columns = {}
        report_columns = list(report_df.columns)
        
        for template_col in template_headers:
            if pd.isna(template_col) or str(template_col).startswith('Unnamed'):
                continue
                
            template_col_str = str(template_col).strip()
            
            # 精确匹配（区分大小写）
            if template_col_str in report_columns:
                matched_columns[template_col_str] = template_col_str
                print(f"✅ 精确匹配: {template_col_str}")
            else:
                # 左右两侧匹配
                found_match = False
                for report_col in report_columns:
                    report_col_str = str(report_col).strip()
                    if (template_col_str in report_col_str or 
                        report_col_str in template_col_str):
                        matched_columns[template_col_str] = report_col_str
                        print(f"✅ 部分匹配: {template_col_str} -> {report_col_str}")
                        found_match = True
                        break
                
                if not found_match:
                    print(f"❌ 未匹配: {template_col_str}")
        
        print(f"📊 成功匹配 {len(matched_columns)} 个字段")
        
        # 步骤4：使用xlwings高性能批量填充数据
        update_progress(task_id, "填充数据", 70, "🚀 使用xlwings高性能批量填充数据...")
        
        print("🚀 开始xlwings高性能批量填充数据...")
        
        # 预处理数据为批量格式
        print(f"📊 准备批量数据：{len(report_df)}行 × {len(template_headers)}列")
        
        try:
            update_progress(task_id, "填充数据", 75, "🚀 准备xlwings批量写入...")
            
            # 快速数据准备：使用pandas的向量化操作
            print("🔄 使用pandas向量化操作准备数据...")
            
            # 创建空的结果DataFrame
            result_data = {}
            for template_col in template_headers:
                template_col_str = str(template_col).strip()
                
                if template_col_str in matched_columns:
                    report_col = matched_columns[template_col_str]
                    result_data[template_col_str] = report_df[report_col].fillna('').astype(str)
                else:
                    result_data[template_col_str] = [''] * len(report_df)
            
            matched_df = pd.DataFrame(result_data)
            print(f"✅ 向量化数据准备完成：{matched_df.shape}")
            
            update_progress(task_id, "填充数据", 80, f"🚀 开始xlwings写入{len(matched_df)}行数据...")
            
            # 使用xlwings打开Excel文件（速度更快）
            print("📂 使用xlwings打开Excel文件...")
            
            # 确保Excel应用程序不可见，提升性能
            app_xl = xw.App(visible=False, add_book=False)
            app_xl.display_alerts = False
            app_xl.screen_updating = False
            
            try:
                # 打开工作簿
                wb = app_xl.books.open(temp_template)
                ws = wb.sheets[template_sheet_name]
                
                # 计算写入范围
                start_row = 4
                num_rows = len(matched_df)
                num_cols = len(template_headers)
                
                # 准备数据矩阵
                data_matrix = matched_df.values.tolist()
                
                print(f"📝 xlwings批量写入范围：{start_row}行开始，{num_rows}行 × {num_cols}列")
                
                # xlwings超高性能：一次性写入整个数据范围
                update_progress(task_id, "填充数据", 85, "⚡ xlwings一次性批量写入...")
                
                # 先清除现有数据
                if ws.cells.last_cell.row >= start_row:
                    clear_range = f"A{start_row}:{ws.cells.last_cell.address.split('$')[1]}{ws.cells.last_cell.row}"
                    ws.range(clear_range).clear_contents()
                
                # 一次性写入所有数据（这是xlwings的强项）
                if data_matrix:
                    target_range = f"A{start_row}"
                    ws.range(target_range).value = data_matrix
                
                print(f"✅ xlwings一次性批量写入完成！写入了 {num_rows} 行数据")
                
                # 保存文件
                update_progress(task_id, "保存文件", 90, "正在保存填充后的文件...")
                
                # 生成输出文件名
                base_name = os.path.splitext(os.path.basename(template_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"{base_name}_已填充_{timestamp}.xlsm"
                final_output_path = os.path.join(OUTPUT_FOLDER, output_filename)
                
                # 保存并关闭
                wb.save(final_output_path)
                wb.close()
                
            finally:
                # 确保关闭Excel应用程序
                app_xl.quit()
            
        except Exception as e:
            error_msg = f"xlwings批量填充失败：{str(e)}"
            print(f"❌ {error_msg}")
            print(f"详细错误：{traceback.format_exc()}")
            update_progress(task_id, "错误", 0, error_msg, str(e))
            return False, None, 0, 0
        
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
        
        update_progress(task_id, "完成", 100, 
                       f"成功填充 {num_rows} 行数据，文件已保存：{output_filename}")
        
        return True, final_output_path, num_rows, len(matched_columns)
        
    except Exception as e:
        error_msg = f"处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        print(f"详细错误：{traceback.format_exc()}")
        update_progress(task_id, "错误", 0, error_msg, str(e))
        return False, None, 0, 0

@app.route('/')
def index():
    """主页"""
    return render_template('template_filler.html')

@app.route('/favicon.ico')
def favicon():
    """返回favicon.ico"""
    return '', 204  # 返回空内容，状态码204 No Content

@app.route('/api/fill-template', methods=['POST'])
def api_fill_template():
    """填充模板API"""
    try:
        # 生成任务ID
        task_id = f"task_{int(time.time())}_{hash(str(time.time())) % 10000}"
        
        # 获取参数
        max_rows = int(request.form.get('max_rows', 300))
        use_custom_paths = request.form.get('use_custom_paths') == 'true'
        
        print(f"[调试] use_custom_paths: {use_custom_paths}")
        print(f"[调试] max_rows: {max_rows}")
        
        # 处理文件路径
        if use_custom_paths:
            # 使用自定义路径模式
            template_path = request.form.get('template_path', '').strip()
            report_path = request.form.get('report_path', '').strip()
            
            print(f"[调试] template_path: {template_path}")
            print(f"[调试] report_path: {report_path}")
            
            if not template_path or not report_path:
                return jsonify({'success': False, 'error': '请提供有效的文件路径'}), 400
            
            if not os.path.exists(template_path):
                return jsonify({'success': False, 'error': f'模板文件不存在：{template_path}'}), 400
            
            if not os.path.exists(report_path):
                return jsonify({'success': False, 'error': f'报告文件不存在：{report_path}'}), 400
        else:
            # 上传文件模式
            if 'template_file' not in request.files or 'report_file' not in request.files:
                return jsonify({'success': False, 'error': '请选择模板文件和商品报告文件'}), 400
            
            template_file = request.files['template_file']
            report_file = request.files['report_file']
            
            if template_file.filename == '' or report_file.filename == '':
                return jsonify({'success': False, 'error': '请选择有效文件'}), 400
            
            # 保存上传的文件
            template_filename = secure_filename(template_file.filename)
            report_filename = secure_filename(report_file.filename)
            
            template_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{template_filename}")
            report_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{report_filename}")
            
            template_file.save(template_path)
            report_file.save(report_path)
        
        # 启动后台处理任务
        def process_task():
            try:
                success, output_path, filled_rows, matched_cols = fill_excel_template(
                    template_path, report_path, None, task_id, max_rows
                )
                
                # 清理上传的临时文件
                if not use_custom_paths:
                    try:
                        if os.path.exists(template_path):
                            os.unlink(template_path)
                        if os.path.exists(report_path):
                            os.unlink(report_path)
                    except:
                        pass
                
            except Exception as e:
                update_progress(task_id, "错误", 0, f"处理异常：{str(e)}", str(e))
        
        # 启动后台线程
        thread = threading.Thread(target=process_task)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '任务已启动，正在处理中...'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'启动任务失败：{str(e)}'
        }), 500

@app.route('/api/progress/<task_id>')
def api_progress(task_id):
    """获取处理进度"""
    with processing_lock:
        progress_info = processing_progress.get(task_id)
    
    if not progress_info:
        return jsonify({'success': False, 'error': '任务不存在'}), 404
    
    return jsonify({
        'success': True,
        'stage': progress_info['stage'],
        'progress': progress_info['progress'],
        'message': progress_info['message'],
        'error': progress_info.get('error'),
        'completed': progress_info['stage'] in ['完成', '错误']
    })

@app.route('/api/download/<filename>')
def api_download(filename):
    """下载填充后的文件"""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
        
        return send_file(file_path, as_attachment=True, download_name=filename)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/output-files')
def api_output_files():
    """获取输出文件列表"""
    try:
        files = []
        if os.path.exists(OUTPUT_FOLDER):
            for filename in os.listdir(OUTPUT_FOLDER):
                if filename.endswith(('.xlsx', '.xlsm')):
                    file_path = os.path.join(OUTPUT_FOLDER, filename)
                    file_stat = os.stat(file_path)
                    files.append({
                        'name': filename,
                        'size': file_stat.st_size,
                        'modified': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    })
        
        # 按修改时间降序排列
        files.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({'success': True, 'files': files})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动Excel模板填充Web工具...")
    print("📋 功能特点：")
    print("   - 智能读取Excel模板和商品报告")
    print("   - 精确字段匹配（区分大小写）")
    print("   - 批量高性能数据填充")
    print("   - 仅处理前300行数据")
    print("   - 保持原有格式和公式")
    print("   - 实时进度显示")
    print("="*50)
    
    # 关闭debug模式，避免长时间任务被中断
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)