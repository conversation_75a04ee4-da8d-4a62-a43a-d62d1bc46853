@echo off
chcp 65001 >nul
title 亚马逊图片上传图床工具 - Web服务

echo.
echo ==========================================
echo   亚马逊图片上传图床工具 - Web服务启动器
echo ==========================================
echo.

:: 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请确保已安装Python并添加到PATH环境变量
    echo    您可以从 https://www.python.org/downloads/ 下载安装Python
    pause
    exit /b 1
)

echo ✅ 已找到Python环境

:: 检查依赖
echo.
echo 🔍 检查项目依赖...
if not exist requirements.txt (
    echo ⚠️ 未找到requirements.txt文件，跳过依赖检查
) else (
    echo 正在安装依赖，请稍候...
    python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    if %errorlevel% neq 0 (
        echo ⚠️ 部分依赖可能安装失败，但将继续尝试启动服务
    ) else (
        echo ✅ 依赖安装完成
    )
)

:: 检查配置文件
echo.
echo 🔍 检查配置文件...
if not exist mohe_config.py (
    if exist mohe_config.example.py (
        echo 未找到配置文件，正在从示例创建...
        copy mohe_config.example.py mohe_config.py >nul
        echo ✅ 已创建配置文件 mohe_config.py
        echo    请根据需要修改配置文件中的API密钥等信息
    ) else (
        echo ⚠️ 未找到配置文件和示例文件，服务可能无法正常工作
    )
) else (
    echo ✅ 已找到配置文件 mohe_config.py
)

:: 确保必要的目录存在
echo.
echo 🔍 检查必要目录...
if not exist uploads mkdir uploads
if not exist temp mkdir temp
if not exist 历史映射表 mkdir 历史映射表
if not exist 填充后的模板 mkdir 填充后的模板
echo ✅ 已确认必要目录存在

:: 启动Web服务
echo.
echo 🚀 正在启动Web服务...
echo ===========================================
echo   服务启动后，请访问: http://localhost:5000
echo   按Ctrl+C可以停止服务
echo ===========================================
echo.

:: 启动浏览器
start http://localhost:5000

:: 启动Python Web服务
python web_app.py

:: 如果Python服务退出，显示提示
echo.
echo Web服务已停止运行
pause 