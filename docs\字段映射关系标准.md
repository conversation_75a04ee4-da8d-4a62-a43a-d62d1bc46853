# 亚马逊图片上传图床工具 - 字段映射关系标准

> **⚠️ 重要说明**：本文档定义了项目中所有文件之间的标准字段映射关系，请勿随意修改！
> **🔄 最新更新**：适配亚马逊2025年商品分类报告格式变化

## 📋 核心映射关系

### 🔗 主要关联关系

```
图片映射表.MSKU = 商品分类报告.SKU = 亚马逊模版.item_sku
图片映射表.SKU = 产品资料表.*SKU（必填）
```

### 📊 详细文件结构和字段说明

#### 1️⃣ **图片映射表**
- **文件位置**：用户上传的Excel文件
- **工作表**：第一个工作表
- **表头位置**：第1行
- **关键字段**：
  - `MSKU`：用于关联商品分类报告和亚马逊模板
  - `SKU`：用于关联产品资料表
  - 图片URL字段：`main_image_url`, `other_image_url1-8`, `swatch_image_url`

#### 2️⃣ **商品分类报告** ⭐ 最新格式
- **文件位置**：用户上传的Excel文件
- **工作表**：`Template`
- **表头位置**：第4行（第2行为价格字段表头，第3行被移除）
- **关键字段**：
  - `SKU`：用于关联图片映射表和亚马逊模板（原字段名为item_sku）

#### 3️⃣ **亚马逊模板**
- **文件位置**：用户上传的Excel文件
- **工作表**：`Template`
- **表头位置**：第3行
- **关键字段**：
  - `item_sku`：主键字段，用于数据填充

#### 4️⃣ **产品资料表**
- **文件位置**：用户上传的Excel文件
- **工作表**：`单个产品`
- **表头位置**：第1行
- **关键字段**：
  - `*SKU（必填）`：用于关联图片映射表的SKU字段
  - `SKC`：用于包装信息聚合
  - 包装字段：`包装规格长`, `包装规格宽`, `包装规格高`, `单品毛重`

## 🔄 数据流向和处理逻辑

### 📈 填充流程

1. **主数据源**：以图片映射表为基准，确保包含所有记录
2. **数据关联**：
   ```
   图片映射表 → 通过MSKU关联商品分类报告（SKU字段）
   图片映射表 → 通过SKU关联产品资料表
   ```
3. **数据填充**：将整合后的数据填充到亚马逊模板

### 🎯 关键原则

- **完整性原则**：填充后的模板必须包含图片映射表的所有记录
- **精确匹配**：所有字段关联采用精确匹配（大小写不敏感）
- **数据优先级**：图片映射表 > 商品分类报告 > 产品资料表 > 默认值

## 📝 实现要求

### ✅ 必须实现的功能

1. **图片映射表完整性检查**：确保所有MSKU都能在输出中找到
2. **双重关联验证**：
   - MSKU关联验证（图片映射表 ↔ 商品分类报告）
   - SKU关联验证（图片映射表 ↔ 产品资料表）
3. **数据完整性报告**：显示匹配成功和失败的记录统计
4. **🆕 向后兼容**：自动检测商品分类报告格式，兼容新旧版本

### ⚠️ 错误处理

- 如果图片映射表中的某个MSKU在商品分类报告中找不到，仍需保留该记录
- 如果图片映射表中的某个SKU在产品资料表中找不到，使用默认包装信息
- 确保输出文件包含图片映射表的全部记录数

## 🔧 技术实现细节

### 🆕 商品分类报告格式检测

```python
def detect_report_format(report_path):
    """
    自动检测商品分类报告格式
    返回：(header_row, sku_field_name)
    """
    # 检测新格式（第4行表头，SKU字段）
    try:
        df_new = pd.read_excel(report_path, sheet_name='Template', header=3)
        if 'SKU' in df_new.columns:
            return 3, 'SKU'  # header=3表示第4行
    except:
        pass
    
    # 检测旧格式（第3行表头，item_sku字段）
    try:
        df_old = pd.read_excel(report_path, sheet_name='Template', header=2)
        if 'item_sku' in df_old.columns:
            return 2, 'item_sku'  # header=2表示第3行
    except:
        pass
    
    raise ValueError("无法识别商品分类报告格式")
```

### 包装字段映射说明

产品资料表中的包装字段映射到模板字段：
```
包装规格长 → package_length
包装规格宽 → package_width  
包装规格高 → package_height
单品毛重 → package_weight
```

### 字段映射代码示例

```python
# 1. 自动检测报告格式
header_row, sku_field = detect_report_format(report_path)

# 2. 读取商品分类报告
report_df = pd.read_excel(report_path, sheet_name='Template', header=header_row)

# 3. 建立MSKU关联（适配新旧格式）
msku_mapping = {}
for _, row in image_mapping_df.iterrows():
    msku = str(row['MSKU']).strip().upper()
    msku_mapping[msku] = row

# 4. 建立SKU关联  
sku_mapping = {}
for _, row in image_mapping_df.iterrows():
    sku = str(row['SKU']).strip().upper()
    sku_mapping[sku] = row

# 5. 确保输出包含所有图片映射记录
output_records = []
for msku, mapping_row in msku_mapping.items():
    # 从商品分类报告获取数据（使用动态检测的字段名）
    report_data = report_df[report_df[sku_field] == msku]
    
    # 从产品资料表获取数据（使用"单个产品"工作表）
    sku = mapping_row['SKU'] 
    product_data = product_df[product_df['*SKU（必填）'] == sku]
    
    # 整合数据并添加到输出
    output_records.append(integrate_data(mapping_row, report_data, product_data))
```

## 📝 格式变化历史

### 🔄 2025年格式变化
- **字段名变化**：`item_sku` → `SKU`
- **表头位置变化**：第3行 → 第4行
- **影响范围**：所有亚马逊商品分类报告

### 🔒 兼容性保证
- 系统自动检测并兼容新旧两种格式
- 用户无需手动选择格式版本
- 错误提示包含具体的格式检测信息

---

## 📅 文档信息

- **创建时间**：2024年初版
- **最新更新**：2025年1月（适配亚马逊格式变化）
- **版本**：v2.0
- **状态**：正式标准
- **修改权限**：仅限核心开发人员

> **🔒 重要提醒**：此映射关系为项目核心逻辑，如需修改请务必经过充分测试和验证！ 