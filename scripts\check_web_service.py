#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查Web服务状态
"""

import requests
import time
import subprocess
import psutil

def check_port_usage(port=5000):
    """检查端口使用情况"""
    print(f"🔍 检查端口 {port} 的使用情况...")
    
    for conn in psutil.net_connections():
        if conn.laddr.port == port:
            try:
                process = psutil.Process(conn.pid)
                print(f"✅ 端口 {port} 正被进程占用: PID={conn.pid}, 进程={process.name()}")
                return True
            except:
                print(f"✅ 端口 {port} 正被占用: PID={conn.pid}")
                return True
    
    print(f"❌ 端口 {port} 未被占用")
    return False

def test_web_service(url="http://localhost:5000"):
    """测试Web服务是否可访问"""
    print(f"🌐 测试Web服务: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ Web服务正常运行: {response.status_code}")
            return True
        else:
            print(f"⚠️ Web服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到Web服务")
        return False
    except Exception as e:
        print(f"❌ 测试Web服务时出错: {str(e)}")
        return False

def start_web_service():
    """启动Web服务"""
    print("🚀 尝试启动Web服务...")
    
    try:
        # 使用Python直接启动
        import sys
        import os
        
        # 添加项目根目录到路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sys.path.insert(0, project_root)
        
        # 启动Web应用
        from src.web.app import app
        print("✅ Web应用模块加载成功")
        
        # 在单独的线程中启动服务
        import threading
        
        def run_app():
            app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
        
        server_thread = threading.Thread(target=run_app)
        server_thread.daemon = True
        server_thread.start()
        
        print("⏳ 等待服务启动...")
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ 启动Web服务失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 Web服务状态检查工具")
    print("=" * 50)
    
    # 1. 检查端口使用情况
    port_in_use = check_port_usage()
    
    # 2. 测试Web服务
    service_running = test_web_service()
    
    if service_running:
        print(f"\n✅ Web服务状态正常!")
        print(f"🌐 访问地址: http://localhost:5000")
        print(f"💡 现在可以上传商品分类报告进行测试")
    else:
        print(f"\n❌ Web服务未运行，尝试启动...")
        if start_web_service():
            # 再次测试
            time.sleep(2)
            if test_web_service():
                print(f"\n✅ Web服务启动成功!")
                print(f"🌐 访问地址: http://localhost:5000")
                print(f"💡 现在可以上传商品分类报告进行测试")
            else:
                print(f"\n❌ Web服务启动后仍无法访问")
        else:
            print(f"\n❌ 无法启动Web服务")
    
    # 输出使用说明
    print(f"\n📋 使用说明:")
    print(f"1. 打开浏览器访问: http://localhost:5000")
    print(f"2. 上传您的商品分类报告文件")
    print(f"3. 查看控制台日志，确认字段映射是否正常")
    print(f"4. 如有问题，可以运行字段分析工具:")
    print(f"   python scripts/analyze_field_mapping.py [您的报告文件路径]")

if __name__ == "__main__":
    main() 