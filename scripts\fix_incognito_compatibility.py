#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 无痕模式兼容性修复脚本
修复其他用户环境下的显示问题，将所有外部CDN依赖替换为本地资源
"""

import os
import shutil
from pathlib import Path

def main():
    """主函数"""
    print("🎯 亚马逊图片上传图床工具 - 无痕模式兼容性修复")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print(f"📂 项目目录: {project_root}")
    
    # 检查修复状态
    template_file = project_root / "src" / "web" / "templates" / "index.html"
    
    if not template_file.exists():
        print("❌ 模板文件不存在!")
        return False
    
    # 读取模板内容
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有外部CDN引用
    cdn_refs = [
        "cdn.jsdelivr.net",
        "fonts.googleapis.com", 
        "cdnjs.cloudflare.com",
        "unpkg.com"
    ]
    
    found_external = []
    for cdn in cdn_refs:
        if cdn in content:
            found_external.append(cdn)
    
    if not found_external:
        print("✅ 模板已经使用本地资源，无需修复!")
        
        # 验证本地资源完整性
        static_dir = project_root / "src" / "web" / "static"
        required_files = [
            "css/bootstrap.min.css",
            "css/style.css", 
            "js/jquery.min.js",
            "js/bootstrap.bundle.min.js",
            "js/app.js",
            "fonts/bootstrap-icons.css",
            "fonts/bootstrap-icons.woff",
            "fonts/bootstrap-icons.woff2"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = static_dir / file_path
            if not full_path.exists():
                missing_files.append(file_path)
            else:
                file_size = full_path.stat().st_size / 1024  # KB
                print(f"📁 {file_path}: {file_size:.1f}KB ✅")
        
        if missing_files:
            print(f"\n⚠️ 缺少文件: {missing_files}")
            print("💡 请运行: python scripts/download_offline_resources.py")
            return False
        else:
            print("\n🎉 所有本地资源文件完整!")
            print("\n📋 修复效果:")
            print("  ✅ 解决其他用户环境下的显示问题")
            print("  ✅ 消除网络依赖，支持离线环境")
            print("  ✅ 避免企业防火墙阻止外部资源")
            print("  ✅ 提升加载速度和稳定性")
            return True
    else:
        print(f"⚠️ 发现外部CDN引用: {found_external}")
        print("🔧 请手动检查模板文件的修复状态")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键关闭...")  # 防止窗口闪退 