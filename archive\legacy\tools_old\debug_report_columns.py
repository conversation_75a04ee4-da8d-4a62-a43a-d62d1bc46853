# -*- coding: utf-8 -*-
"""
调试脚本：查看报告文件的列结构
"""

import pandas as pd
import os

def debug_report_file(file_path):
    """调试报告文件的列结构"""
    print(f"🔍 正在分析文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        # 尝试读取Excel文件
        xl_file = pd.ExcelFile(file_path)
        print(f"📋 工作表列表: {xl_file.sheet_names}")
        
        # 读取每个工作表
        for sheet_name in xl_file.sheet_names:
            print(f"\n📊 工作表: {sheet_name}")
            
            # 尝试不同的header行
            for header_row in [0, 1, 2]:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
                    print(f"  📍 Header行{header_row+1}: 数据形状 {df.shape}")
                    print(f"  📝 列名: {list(df.columns)}")
                    
                    # 显示前几行数据
                    if len(df) > 0:
                        print(f"  📄 前3行数据:")
                        for i in range(min(3, len(df))):
                            print(f"    行{i+1}: {list(df.iloc[i])}")
                    
                    print("-" * 50)
                    
                except Exception as e:
                    print(f"  ❌ Header行{header_row+1} 读取失败: {e}")
                    
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")

if __name__ == "__main__":
    # 查看最近上传的报告文件
    temp_dir = "temp"
    if os.path.exists(temp_dir):
        files = [f for f in os.listdir(temp_dir) if f.startswith('report_')]
        if files:
            latest_file = max(files, key=lambda x: os.path.getctime(os.path.join(temp_dir, x)))
            debug_report_file(os.path.join(temp_dir, latest_file))
        else:
            print("❌ 临时目录中没有找到报告文件")
    else:
        print("❌ 临时目录不存在") 