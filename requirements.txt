# 🎯 亚马逊图片上传图床工具 - 依赖配置文件
# 更新于 2024年，解决pkg_resources弃用问题

# 核心构建工具 (解决pkg_resources弃用警告)
setuptools>=68.0.0
wheel>=0.40.0
importlib-metadata>=6.0.0
packaging>=23.0

# Web框架
Flask==2.3.3
Flask-CORS==4.0.0
Werkzeug==2.3.7
jinja2==3.1.3
itsdangerous==2.1.2
click==8.1.7
markupsafe==2.1.5
blinker==1.7.0

# 网络请求
requests==2.32.3
urllib3>=2.0.0
certifi>=2023.0.0
charset-normalizer>=3.0.0
idna>=3.4

# 图像处理
Pillow==11.2.1

# 数据处理
openpyxl==3.1.5
pandas>=1.3.0
numpy==2.2.6
# 图片重命名功能所需的Excel处理库
xlsxwriter>=3.0.0
xlwt>=1.3.0
xlutils>=2.0.0
xlrd>=2.0.0

# 构建工具
PyInstaller>=6.6.0

# 进程管理 (用于优化构建系统)
psutil>=5.9.0

# 时间处理
python-dateutil==2.9.0
pytz==2024.1

# 兼容性
six==1.16.0 