import pandas as pd
import sys

# 桌面版文件（第一个）
desktop_file = 'd:/华为家庭存储/Pythonproject/亚马逊图片上传图床工具/填充后的模板/填充后的亚马逊模板_美国_20250606_144733.xlsm'
# Web版文件（第二个）
web_file = 'd:/华为家庭存储/Pythonproject/亚马逊图片上传图床工具/填充后的模板/filled_template_20250606_150729.xlsm'

print('正在读取Excel文件...')
print(f'桌面版文件: {desktop_file}')
print(f'Web版文件: {web_file}')

# 从第4行开始读取数据（header=2表示第3行为列头，数据从第4行开始）
desktop_df = pd.read_excel(desktop_file, sheet_name='Template', header=2)
web_df = pd.read_excel(web_file, sheet_name='Template', header=2)

print('\n=== 基本信息对比 ===')
print(f'桌面版数据行数: {len(desktop_df)}')
print(f'Web版数据行数: {len(web_df)}')
print(f'桌面版列数: {len(desktop_df.columns)}')
print(f'Web版列数: {len(web_df.columns)}')

# 查找Product Type列
product_type_col = None
for col in desktop_df.columns:
    if 'product type' in str(col).lower():
        product_type_col = col
        break

print(f'\nProduct Type列名: {product_type_col}')

if product_type_col:
    print('\n=== Product Type字段详细对比 ===')
    
    # 桌面版Product Type分析
    desktop_pt_values = desktop_df[product_type_col].dropna()
    desktop_unique = desktop_pt_values.unique()
    desktop_filled = len(desktop_pt_values)
    
    print(f'桌面版Product Type填充情况:')
    print(f'  - 填充行数: {desktop_filled}/{len(desktop_df)} ({desktop_filled/len(desktop_df)*100:.1f}%)')
    print(f'  - 唯一值数量: {len(desktop_unique)}')
    print(f'  - 唯一值: {list(desktop_unique)}')
    
    # Web版Product Type分析
    web_pt_values = web_df[product_type_col].dropna()
    web_unique = web_pt_values.unique()
    web_filled = len(web_pt_values)
    
    print(f'\nWeb版Product Type填充情况:')
    print(f'  - 填充行数: {web_filled}/{len(web_df)} ({web_filled/len(web_df)*100:.1f}%)')
    print(f'  - 唯一值数量: {len(web_unique)}')
    print(f'  - 唯一值: {list(web_unique)}')
    
    # 前5行对比
    print(f'\n=== 前5行Product Type值对比 ===')
    for i in range(min(5, len(desktop_df), len(web_df))):
        desktop_val = desktop_df[product_type_col].iloc[i] if i < len(desktop_df) else 'N/A'
        web_val = web_df[product_type_col].iloc[i] if i < len(web_df) else 'N/A'
        print(f'第{i+1}行: 桌面版="{desktop_val}", Web版="{web_val}", 一致={desktop_val==web_val}')
else:
    print('未找到Product Type列')

# 查找其他关键字段
key_fields = ['Seller SKU', 'Product Type', 'Quantity', 'Currency', 'Batteries are Included']
found_fields = {}

for field in key_fields:
    for col in desktop_df.columns:
        if field.lower() in str(col).lower():
            found_fields[field] = col
            break

print('\n=== 关键字段填充率对比 ===')
for field_name, col_name in found_fields.items():
    if col_name in desktop_df.columns and col_name in web_df.columns:
        desktop_filled = desktop_df[col_name].notna().sum()
        web_filled = web_df[col_name].notna().sum()
        desktop_total = len(desktop_df)
        web_total = len(web_df)
        
        print(f'{field_name} ({col_name}):')
        print(f'  桌面版: {desktop_filled}/{desktop_total} ({desktop_filled/desktop_total*100:.1f}%)')
        print(f'  Web版: {web_filled}/{web_total} ({web_filled/web_total*100:.1f}%)')
        
        # 显示前3个非空值
        desktop_sample = desktop_df[col_name].dropna().head(3).tolist()
        web_sample = web_df[col_name].dropna().head(3).tolist()
        print(f'  桌面版样本值: {desktop_sample}')
        print(f'  Web版样本值: {web_sample}')
        print()

print('\n=== SKU数据对比（前5行）===')
sku_col = found_fields.get('Seller SKU')
if sku_col:
    for i in range(min(5, len(desktop_df), len(web_df))):
        desktop_sku = desktop_df[sku_col].iloc[i] if i < len(desktop_df) else 'N/A'
        web_sku = web_df[sku_col].iloc[i] if i < len(web_df) else 'N/A'
        print(f'第{i+1}行SKU: 桌面版="{desktop_sku}", Web版="{web_sku}", 一致={desktop_sku==web_sku}')
else:
    print('未找到SKU列')

print('\n对比完成！')