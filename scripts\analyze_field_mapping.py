#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
字段名匹配分析工具

检查商品分类报告中所有字段是否能正确匹配到模板填充逻辑中
特别是检查字段名变化（如 item_sku → SKU, size_name → Size）
"""

import pandas as pd
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.template_filler_web import detect_report_format

def analyze_classification_report_fields(report_path):
    """
    分析商品分类报告中的所有字段名
    
    Args:
        report_path: 商品分类报告路径
    
    Returns:
        dict: 包含字段分析结果的字典
    """
    print(f"🔍 分析商品分类报告字段: {report_path}")
    
    if not os.path.exists(report_path):
        print(f"❌ 文件不存在: {report_path}")
        return None
    
    try:
        # 使用detect_report_format检测格式
        header_row, sku_field_name, format_description = detect_report_format(report_path)
        print(f"📊 报告格式信息:")
        print(f"   📋 格式版本: {format_description}")
        print(f"   📍 表头行号: {header_row + 1}")
        print(f"   🔑 SKU字段名: {sku_field_name}")
        
        # 读取报告数据
        report_df = pd.read_excel(report_path, sheet_name='Template', header=header_row)
        
        # 获取所有字段名
        all_fields = list(report_df.columns)
        print(f"\n📝 商品分类报告包含 {len(all_fields)} 个字段:")
        
        # 分类分析字段
        critical_fields = []  # 关键字段（在模板填充中使用）
        potential_issues = []  # 可能有问题的字段
        unknown_fields = []    # 未知用途的字段
        
        # 定义关键字段及其可能的变体
        critical_field_mapping = {
            'sku': ['item_sku', 'SKU', 'sku', 'Sku', 'Product SKU', 'MSKU'],
            'price': ['standard_price', 'Standard Price', 'price', 'Price', 'List Price', 'list_price'],
            'size': ['size_name', 'Size', 'size', 'SIZE', 'Size Name', 'size name', 'Product Size'],
            'title': ['item_name', 'Item Name', 'title', 'Title', 'Product Title', 'product_title'],
            'description': ['product_description', 'Product Description', 'description', 'Description'],
            'brand': ['brand_name', 'Brand Name', 'brand', 'Brand'],
            'weight': ['item_weight', 'Item Weight', 'weight', 'Weight'],
            'category': ['category', 'Category', 'product_category', 'Product Category'],
            'bullet_points': ['bullet_point1', 'bullet_point2', 'bullet_point3', 'bullet_point4', 'bullet_point5']
        }
        
        # 分析每个字段
        for i, field in enumerate(all_fields, 1):
            field_str = str(field).strip()
            if not field_str or field_str.lower() in ['nan', 'unnamed']:
                continue
                
            print(f"   {i:2d}. {field_str}")
            
            # 检查是否是关键字段
            is_critical = False
            for field_type, variants in critical_field_mapping.items():
                if field_str in variants:
                    critical_fields.append({
                        'field_name': field_str,
                        'field_type': field_type,
                        'status': '✅ 已识别'
                    })
                    is_critical = True
                    break
            
            if not is_critical:
                # 检查是否是类似的字段（可能需要添加支持）
                field_lower = field_str.lower()
                potential_match = None
                
                for field_type, variants in critical_field_mapping.items():
                    for variant in variants:
                        if (variant.lower() in field_lower or 
                            field_lower in variant.lower() or
                            # 模糊匹配常见变体
                            (field_type == 'sku' and 'sku' in field_lower) or
                            (field_type == 'price' and 'price' in field_lower) or
                            (field_type == 'size' and 'size' in field_lower) or
                            (field_type == 'weight' and 'weight' in field_lower)):
                            potential_match = field_type
                            break
                    if potential_match:
                        break
                
                if potential_match:
                    potential_issues.append({
                        'field_name': field_str,
                        'potential_type': potential_match,
                        'status': '⚠️ 可能需要添加支持'
                    })
                else:
                    unknown_fields.append({
                        'field_name': field_str,
                        'status': '❓ 未知用途'
                    })
        
        # 输出分析结果
        print(f"\n📊 字段分析结果:")
        print(f"   ✅ 已识别关键字段: {len(critical_fields)}")
        print(f"   ⚠️ 可能需要处理: {len(potential_issues)}")
        print(f"   ❓ 未知字段: {len(unknown_fields)}")
        
        if critical_fields:
            print(f"\n✅ 已识别的关键字段:")
            for field in critical_fields:
                print(f"   • {field['field_name']} ({field['field_type']}) - {field['status']}")
        
        if potential_issues:
            print(f"\n⚠️ 可能需要添加支持的字段:")
            for field in potential_issues:
                print(f"   • {field['field_name']} (疑似{field['potential_type']}) - {field['status']}")
        
        if unknown_fields:
            print(f"\n❓ 未知用途的字段 (前10个):")
            for field in unknown_fields[:10]:
                print(f"   • {field['field_name']} - {field['status']}")
            if len(unknown_fields) > 10:
                print(f"   ... 还有 {len(unknown_fields) - 10} 个未知字段")
        
        # 检查数据样本
        print(f"\n📋 数据样本分析 (前3行):")
        if not report_df.empty:
            for idx in range(min(3, len(report_df))):
                print(f"   第 {idx+1} 行数据:")
                row = report_df.iloc[idx]
                for field in critical_fields + potential_issues[:5]:  # 只显示关键字段和前5个可能的问题字段
                    field_name = field['field_name']
                    if field_name in row:
                        value = row[field_name]
                        if pd.notna(value):
                            value_str = str(value).strip()
                            if len(value_str) > 50:
                                value_str = value_str[:47] + "..."
                            print(f"     • {field_name}: {value_str}")
                        else:
                            print(f"     • {field_name}: (空值)")
                print()
        
        return {
            'format_info': {
                'format': format_description,
                'header_row': header_row + 1,
                'sku_field_name': sku_field_name
            },
            'all_fields': all_fields,
            'critical_fields': critical_fields,
            'potential_issues': potential_issues,
            'unknown_fields': unknown_fields,
            'total_rows': len(report_df) if not report_df.empty else 0
        }
        
    except Exception as e:
        print(f"❌ 分析出错: {str(e)}")
        return None

def generate_field_mapping_recommendations(analysis_result):
    """
    根据分析结果生成字段映射修复建议
    
    Args:
        analysis_result: analyze_classification_report_fields的返回结果
    """
    if not analysis_result:
        return
    
    print(f"\n🔧 字段映射修复建议:")
    
    # 检查关键字段的支持情况
    supported_fields = [f['field_name'] for f in analysis_result['critical_fields']]
    potential_fields = analysis_result['potential_issues']
    
    recommendations = []
    
    # 检查SKU字段
    sku_fields = [f for f in supported_fields if f in ['item_sku', 'SKU', 'sku', 'Sku']]
    if not sku_fields:
        sku_candidates = [f for f in potential_fields if f['potential_type'] == 'sku']
        if sku_candidates:
            recommendations.append({
                'type': '高优先级',
                'action': f"添加SKU字段支持: {[f['field_name'] for f in sku_candidates]}",
                'code_location': 'detect_report_format函数 - SKU字段检测逻辑'
            })
    
    # 检查Size字段
    size_fields = [f for f in supported_fields if f in ['size_name', 'Size', 'size']]
    if not size_fields:
        size_candidates = [f for f in potential_fields if f['potential_type'] == 'size']
        if size_candidates:
            recommendations.append({
                'type': '高优先级',
                'action': f"添加Size字段支持: {[f['field_name'] for f in size_candidates]}",
                'code_location': 'template_filler_web.py - size_field_candidates列表'
            })
    
    # 检查价格字段
    price_fields = [f for f in supported_fields if 'price' in f.lower()]
    if not price_fields:
        price_candidates = [f for f in potential_fields if f['potential_type'] == 'price']
        if price_candidates:
            recommendations.append({
                'type': '中优先级',
                'action': f"添加价格字段支持: {[f['field_name'] for f in price_candidates]}",
                'code_location': 'template_filler_web.py - 价格字段检测逻辑'
            })
    
    # 输出建议
    if recommendations:
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. [{rec['type']}] {rec['action']}")
            print(f"      📍 修改位置: {rec['code_location']}")
            print()
    else:
        print(f"   ✅ 目前没有发现需要紧急修复的字段映射问题")
    
    # 建议添加的字段变体
    if potential_fields:
        print(f"\n💡 建议添加支持的字段变体:")
        for field in potential_fields[:5]:  # 只显示前5个
            print(f"   • {field['field_name']} (疑似{field['potential_type']}字段)")

def main():
    """主函数"""
    print("🔍 字段名匹配分析工具")
    print("=" * 50)
    
    # 检查是否提供了报告路径
    if len(sys.argv) > 1:
        report_path = sys.argv[1]
    else:
        # 尝试找到最近的商品分类报告
        possible_paths = [
            "data/uploads/商品分类报告.xlsx",
            "data/uploads/商品分类报告.xls",
            "uploads/商品分类报告.xlsx",
            "uploads/商品分类报告.xls"
        ]
        
        report_path = None
        for path in possible_paths:
            if os.path.exists(path):
                report_path = path
                break
        
        if not report_path:
            print("❌ 未找到商品分类报告文件")
            print("💡 使用方法: python scripts/analyze_field_mapping.py [报告文件路径]")
            print("💡 或者将商品分类报告放在以下位置之一:")
            for path in possible_paths:
                print(f"   • {path}")
            return
    
    print(f"📂 分析文件: {report_path}")
    
    # 执行分析
    result = analyze_classification_report_fields(report_path)
    
    if result:
        # 生成修复建议
        generate_field_mapping_recommendations(result)
        
        print(f"\n✅ 分析完成!")
        print(f"📊 总结:")
        print(f"   • 报告格式: {result['format_info']['format']}")
        print(f"   • 数据行数: {result['total_rows']}")
        print(f"   • 字段总数: {len(result['all_fields'])}")
        print(f"   • 已识别关键字段: {len(result['critical_fields'])}")
        print(f"   • 需要关注的字段: {len(result['potential_issues'])}")
    else:
        print(f"❌ 分析失败")

if __name__ == "__main__":
    main() 