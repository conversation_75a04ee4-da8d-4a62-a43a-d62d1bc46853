# Excel模板填充工具启动脚本
# 📊 智能填充亚马逊Excel模板

Write-Host "🚀 启动Excel模板填充Web工具..." -ForegroundColor Green
Write-Host "=" * 50

# 检查Python环境
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python环境: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到Python环境，请先安装Python" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} catch {
    Write-Host "❌ 无法检查Python版本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查依赖包
Write-Host "📦 检查依赖包..." -ForegroundColor Yellow

$requiredPackages = @(
    "flask",
    "flask-cors", 
    "pandas",
    "openpyxl",
    "werkzeug"
)

$missingPackages = @()

foreach ($package in $requiredPackages) {
    try {
        $result = python -c "import $($package.Replace('-', '_'))" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ $package" -ForegroundColor Green
        } else {
            $missingPackages += $package
            Write-Host "  ❌ $package (缺失)" -ForegroundColor Red
        }
    } catch {
        $missingPackages += $package
        Write-Host "  ❌ $package (检查失败)" -ForegroundColor Red
    }
}

# 安装缺失的包
if ($missingPackages.Count -gt 0) {
    Write-Host "📥 安装缺失的依赖包..." -ForegroundColor Yellow
    foreach ($package in $missingPackages) {
        Write-Host "  安装 $package..." -ForegroundColor Cyan
        try {
            pip install $package
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ $package 安装成功" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $package 安装失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $package 安装异常" -ForegroundColor Red
        }
    }
}

# 检查必要目录
Write-Host "📁 检查必要目录..." -ForegroundColor Yellow

$requiredDirs = @(
    "uploads",
    "填充后的模板", 
    "temp",
    "templates"
)

foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir" -ForegroundColor Green
    } else {
        try {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "  ✅ $dir (已创建)" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ $dir (创建失败)" -ForegroundColor Red
        }
    }
}

# 检查模板文件
if (Test-Path "templates/template_filler.html") {
    Write-Host "✅ HTML模板文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ HTML模板文件缺失" -ForegroundColor Red
}

# 显示功能特性
Write-Host ""
Write-Host "📋 工具特性:" -ForegroundColor Cyan
Write-Host "  • 智能读取Excel模板和商品报告" -ForegroundColor White
Write-Host "  • 精确字段匹配（区分大小写）" -ForegroundColor White
Write-Host "  • 批量高性能数据填充" -ForegroundColor White
Write-Host "  • 仅处理前300行数据" -ForegroundColor White
Write-Host "  • 保持原有格式和公式" -ForegroundColor White
Write-Host "  • 实时进度显示" -ForegroundColor White

Write-Host ""
Write-Host "🌐 启动Web服务器..." -ForegroundColor Green
Write-Host "访问地址: http://localhost:5000" -ForegroundColor Yellow

# 启动应用
try {
    # 设置环境变量
    $env:FLASK_ENV = "production"
    $env:PYTHONPATH = $PWD
    
    # 启动Flask应用
    python template_filler_app.py
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "👋 感谢使用Excel模板填充工具！" -ForegroundColor Green
Read-Host "按任意键退出" 