#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析商品分类报告文件结构
支持亚马逊2025年格式变化的自动检测
"""

import pandas as pd
import os

def analyze_report_file():
    report_path = r'D:\华为家庭存储\工作\运营\Listing相关\Listing\US\分类商品报告+05-27-2025.xlsm'
    
    if not os.path.exists(report_path):
        print('❌ 文件不存在')
        return
    
    print('📋 文件存在，开始分析...')
    print(f'文件路径: {report_path}')
    
    try:
        # 读取Excel文件信息
        excel_file = pd.ExcelFile(report_path, engine='openpyxl')
        print(f'工作表列表: {excel_file.sheet_names}')
        
        # 分析每个可能的表头位置
        for i in range(5):
            print(f'\n=== Header={i} (第{i+1}行作为表头) ===')
            try:
                df = pd.read_excel(report_path, header=i, nrows=5, engine='openpyxl')
                print(f'读取成功！')
                print(f'列数: {len(df.columns)}')
                print(f'行数: {len(df)}')
                print(f'列名: {list(df.columns)}')
                
                # 🆕 检查SKU相关列（优先检查新格式的SKU字段）
                sku_columns = ['SKU', 'sku', 'item_sku', 'ITEM_SKU', 'Item_Sku', 'MSKU', 'msku']
                found_sku = []
                for col in df.columns:
                    col_str = str(col).strip()
                    # 精确匹配优先
                    if col_str in sku_columns:
                        found_sku.append(col_str)
                    # 模糊匹配
                    elif any(sku_name.lower() in col_str.lower() for sku_name in sku_columns):
                        found_sku.append(col_str)
                
                if found_sku:
                    print(f'🎯 找到SKU相关列: {found_sku}')
                    
                    # 🆕 判断可能的格式
                    if 'SKU' in found_sku and i == 3:
                        print('✅ 可能是新格式（2025年）：第4行表头，SKU字段')
                    elif 'item_sku' in found_sku and i == 2:
                        print('✅ 可能是旧格式（2024年及之前）：第3行表头，item_sku字段')
                    else:
                        print(f'⚠️ 检测到SKU字段但格式不标准：第{i+1}行表头，{found_sku}')
                else:
                    print('❌ 未找到SKU相关列')
                
                if len(df) > 0:
                    print(f'第一行数据样例: {df.iloc[0].tolist()[:5]}...')
                    
            except Exception as e:
                print(f'读取失败: {e}')
        
        # 🆕 尝试使用自动检测功能
        print('\n=== 🔍 使用自动格式检测 ===')
        try:
            # 导入检测函数（如果在同一项目中）
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
            from template_filler_web import detect_report_format
            
            header_row, sku_field_name, format_info = detect_report_format(report_path)
            print(f'🎯 自动检测结果：')
            print(f'  - 表头行号: {header_row} (第{header_row+1}行)')
            print(f'  - SKU字段名: {sku_field_name}')
            print(f'  - 格式信息: {format_info}')
            
            # 读取检测到的数据
            detected_df = pd.read_excel(report_path, header=header_row, nrows=5, engine='openpyxl')
            print(f'✅ 使用检测到的格式读取成功！')
            print(f'字段列表: {list(detected_df.columns)[:10]}...')
            
            if sku_field_name in detected_df.columns:
                sku_sample = detected_df[sku_field_name].head(3).tolist()
                print(f'📋 {sku_field_name}字段示例: {sku_sample}')
            
        except Exception as e:
            print(f'❌ 自动检测失败: {e}')
        
    except Exception as e:
        print(f'整体分析失败: {e}')

if __name__ == '__main__':
    analyze_report_file() 