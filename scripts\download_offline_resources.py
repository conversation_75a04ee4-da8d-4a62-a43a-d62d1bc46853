#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
📦 离线资源下载脚本
下载所有外部CDN资源到本地，确保应用在任何环境下都能正常显示
"""

import os
import requests
import sys
from pathlib import Path

def download_file(url, local_path, description):
    """下载文件到本地"""
    try:
        print(f"📥 正在下载 {description}...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        file_size = len(response.content) / 1024  # KB
        print(f"✅ {description} 下载完成 ({file_size:.1f}KB)")
        return True
        
    except Exception as e:
        print(f"❌ {description} 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 亚马逊图片上传图床工具 - 离线资源下载器")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    static_dir = project_root / "src" / "web" / "static"
    
    print(f"📂 项目目录: {project_root}")
    print(f"📁 静态资源目录: {static_dir}")
    
    # 需要下载的资源列表
    resources = [
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css",
            "path": static_dir / "fonts" / "bootstrap-icons.css",
            "description": "Bootstrap Icons CSS"
        },
        {
            "url": "https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js",
            "path": static_dir / "js" / "jquery.min.js", 
            "description": "jQuery JavaScript"
        }
    ]
    
    # 下载字体文件 (从bootstrap-icons.css中提取)
    bootstrap_icons_fonts = [
        "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff",
        "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/fonts/bootstrap-icons.woff2"
    ]
    
    success_count = 0
    total_count = len(resources) + len(bootstrap_icons_fonts)
    
    # 下载主要资源
    for resource in resources:
        if download_file(resource["url"], resource["path"], resource["description"]):
            success_count += 1
    
    # 下载字体文件
    for font_url in bootstrap_icons_fonts:
        font_name = os.path.basename(font_url)
        font_path = static_dir / "fonts" / font_name
        if download_file(font_url, font_path, f"Bootstrap Icons 字体 - {font_name}"):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📋 下载结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有资源下载完成！现在可以更新HTML模板使用本地资源。")
        return True
    else:
        print("⚠️ 部分资源下载失败，请检查网络连接。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 