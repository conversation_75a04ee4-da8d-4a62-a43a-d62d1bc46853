#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🧹 亚马逊图片上传图床工具 - 构建清理脚本
清理打包过程中产生的临时文件和目录
"""

import os
import shutil
import sys
from pathlib import Path
import glob

def print_step(message):
    """打印步骤信息"""
    print(f"\n🧹 {message}")
    print('-' * 50)

def safe_remove(path):
    """安全删除文件或目录"""
    try:
        if os.path.isfile(path):
            os.remove(path)
            print(f"  ✅ 删除文件: {path}")
            return True
        elif os.path.isdir(path):
            shutil.rmtree(path)
            print(f"  ✅ 删除目录: {path}")
            return True
        else:
            print(f"  ⚠️ 路径不存在: {path}")
            return False
    except Exception as e:
        print(f"  ❌ 删除失败: {path} - {e}")
        return False

def get_file_size_mb(path):
    """获取文件或目录大小（MB）"""
    try:
        if os.path.isfile(path):
            return os.path.getsize(path) / (1024 * 1024)
        elif os.path.isdir(path):
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, IOError):
                        pass
            return total_size / (1024 * 1024)
        return 0
    except:
        return 0

def clean_pyinstaller_files():
    """清理PyInstaller生成的文件"""
    print_step("清理PyInstaller生成的文件")
    
    pyinstaller_patterns = [
        'build',
        'dist/亚马逊图片上传图床工具_temp_build',
        '*.spec',
        '__pycache__',
        '**/__pycache__',
        '*.pyc',
        '**/*.pyc'
    ]
    
    cleaned_size = 0
    
    for pattern in pyinstaller_patterns:
        # 使用glob查找匹配的文件和目录
        matches = glob.glob(pattern, recursive=True)
        
        for match in matches:
            if os.path.exists(match):
                size = get_file_size_mb(match)
                cleaned_size += size
                
                if safe_remove(match):
                    if size > 0.1:  # 只显示大于0.1MB的文件
                        print(f"    ({size:.1f} MB)")

    return cleaned_size

def clean_temp_files():
    """清理临时文件"""
    print_step("清理临时文件")
    
    temp_patterns = [
        'temp',
        'tmp',
        '*.tmp',
        '*.log',
        '*.bak',
        'logs/*.log',
        'output/temp/*',
        'data/temp/*',
        '.pytest_cache',
        '.coverage'
    ]
    
    cleaned_size = 0
    
    for pattern in temp_patterns:
        matches = glob.glob(pattern, recursive=True)
        
        for match in matches:
            if os.path.exists(match):
                size = get_file_size_mb(match)
                cleaned_size += size
                
                if safe_remove(match):
                    if size > 0.1:
                        print(f"    ({size:.1f} MB)")

    return cleaned_size

def clean_python_cache():
    """清理Python缓存文件"""
    print_step("清理Python缓存文件")
    
    cache_patterns = [
        '**/__pycache__',
        '**/*.pyc',
        '**/*.pyo',
        '**/*.pyd',
        '.mypy_cache',
        '.pytest_cache'
    ]
    
    cleaned_size = 0
    
    for pattern in cache_patterns:
        matches = glob.glob(pattern, recursive=True)
        
        for match in matches:
            if os.path.exists(match):
                size = get_file_size_mb(match)
                cleaned_size += size
                safe_remove(match)

    return cleaned_size

def clean_old_builds():
    """清理旧的构建文件"""
    print_step("清理旧的构建文件")
    
    old_build_patterns = [
        'dist/亚马逊图片上传图床工具_v*',
        'dist/亚马逊图片上传图床工具_简化版_*',
        '*.zip',
        'build_*.spec'
    ]
    
    cleaned_size = 0
    
    for pattern in old_build_patterns:
        matches = glob.glob(pattern)
        
        for match in matches:
            if os.path.exists(match):
                size = get_file_size_mb(match)
                
                # 确认是否删除大文件
                if size > 50:  # 大于50MB的文件需要确认
                    confirm = input(f"  ⚠️ 发现大文件 {match} ({size:.1f} MB)，是否删除? (y/n): ")
                    if confirm.lower() != 'y':
                        print(f"  ⏭️ 跳过: {match}")
                        continue
                
                cleaned_size += size
                if safe_remove(match):
                    if size > 0.1:
                        print(f"    ({size:.1f} MB)")

    return cleaned_size

def clean_node_modules():
    """清理Node.js模块（如果存在）"""
    print_step("清理Node.js模块")
    
    node_patterns = [
        'node_modules',
        'package-lock.json',
        'yarn.lock'
    ]
    
    cleaned_size = 0
    
    for pattern in node_patterns:
        if os.path.exists(pattern):
            size = get_file_size_mb(pattern)
            cleaned_size += size
            safe_remove(pattern)

    return cleaned_size

def clean_ide_files():
    """清理IDE临时文件"""
    print_step("清理IDE临时文件")
    
    ide_patterns = [
        '.vscode/settings.json',
        '.idea',
        '*.swp',
        '*.swo',
        '*~',
        '.DS_Store',
        'Thumbs.db'
    ]
    
    cleaned_size = 0
    
    for pattern in ide_patterns:
        matches = glob.glob(pattern, recursive=True)
        
        for match in matches:
            if os.path.exists(match):
                size = get_file_size_mb(match)
                cleaned_size += size
                safe_remove(match)

    return cleaned_size

def show_disk_usage():
    """显示磁盘使用情况"""
    print_step("当前目录磁盘使用情况")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage('.')
        
        total_gb = total / (1024**3)
        used_gb = used / (1024**3)
        free_gb = free / (1024**3)
        
        print(f"  💾 总空间: {total_gb:.1f} GB")
        print(f"  📊 已使用: {used_gb:.1f} GB")
        print(f"  💿 可用空间: {free_gb:.1f} GB")
        
    except Exception as e:
        print(f"  ❌ 无法获取磁盘信息: {e}")

def main():
    """主清理函数"""
    print("🧹 亚马逊图片上传图床工具 - 构建清理脚本")
    print("=" * 60)
    print("📋 本脚本将清理构建和开发过程中产生的临时文件")
    print("=" * 60)
    
    # 显示当前磁盘使用情况
    show_disk_usage()
    
    # 确认是否继续
    print("\n⚠️ 即将清理以下内容:")
    print("  - PyInstaller构建文件 (build/, dist/)")
    print("  - Python缓存文件 (__pycache__, *.pyc)")
    print("  - 临时文件 (*.tmp, *.log)")
    print("  - 旧的打包文件")
    print("  - IDE临时文件")
    
    confirm = input(f"\n是否继续清理? (y/n) [默认:y]: ")
    if confirm.lower() == 'n':
        print("❌ 用户取消清理")
        return False
    
    total_cleaned = 0
    
    try:
        # 执行各种清理操作
        total_cleaned += clean_pyinstaller_files()
        total_cleaned += clean_python_cache() 
        total_cleaned += clean_temp_files()
        total_cleaned += clean_old_builds()
        total_cleaned += clean_node_modules()
        total_cleaned += clean_ide_files()
        
        # 显示清理结果
        print("\n" + "=" * 60)
        print("🎉 清理完成！")
        print("=" * 60)
        print(f"💾 总共清理了: {total_cleaned:.1f} MB")
        
        if total_cleaned > 0:
            print("✅ 磁盘空间已释放")
        else:
            print("💡 没有发现需要清理的文件")
        
        # 显示清理后的磁盘使用情况
        show_disk_usage()
        
        return True
        
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False

if __name__ == '__main__':
    success = main()
    
    print(f"\n{'='*60}")
    print("🧹 清理脚本完成")
    print("=" * 60)
    
    if success:
        print("✅ 清理成功完成！")
    else:
        print("❌ 清理过程中遇到问题")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1) 