#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建测试用的商品分类报告

包含最新的字段名格式（SKU、Size等），用于测试字段映射功能
"""

import pandas as pd
import os
from datetime import datetime

def create_test_classification_report():
    """
    创建包含最新字段名的测试商品分类报告
    """
    print("🔧 创建测试商品分类报告...")
    
    # 确保目录存在
    os.makedirs("data/uploads", exist_ok=True)
    
    # 新格式的字段名（2025年格式）
    # 第1行：标题行
    title_row = ["商品分类报告 - 2025年新格式"]
    
    # 第2行：空行
    empty_row = [""]
    
    # 第3行：副标题
    subtitle_row = ["产品信息详细列表"]
    
    # 第4行：表头（新格式）
    headers = [
        "SKU",  # 新格式：item_sku → SKU
        "Size",  # 新格式：size_name → Size  
        "Standard Price",  # 价格字段
        "List Price",  # 列表价格
        "Product Title",  # 产品标题
        "Brand Name",  # 品牌名称
        "Product Description",  # 产品描述
        "Item Weight",  # 重量
        "Category",  # 分类
        "Color",  # 颜色
        "Material",  # 材质
        "Dimensions",  # 尺寸
        "Package Quantity",  # 包装数量
        "Country of Origin",  # 原产地
        "Manufacturer",  # 制造商
        "Model Number",  # 型号
        "UPC",  # UPC码
        "Keywords",  # 关键词
        "Bullet Point 1",  # 要点1
        "Bullet Point 2",  # 要点2
        "Bullet Point 3",  # 要点3
        "Item Height",  # 物品高度
        "Item Length",  # 物品长度
        "Item Width"   # 物品宽度
    ]
    
    # 测试数据（多行）
    test_data = [
        [
            "TEST-RIBBON-001",  # SKU
            "1-1/2\" X 10 Yards",  # Size - 这是用户报告的具体值
            "15.99",  # Standard Price
            "19.99",  # List Price
            "Decorative Ribbon Trim - Premium Quality",  # Product Title
            "CraftMaster",  # Brand Name
            "High-quality decorative ribbon trim perfect for crafting and decoration projects.",  # Product Description
            "0.2",  # Item Weight
            "Arts & Crafts",  # Category
            "Gold",  # Color
            "Polyester",  # Material
            "1.5 x 360 inches",  # Dimensions
            "1",  # Package Quantity
            "China",  # Country of Origin
            "CraftMaster Inc.",  # Manufacturer
            "CR-GT-001",  # Model Number
            "123456789012",  # UPC
            "ribbon, trim, decorative, craft, gold",  # Keywords
            "Premium quality polyester material",  # Bullet Point 1
            "Perfect for crafting and decoration",  # Bullet Point 2
            "Easy to cut and work with",  # Bullet Point 3
            "0.1",  # Item Height
            "360",  # Item Length
            "1.5"   # Item Width
        ],
        [
            "TEST-RIBBON-002",  # SKU
            "3/4\" X 25 Yards",  # Size
            "12.99",  # Standard Price
            "16.99",  # List Price
            "Satin Ribbon Roll - Multiple Colors Available",  # Product Title
            "CraftMaster",  # Brand Name
            "Smooth satin ribbon roll ideal for gift wrapping and crafting.",  # Product Description
            "0.3",  # Item Weight
            "Arts & Crafts",  # Category
            "Silver",  # Color
            "Satin",  # Material
            "0.75 x 900 inches",  # Dimensions
            "1",  # Package Quantity
            "China",  # Country of Origin
            "CraftMaster Inc.",  # Manufacturer
            "CR-SL-002",  # Model Number
            "123456789013",  # UPC
            "ribbon, satin, gift wrap, silver",  # Keywords
            "Smooth satin finish",  # Bullet Point 1
            "Available in multiple colors",  # Bullet Point 2
            "Perfect for gift wrapping",  # Bullet Point 3
            "0.08",  # Item Height
            "900",  # Item Length
            "0.75"   # Item Width
        ],
        [
            "TEST-RIBBON-003",  # SKU
            "2\" X 50 Yards",  # Size
            "25.99",  # Standard Price
            "32.99",  # List Price
            "Wide Grosgrain Ribbon - Heavy Duty",  # Product Title
            "ProCraft",  # Brand Name
            "Heavy-duty grosgrain ribbon suitable for professional crafting projects.",  # Product Description
            "0.8",  # Item Weight
            "Arts & Crafts",  # Category
            "Navy Blue",  # Color
            "Grosgrain",  # Material
            "2 x 1800 inches",  # Dimensions
            "1",  # Package Quantity
            "China",  # Country of Origin
            "ProCraft Ltd.",  # Manufacturer
            "PC-NB-003",  # Model Number
            "123456789014",  # UPC
            "ribbon, grosgrain, heavy duty, navy",  # Keywords
            "Heavy-duty grosgrain material",  # Bullet Point 1
            "Professional grade quality",  # Bullet Point 2
            "Suitable for demanding projects",  # Bullet Point 3
            "0.15",  # Item Height
            "1800",  # Item Length
            "2"   # Item Width
        ]
    ]
    
    # 创建Excel文件
    output_path = "data/uploads/商品分类报告.xlsx"
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # 创建数据框 - 包含所有行
        all_data = [title_row + [""] * (len(headers) - 1)]  # 标题行补齐长度
        all_data.append(empty_row + [""] * (len(headers) - 1))  # 空行补齐长度
        all_data.append(subtitle_row + [""] * (len(headers) - 1))  # 副标题行补齐长度
        all_data.append(headers)  # 表头行
        all_data.extend(test_data)  # 数据行
        
        # 创建DataFrame
        df = pd.DataFrame(all_data)
        
        # 写入Excel
        df.to_excel(writer, sheet_name='Template', index=False, header=False)
        
        print(f"✅ 创建测试报告: {output_path}")
        print(f"📊 报告格式:")
        print(f"   • 第1行: 标题")
        print(f"   • 第2行: 空行")
        print(f"   • 第3行: 副标题")
        print(f"   • 第4行: 表头 (新格式)")
        print(f"   • 第5-7行: 测试数据 ({len(test_data)}行)")
        print(f"   • 字段总数: {len(headers)}")
        
        # 显示关键字段
        print(f"\n🔑 关键字段名:")
        key_fields = ["SKU", "Size", "Standard Price", "List Price", "Product Title"]
        for field in key_fields:
            if field in headers:
                idx = headers.index(field) + 1
                print(f"   • {field} (第{idx}列)")
        
        print(f"\n📋 Size字段测试值:")
        for i, row in enumerate(test_data, 1):
            size_value = row[headers.index("Size")]
            print(f"   • 第{i}行: {size_value}")
    
    return output_path

def main():
    """主函数"""
    print("🔧 测试商品分类报告创建工具")
    print("=" * 50)
    
    # 创建测试报告
    report_path = create_test_classification_report()
    
    print(f"\n✅ 测试报告创建完成!")
    print(f"📂 文件位置: {report_path}")
    print(f"\n💡 接下来可以运行以下命令进行字段分析:")
    print(f"   python scripts/analyze_field_mapping.py")

if __name__ == "__main__":
    main() 