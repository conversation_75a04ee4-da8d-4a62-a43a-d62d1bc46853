# -*- coding: utf-8 -*-
"""
通用工具函数模块
包含文件处理、验证、日志等通用功能
"""

import os
import re
import logging
import time
from datetime import datetime

def validate_image_filename(filename):
    """
    验证图片文件名是否符合要求：B0开头且包含MAIN、PT或SWATCH关键词
    
    参数:
        filename (str): 文件名
        
    返回:
        tuple: (is_valid: bool, reason: str)
    """
    filename_upper = filename.upper()
    
    # 检查是否以B0开头
    if not filename_upper.startswith("B0"):
        return False, "文件名必须以B0开头（Amazon ASIN格式）"
    
    # 检查是否包含MAIN、PT或SWATCH关键词
    if not ('MAIN' in filename_upper or 'PT' in filename_upper or 'SWATCH' in filename_upper):
        return False, "文件名必须包含MAIN、PT或SWATCH关键词以标识图片类型"
    
    # 检查文件扩展名
    valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    _, ext = os.path.splitext(filename.lower())
    if ext not in valid_extensions:
        return False, "不支持的图片格式"
    
    return True, "文件名格式正确"

def determine_image_type(filename):
    """
    根据文件名确定图片类型
    
    参数:
        filename (str): 文件名
        
    返回:
        str: 图片类型 (MAIN, SWATCH, PT01-PT08)
    """
    filename_upper = filename.upper()
    
    if 'MAIN' in filename_upper:
        return 'MAIN'
    elif 'SWATCH' in filename_upper:
        return 'SWATCH'
    else:
        # 检查是否为附图 PT01-PT08
        for i in range(1, 9):
            pt_code = f'PT{i:02d}'
            if pt_code in filename_upper:
                return pt_code
        
        # 如果没有特定标识，默认作为附图处理
        return 'PT01'

def extract_asin(filename):
    """
    从文件名中提取ASIN
    
    参数:
        filename (str): 文件名
        
    返回:
        str: ASIN或None
    """
    # ASIN格式：B开头，总共10位
    match = re.match(r'^(B[0-9A-Z]{9})', filename.upper())
    if match:
        return match.group(1)
    return None

def setup_logging(log_file=None, level=logging.INFO):
    """
    设置日志记录
    
    参数:
        log_file (str, optional): 日志文件路径
        level (int): 日志级别
    """
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置根日志器
    logger = logging.getLogger()
    logger.setLevel(level)
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 添加文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

def ensure_dir_exists(directory):
    """
    确保目录存在，如果不存在则创建
    
    参数:
        directory (str): 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

def get_timestamp():
    """
    获取当前时间戳字符串
    
    返回:
        str: 格式化的时间戳
    """
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def safe_filename(filename):
    """
    生成安全的文件名（移除特殊字符）
    
    参数:
        filename (str): 原始文件名
        
    返回:
        str: 安全的文件名
    """
    # 移除或替换特殊字符
    safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return safe_chars

def format_file_size(size_bytes):
    """
    格式化文件大小
    
    参数:
        size_bytes (int): 文件大小（字节）
        
    返回:
        str: 格式化的文件大小
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def retry_on_failure(max_retries=3, delay=1.0):
    """
    重试装饰器
    
    参数:
        max_retries (int): 最大重试次数
        delay (float): 重试间隔（秒）
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        print(f"第 {attempt + 1} 次尝试失败: {str(e)}")
                        time.sleep(delay * (attempt + 1))  # 递增延迟
                    else:
                        print(f"所有 {max_retries + 1} 次尝试都失败了")
            
            raise last_exception
        return wrapper
    return decorator

# 兼容性函数 - 为了保持与旧版本Web应用的兼容性
def test_api_connection():
    """测试API连接的兼容性函数"""
    try:
        from src.core.image_uploader import default_uploader
        return default_uploader.test_connection()
    except ImportError:
        return {
            'success': False,
            'error': '无法导入上传器模块',
            'message': 'API连接测试不可用'
        }

def ensure_history_dir():
    """确保历史记录目录存在"""
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    history_dir = os.path.join(project_root, 'output', 'results')
    ensure_dir_exists(history_dir)
    return history_dir 