#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🚀 动态端口管理器
自动检测可用端口，避免端口冲突，提供智能端口分配
"""

import socket
import subprocess
import platform
import logging
import os
import time
import webbrowser
from typing import List, Optional, Tuple
import threading


class PortManager:
    """🚀 智能端口管理器"""
    
    # 📊 默认端口配置
    DEFAULT_PORTS = [5000, 5001, 5002, 5003, 8000, 8001, 8080, 8888]
    RESERVED_PORTS = [80, 443, 22, 21, 25, 53, 110, 143, 993, 995]
    
    def __init__(self, preferred_port: int = 5000, app_name: str = "Flask应用"):
        """
        初始化端口管理器
        
        Args:
            preferred_port: 首选端口
            app_name: 应用名称，用于显示
        """
        self.preferred_port = preferred_port
        self.app_name = app_name
        self.current_port = None
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """📝 设置日志记录器"""
        logger = logging.getLogger("PortManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def is_port_available(self, port: int, host: str = 'localhost') -> bool:
        """
        🔍 检查端口是否可用
        
        Args:
            port: 要检查的端口
            host: 主机地址
            
        Returns:
            bool: 端口是否可用
        """
        try:
            # 检查TCP端口
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                if result == 0:
                    return False  # 端口被占用
            
            # 检查UDP端口（可选）
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                try:
                    sock.bind((host, port))
                    return True  # 端口可用
                except OSError:
                    return False  # 端口被占用
                    
        except Exception as e:
            self.logger.warning(f"⚠️ 检查端口 {port} 时出错: {e}")
            return False
    
    def get_port_process_info(self, port: int) -> Optional[str]:
        """
        🔍 获取占用端口的进程信息
        
        Args:
            port: 端口号
            
        Returns:
            str: 进程信息，如果获取失败返回None
        """
        try:
            system = platform.system().lower()
            
            if system == "windows":
                # Windows系统使用netstat
                cmd = f"netstat -ano | findstr :{port}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if f":{port}" in line and "LISTENING" in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                # 获取进程名
                                try:
                                    cmd_task = f"tasklist /FI \"PID eq {pid}\" /FO CSV"
                                    task_result = subprocess.run(cmd_task, shell=True, capture_output=True, text=True)
                                    if task_result.stdout:
                                        lines = task_result.stdout.strip().split('\n')
                                        if len(lines) > 1:
                                            process_name = lines[1].split(',')[0].strip('"')
                                            return f"{process_name} (PID: {pid})"
                                except:
                                    pass
                                return f"PID: {pid}"
            else:
                # Linux/Mac系统使用lsof
                cmd = f"lsof -i :{port}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        parts = lines[1].split()
                        if len(parts) >= 2:
                            return f"{parts[0]} (PID: {parts[1]})"
                            
        except Exception as e:
            self.logger.debug(f"获取端口 {port} 进程信息失败: {e}")
            
        return None
    
    def find_available_port(self, start_port: int = None, port_range: int = 100) -> Optional[int]:
        """
        🔍 查找可用端口
        
        Args:
            start_port: 起始端口，默认使用首选端口
            port_range: 查找范围
            
        Returns:
            int: 可用端口，如果没找到返回None
        """
        if start_port is None:
            start_port = self.preferred_port
            
        # 首先尝试默认端口列表
        for port in self.DEFAULT_PORTS:
            if port >= start_port and port not in self.RESERVED_PORTS:
                if self.is_port_available(port):
                    return port
                else:
                    process_info = self.get_port_process_info(port)
                    if process_info:
                        self.logger.info(f"⚠️ 端口 {port} 被占用: {process_info}")
                    else:
                        self.logger.info(f"⚠️ 端口 {port} 被占用")
        
        # 如果默认端口都不可用，扫描范围内的端口
        self.logger.info(f"🔍 在范围 {start_port}-{start_port + port_range} 中查找可用端口...")
        for port in range(start_port, start_port + port_range):
            if port not in self.RESERVED_PORTS and self.is_port_available(port):
                return port
                
        return None
    
    def allocate_port(self) -> int:
        """
        🎯 分配可用端口
        
        Returns:
            int: 分配的端口号
            
        Raises:
            RuntimeError: 如果没有可用端口
        """
        # 首先尝试首选端口
        if self.is_port_available(self.preferred_port):
            self.current_port = self.preferred_port
            self.logger.info(f"✅ 使用首选端口: {self.preferred_port}")
            return self.preferred_port
        
        # 查找替代端口
        port = self.find_available_port()
        if port:
            self.current_port = port
            if port != self.preferred_port:
                process_info = self.get_port_process_info(self.preferred_port)
                if process_info:
                    self.logger.warning(f"⚠️ 首选端口 {self.preferred_port} 被占用 ({process_info})，使用端口 {port}")
                else:
                    self.logger.warning(f"⚠️ 首选端口 {self.preferred_port} 被占用，使用端口 {port}")
            return port
        
        # 没有找到可用端口
        raise RuntimeError(f"❌ 无法找到可用端口！请检查系统端口使用情况")
    
    def get_access_urls(self, port: int = None) -> List[str]:
        """
        🌐 获取访问地址列表
        
        Args:
            port: 端口号，默认使用当前端口
            
        Returns:
            List[str]: 访问地址列表
        """
        if port is None:
            port = self.current_port or self.preferred_port
            
        urls = []
        
        # 本地访问地址
        urls.append(f"http://localhost:{port}")
        urls.append(f"http://127.0.0.1:{port}")
        
        # 局域网访问地址
        try:
            # 获取本机IP地址
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip and local_ip != "127.0.0.1":
                urls.append(f"http://{local_ip}:{port}")
        except:
            pass
            
        # 尝试获取更准确的局域网IP
        try:
            # 通过连接外部地址获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                url = f"http://{local_ip}:{port}"
                if url not in urls:
                    urls.append(url)
        except:
            pass
            
        return urls
    
    def open_browser(self, port: int = None, delay: float = 1.0) -> bool:
        """
        🌐 自动打开浏览器
        
        Args:
            port: 端口号
            delay: 延迟时间（秒）
            
        Returns:
            bool: 是否成功打开
        """
        if port is None:
            port = self.current_port or self.preferred_port
            
        url = f"http://localhost:{port}"
        
        def delayed_open():
            time.sleep(delay)
            try:
                webbrowser.open(url)
                self.logger.info(f"🌐 已打开浏览器: {url}")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ 无法自动打开浏览器: {e}")
                return False
        
        # 在后台线程中打开浏览器
        thread = threading.Thread(target=delayed_open, daemon=True)
        thread.start()
        return True
    
    def display_startup_info(self, port: int = None) -> None:
        """
        📋 显示启动信息
        
        Args:
            port: 端口号
        """
        if port is None:
            port = self.current_port or self.preferred_port
            
        urls = self.get_access_urls(port)
        
        print("\n" + "="*60)
        print(f"🚀 {self.app_name} 已启动！")
        print("="*60)
        print(f"📊 运行端口: {port}")
        print(f"🌐 访问地址:")
        
        for i, url in enumerate(urls):
            prefix = "   ├─" if i < len(urls) - 1 else "   └─"
            print(f"{prefix} {url}")
            
        print(f"\n💡 提示:")
        print(f"   ├─ 按 Ctrl+C 停止服务")
        print(f"   ├─ 浏览器将自动打开访问地址")
        print(f"   └─ 局域网用户可通过上述IP地址访问")
        print("="*60 + "\n")


class FlaskPortManager(PortManager):
    """🌶️ Flask应用专用端口管理器"""
    
    def __init__(self, app_name: str = "Flask Web应用", preferred_port: int = 5000):
        super().__init__(preferred_port, app_name)
        
    def run_with_dynamic_port(self, app, host: str = '0.0.0.0', **kwargs) -> None:
        """
        🚀 使用动态端口运行Flask应用
        
        Args:
            app: Flask应用实例
            host: 监听地址
            **kwargs: 其他Flask运行参数
        """
        try:
            # 分配端口
            port = self.allocate_port()
            
            # 显示启动信息
            self.display_startup_info(port)
            
            # 自动打开浏览器
            self.open_browser(port, delay=1.5)
            
            # 启动Flask应用
            app.run(
                host=host,
                port=port,
                **kwargs
            )
            
        except KeyboardInterrupt:
            self.logger.info(f"\n👋 用户中断，{self.app_name} 已停止")
        except Exception as e:
            self.logger.error(f"❌ 启动失败: {e}")
            raise


# 🛠️ 便捷函数
def create_flask_port_manager(app_name: str = "亚马逊图片上传图床工具", preferred_port: int = 5000) -> FlaskPortManager:
    """
    🏭 创建Flask端口管理器实例
    
    Args:
        app_name: 应用名称
        preferred_port: 首选端口
        
    Returns:
        FlaskPortManager: 端口管理器实例
    """
    return FlaskPortManager(app_name, preferred_port)


def get_available_port(preferred_port: int = 5000) -> int:
    """
    🎯 快速获取可用端口
    
    Args:
        preferred_port: 首选端口
        
    Returns:
        int: 可用端口
    """
    manager = PortManager(preferred_port)
    return manager.allocate_port()


if __name__ == "__main__":
    # 🧪 测试端口管理器
    print("🧪 测试端口管理器...")
    
    manager = FlaskPortManager("测试应用", 5000)
    
    # 测试端口检测
    print(f"端口5000可用: {manager.is_port_available(5000)}")
    print(f"端口80可用: {manager.is_port_available(80)}")
    
    # 测试端口分配
    try:
        port = manager.allocate_port()
        print(f"分配的端口: {port}")
        
        # 显示访问地址
        urls = manager.get_access_urls(port)
        print("访问地址:")
        for url in urls:
            print(f"  - {url}")
            
    except Exception as e:
        print(f"测试失败: {e}") 