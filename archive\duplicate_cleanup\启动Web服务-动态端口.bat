@echo off
chcp 65001 >nul
title 亚马逊图片上传图床工具 - Web服务 (动态端口)

echo.
echo ==========================================
echo   亚马逊图片上传图床工具 - Web服务启动器
echo   🚀 支持动态端口分配，自动避免端口冲突
echo ==========================================
echo.

:: 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请确保已安装Python并添加到PATH环境变量
    echo    您可以从 https://www.python.org/downloads/ 下载安装Python
    pause
    exit /b 1
)

echo ✅ 已找到Python环境

:: 获取Python版本信息
for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo 📊 Python版本: %PYTHON_VERSION%

:: 检查依赖
echo.
echo 🔍 检查项目依赖...
if not exist requirements.txt (
    echo ⚠️ 未找到requirements.txt文件，跳过依赖检查
) else (
    echo 📦 正在安装依赖，请稍候...
    python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    if %errorlevel% neq 0 (
        echo ⚠️ 部分依赖可能安装失败，但将继续尝试启动服务
    ) else (
        echo ✅ 依赖安装完成
    )
)

:: 检查配置文件
echo.
echo 🔍 检查配置文件...
if not exist config\mohe_config.py (
    if exist config\mohe_config.example.py (
        echo 📝 未找到配置文件，正在从示例创建...
        copy config\mohe_config.example.py config\mohe_config.py >nul
        echo ✅ 已创建配置文件 config\mohe_config.py
        echo    请根据需要修改配置文件中的API密钥等信息
    ) else (
        echo ⚠️ 未找到配置文件和示例文件，服务可能无法正常工作
    )
) else (
    echo ✅ 已找到配置文件 config\mohe_config.py
)

:: 确保必要的目录存在
echo.
echo 🔍 检查必要目录...
if not exist uploads mkdir uploads
if not exist temp mkdir temp
if not exist 历史映射表 mkdir 历史映射表
if not exist 填充后的模板 mkdir 填充后的模板
if not exist data mkdir data
if not exist data\uploads mkdir data\uploads
if not exist output mkdir output
if not exist output\logs mkdir output\logs
echo ✅ 已确认必要目录存在

:: 检查端口管理器
echo.
echo 🔍 检查动态端口管理器...
python -c "from core.port_manager import get_available_port; print('✅ 动态端口管理器可用')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ 动态端口管理器不可用，将使用默认端口
) else (
    echo ✅ 动态端口管理器已准备就绪
)

:: 检查当前端口占用情况
echo.
echo 🔍 检查端口占用情况...
netstat -an | findstr :5000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ 端口5000已被占用，将自动分配其他可用端口
    netstat -ano | findstr :5000 | findstr LISTENING
) else (
    echo ✅ 端口5000可用
)

:: 启动Web服务
echo.
echo 🚀 正在启动Web服务...
echo ===========================================
echo   ✨ 动态端口分配功能：
echo   ├─ 自动检测可用端口
echo   ├─ 避免端口冲突
echo   ├─ 显示所有访问地址
echo   └─ 自动打开浏览器
echo   
echo   💡 服务启动后将显示实际访问地址
echo   📱 支持局域网访问
echo   🛑 按Ctrl+C可以停止服务
echo ===========================================
echo.

:: 启动Python Web服务
python web_app.py

:: 如果Python服务退出，显示提示
echo.
echo 👋 Web服务已停止运行
echo.
echo 💡 提示:
echo    ├─ 如果遇到端口冲突，程序已自动处理
echo    ├─ 如果需要指定端口，请修改web_app.py
echo    └─ 查看日志获取更多信息
echo.
pause 