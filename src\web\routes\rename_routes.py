#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图片重命名功能路由
"""

import os
import uuid
import tempfile
import shutil
from flask import request, jsonify, send_file
from werkzeug.utils import secure_filename

# 导入核心功能
try:
    from src.core.image_renamer import process_rename_task, analyze_color_matching
except ImportError:
    # 尝试相对导入
    try:
        from ...core.image_renamer import process_rename_task, analyze_color_matching
    except ImportError:
        print("⚠️ 无法导入图片重命名模块，请确保模块已正确安装")

def register_rename_routes(app):
    """注册重命名功能相关路由"""
    
    @app.route('/api/rename-images', methods=['POST'])
    def api_rename_images():
        """处理图片重命名请求"""
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 获取上传的Excel文件
            if 'excel_file' not in request.files:
                return jsonify({'error': '未找到Excel文件'}), 400
                
            excel_file = request.files['excel_file']
            if not excel_file.filename:
                return jsonify({'error': '未选择Excel文件'}), 400
                
            # 获取上传的图片文件
            if 'image_folder' not in request.files:
                return jsonify({'error': '未找到图片文件夹'}), 400
                
            # 创建临时目录
            temp_dir = tempfile.mkdtemp(prefix=f"rename_task_{task_id}_")
            excel_path = os.path.join(temp_dir, secure_filename(excel_file.filename))
            image_dir = os.path.join(temp_dir, 'images')
            os.makedirs(image_dir, exist_ok=True)
            
            # 保存Excel文件
            excel_file.save(excel_path)
            
            # 保存图片文件
            for file in request.files.getlist('image_folder'):
                if file.filename:
                    relative_path = file.filename.replace('\\', '/').lstrip('/')
                    target_path = os.path.join(image_dir, relative_path)
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    file.save(target_path)
            
            # 获取处理选项
            options = {
                'process_main': 'process_main' in request.form,
                'process_scene': 'process_scene' in request.form,
                'process_swatch': 'process_swatch' in request.form,
                'scene_generic': 'scene_generic' in request.form
            }
            
            # 处理重命名任务
            success, message, zip_memory_file = process_rename_task(
                task_id, excel_path, image_dir, options
            )
            
            if not success:
                return jsonify({'error': message}), 400
                
            # 返回处理后的ZIP文件
            return send_file(
                zip_memory_file,
                mimetype='application/zip',
                as_attachment=True,
                download_name=f"renamed_images_{task_id}.zip"
            )
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'处理失败: {str(e)}'}), 500
            
    @app.route('/api/test-color-matching', methods=['POST'])
    def api_test_color_matching():
        """测试颜色匹配功能"""
        try:
            # 获取上传的Excel文件
            if 'excel_file' not in request.files:
                return jsonify({'error': '未找到Excel文件'}), 400
                
            excel_file = request.files['excel_file']
            if not excel_file.filename:
                return jsonify({'error': '未选择Excel文件'}), 400
                
            # 获取上传的图片文件
            if 'image_folder' not in request.files:
                return jsonify({'error': '未找到图片文件夹'}), 400
                
            # 创建临时目录
            temp_dir = tempfile.mkdtemp(prefix="color_match_test_")
            excel_path = os.path.join(temp_dir, secure_filename(excel_file.filename))
            image_dir = os.path.join(temp_dir, 'images')
            os.makedirs(image_dir, exist_ok=True)
            
            # 保存Excel文件
            excel_file.save(excel_path)
            
            # 保存图片文件
            for file in request.files.getlist('image_folder'):
                if file.filename:
                    relative_path = file.filename.replace('\\', '/').lstrip('/')
                    target_path = os.path.join(image_dir, relative_path)
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    file.save(target_path)
            
            # 分析颜色匹配
            result = analyze_color_matching(excel_path, image_dir)
            
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
                
            return jsonify(result)
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'测试失败: {str(e)}'}), 500 