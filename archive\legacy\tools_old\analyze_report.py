#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析商品分类报告文件结构
"""

import pandas as pd
import os

def analyze_report_file():
    report_path = r'D:\华为家庭存储\工作\运营\Listing相关\Listing\US\分类商品报告+05-27-2025.xlsm'
    
    if not os.path.exists(report_path):
        print('❌ 文件不存在')
        return
    
    print('📋 文件存在，开始分析...')
    print(f'文件路径: {report_path}')
    
    try:
        # 读取Excel文件信息
        excel_file = pd.ExcelFile(report_path, engine='openpyxl')
        print(f'工作表列表: {excel_file.sheet_names}')
        
        # 分析每个可能的表头位置
        for i in range(5):
            print(f'\n=== Header={i} (第{i+1}行作为表头) ===')
            try:
                df = pd.read_excel(report_path, header=i, nrows=5, engine='openpyxl')
                print(f'读取成功！')
                print(f'列数: {len(df.columns)}')
                print(f'行数: {len(df)}')
                print(f'列名: {list(df.columns)}')
                
                # 检查SKU相关列
                sku_columns = ['item_sku', 'sku', 'ITEM_SKU', 'SKU', 'Item_Sku', 'MSKU', 'msku']
                found_sku = []
                for col in df.columns:
                    col_str = str(col).strip()
                    if any(sku_name.lower() in col_str.lower() for sku_name in sku_columns):
                        found_sku.append(col_str)
                
                if found_sku:
                    print(f'🎯 找到SKU相关列: {found_sku}')
                else:
                    print('❌ 未找到SKU相关列')
                
                if len(df) > 0:
                    print(f'第一行数据样例: {df.iloc[0].tolist()[:5]}...')
                    
            except Exception as e:
                print(f'读取失败: {e}')
        
        # 尝试读取所有工作表
        print(f'\n=== 所有工作表分析 ===')
        for sheet_name in excel_file.sheet_names:
            print(f'\n--- 工作表: {sheet_name} ---')
            try:
                df = pd.read_excel(report_path, sheet_name=sheet_name, header=0, nrows=3, engine='openpyxl')
                print(f'列数: {len(df.columns)}')
                print(f'列名: {list(df.columns)[:10]}...')
            except Exception as e:
                print(f'读取失败: {e}')
                
    except Exception as e:
        print(f'❌ 分析失败: {e}')

if __name__ == '__main__':
    analyze_report_file() 