# -*- coding: utf-8 -*-
"""
图片上传模块
提供图片上传到图床的核心功能
"""

import os
import sys
import time
import random
import requests
import threading
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from .utils import retry_on_failure, determine_image_type, extract_asin
from .image_optimizer import ImageOptimizer

# 设置日志
logger = logging.getLogger(__name__)

class ImageUploader:
    """图片上传处理器"""
    
    def __init__(self, api_key, api_url, file_param_name="uploadedFile", 
                 upload_mode="file", upload_format="file", max_workers=10, 
                 upload_delay=0.1, max_retries=3):
        """
        初始化图片上传器
        
        参数:
            api_key (str): API密钥
            api_url (str): API上传地址
            file_param_name (str): 文件参数名
            upload_mode (str): 上传模式
            upload_format (str): 上传格式
            max_workers (int): 最大并发线程数
            upload_delay (float): 上传延迟（秒）
            max_retries (int): 最大重试次数
        """
        self.api_key = api_key
        self.api_url = api_url
        self.file_param_name = file_param_name
        self.upload_mode = upload_mode
        self.upload_format = upload_format
        self.max_workers = max_workers
        self.upload_delay = upload_delay
        self.max_retries = max_retries
        
        # 初始化图片优化器
        self.optimizer = ImageOptimizer()
        
        # 创建session和锁
        self.session = self._create_session()
        self.upload_lock = threading.Lock()
    
    def _create_session(self):
        """创建带有重试策略的session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 配置连接池
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.max_workers,
            pool_maxsize=self.max_workers,
            pool_block=False
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    @retry_on_failure(max_retries=3, delay=1.0)
    def upload_image(self, file_path, upload_folder=None, optimize=False):
        """
        上传图片到图床
        
        参数:
            file_path (str): 图片文件路径
            upload_folder (str, optional): 上传文件夹
            optimize (bool): 是否优化图片
            
        返回:
            str: 上传成功返回URL，失败返回None
        """
        # 检查文件大小
        file_size = os.path.getsize(file_path) / (1024 * 1024)
        if file_size > 8:
            logger.warning(f"文件 {os.path.basename(file_path)} 大小为 {file_size:.2f}MB，超过8MB可能导致上传失败")
        
        try:
            # 准备上传数据
            data = {
                "api_token": self.api_key,
                "upload_format": self.upload_format,
                "mode": self.upload_mode,
                "protocol_type": "http"  # 返回HTTP协议的链接
            }
            
            if upload_folder:
                data["uploadPath"] = upload_folder
            
            # 准备文件数据
            if optimize:
                # 使用优化后的图片数据
                image_data = self.optimizer.optimize_image(file_path)
                files = {self.file_param_name: (os.path.basename(file_path), image_data, 'image/jpeg')}
            else:
                # 使用原始文件
                with open(file_path, 'rb') as file:
                    files = {self.file_param_name: (os.path.basename(file_path), file, 'image/jpeg')}
            
            logger.info(f"开始上传: {os.path.basename(file_path)}")
            
            # 上传文件（使用锁避免并发冲突）
            with self.upload_lock:
                response = self.session.post(self.api_url, data=data, files=files, timeout=30)
            
            # 记录响应信息
            logger.debug(f"上传响应状态码: {response.status_code}")
            logger.debug(f"上传响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"上传响应内容: {response.text}")
            
            if response.status_code == 200:
                return self._parse_upload_response(response)
            else:
                self._handle_upload_error(response, file_path)
                return None
                
        except Exception as e:
            logger.error(f"上传文件 {file_path} 时发生错误: {str(e)}")
            raise
    
    def _parse_upload_response(self, response):
        """解析上传响应，提取图片URL"""
        try:
            result = response.json()
            
            # 解析不同格式的响应
            if 'url' in result:
                return result['url']
            elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                return result['data']['url']
            elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                return result['data']
            else:
                logger.warning(f"上传成功但未返回URL: {response.text}")
                return response.text if 'http' in response.text else None
                
        except Exception as e:
            logger.error(f"解析响应时出错: {str(e)}")
            return response.text if 'http' in response.text else None
    
    def _handle_upload_error(self, response, file_path):
        """处理上传错误"""
        logger.error(f"上传失败: {file_path}, 状态码: {response.status_code}")
        logger.error(f"错误响应: {response.text}")
        
        # API速率限制检查
        if response.status_code == 429:  # Too Many Requests
            wait_time = 60 + random.randint(1, 30)
            logger.warning(f"达到API速率限制，等待{wait_time}秒...")
            time.sleep(wait_time)
            raise requests.exceptions.RequestException("API速率限制")
    
    def upload_with_custom_name(self, file_path, custom_name, upload_folder=None):
        """
        使用自定义文件名上传图片
        
        参数:
            file_path (str): 原始文件路径
            custom_name (str): 自定义文件名
            upload_folder (str, optional): 上传文件夹
            
        返回:
            str: 上传成功返回URL，失败返回None
        """
        try:
            data = {
                "api_token": self.api_key,
                "upload_format": self.upload_format,
                "mode": self.upload_mode,
                "protocol_type": "http"
            }
            
            if upload_folder:
                data["uploadPath"] = upload_folder
            
            with open(file_path, 'rb') as file:
                image_data = file.read()
                files = {self.file_param_name: (custom_name, image_data, 'image/jpeg')}
            
            logger.info(f"开始上传: {custom_name}")
            
            with self.upload_lock:
                response = self.session.post(self.api_url, data=data, files=files, timeout=30)
            
            if response.status_code == 200:
                return self._parse_upload_response(response)
            else:
                self._handle_upload_error(response, custom_name)
                return None
                
        except Exception as e:
            logger.error(f"上传文件 {custom_name} 时发生错误: {str(e)}")
            return None
    
    def batch_upload(self, file_paths, upload_folder=None, optimize=False, 
                    progress_callback=None):
        """
        批量上传图片
        
        参数:
            file_paths (list): 文件路径列表
            upload_folder (str, optional): 上传文件夹
            optimize (bool): 是否优化图片
            progress_callback (callable, optional): 进度回调函数
            
        返回:
            dict: {ASIN: [图片信息列表]}
        """
        results = {}
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            try:
                # 提取文件信息
                filename = os.path.basename(file_path)
                asin = extract_asin(filename)
                image_type = determine_image_type(filename)
                
                if not asin:
                    logger.warning(f"无法从文件名 {filename} 中提取ASIN，跳过")
                    continue
                
                # 上传图片
                url = self.upload_image(file_path, upload_folder, optimize)
                
                if url:
                    # 保存结果
                    if asin not in results:
                        results[asin] = []
                    
                    results[asin].append({
                        'filename': filename,
                        'type': image_type,
                        'url': url
                    })
                    
                    logger.info(f"✅ 上传成功: {filename} -> {url}")
                else:
                    logger.error(f"❌ 上传失败: {filename}")
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(i + 1, total_files, filename)
                
                # 添加上传延迟
                if i < total_files - 1:  # 最后一个文件不需要延迟
                    time.sleep(self.upload_delay)
                    
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时发生错误: {str(e)}")
                continue
        
        return results
    
    def test_connection(self):
        """
        测试API连接
        
        返回:
            dict: 测试结果
        """
        try:
            # 创建测试数据
            test_data = {
                "api_token": self.api_key,
                "upload_format": self.upload_format,
                "mode": self.upload_mode
            }
            
            # 发送测试请求（不包含文件）
            response = self.session.post(self.api_url, data=test_data, timeout=10)
            
            return {
                'success': True,
                'status_code': response.status_code,
                'response': response.text,
                'message': 'API连接正常' if response.status_code in [200, 400] else 'API连接异常'
            }
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': '连接超时',
                'message': 'API连接超时，请检查网络连接'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': '连接错误',
                'message': 'API连接失败，请检查API地址是否正确'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'API测试失败: {str(e)}'
            }

# 导入配置创建默认上传器实例
try:
    import sys
    import os
    
    # 添加config目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(os.path.dirname(os.path.dirname(current_dir)), 'config')
    if config_dir not in sys.path:
        sys.path.insert(0, config_dir)
    
    from mohe_config import API_KEY, API_URL, FILE_PARAM_NAME, UPLOAD_MODE, UPLOAD_FORMAT
    
    # 创建默认上传器实例
    default_uploader = ImageUploader(
        api_key=API_KEY,
        api_url=API_URL,
        file_param_name=FILE_PARAM_NAME,
        upload_mode=UPLOAD_MODE,
        upload_format=UPLOAD_FORMAT
    )
    
except ImportError:
    # 如果配置导入失败，创建空的默认实例
    default_uploader = ImageUploader(
        api_key="",
        api_url="https://s3.x914.com/tempofan/api/upload/",
        file_param_name="uploadedFile",
        upload_mode="file",
        upload_format="file"
    )

# 兼容性函数和变量 - 为了保持与旧版本Web应用的兼容性
def upload_image_to_mohecdn(file_path, upload_folder=None, optimize=False):
    """上传图片到魔河CDN的兼容性函数"""
    return default_uploader.upload_image(file_path, upload_folder, optimize)

# 兼容性变量
session = default_uploader.session
upload_lock = default_uploader.upload_lock 