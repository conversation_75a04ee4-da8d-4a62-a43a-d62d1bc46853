#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
亚马逊模板填充功能 - Web版本
提供Web接口的模板填充功能
"""

import os
import sys
import pandas as pd
import logging
import time
import traceback
from datetime import datetime
from collections import Counter
import shutil
import tempfile
import xlwings as xw
from openpyxl import load_workbook

# 设置日志
logger = logging.getLogger(__name__)

# 按照文档定义的字段映射关系
SECOND_ROW_HEADER_MAPPING = {
    # 模板字段 → 分类商品报告第二行表头字段（去除"(US)"和"USD"后缀模糊匹配）
    'standard_price': 'Your Price',               # 第二行: Your Price USD (US)
    'sale_price': 'Sale Price',                   # 第二行: Sale Price USD (US)  
    'sale_from_date': 'Sale Start Date',          # 第二行: Sale Start Date (US)
    'sale_end_date': 'Sale End Date',             # 第二行: Sale End Date (US)
    'offering_release_date': 'Offering Release Date',  # 第二行: Offering Release Date (US)
    'offering_end_date': 'Stop Selling Date'     # 第二行: Stop Selling Date (US)
}

# 图片字段映射（完整的10个字段）
IMAGE_FIELD_MAPPING = {
    'main_image_url': 'main_image_url',           # 主图
    'other_image_url1': 'other_image_url1',       # 副图1
    'other_image_url2': 'other_image_url2',       # 副图2
    'other_image_url3': 'other_image_url3',       # 副图3
    'other_image_url4': 'other_image_url4',       # 副图4
    'other_image_url5': 'other_image_url5',       # 副图5
    'other_image_url6': 'other_image_url6',       # 副图6
    'other_image_url7': 'other_image_url7',       # 副图7
    'other_image_url8': 'other_image_url8',       # 副图8
    'swatch_image_url': 'swatch_image_url'        # 样本图
}

# 单位字段映射（从产品资料表动态获取）
UNIT_FIELD_MAPPING = {
    # 🔧 修复：item尺寸单位字段不使用产品资料表，始终使用固定的CM单位
    # 'item_length_unit_of_measure': '包装规格单位',  # 注释掉，使用固定的CM单位
    # 'item_width_unit_of_measure': '包装规格单位',  # 注释掉，使用固定的CM单位
    # 'item_height_unit_of_measure': '包装规格单位',  # 注释掉，使用固定的CM单位
    # 🔧 修复：边缘尺寸单位不使用产品资料表，直接使用市场默认值
    # 'length_longer_edge_unit_of_measure': '包装规格单位',  # 注释掉，使用市场默认值
    # 'width_shorter_edge_unit_of_measure': '包装规格单位',  # 注释掉，使用市场默认值
    # 🔧 修复：package尺寸单位字段不使用产品资料表，直接使用市场默认值
    # 'package_length_unit_of_measure': '包装规格单位',  # 注释掉，使用市场默认值
    # 'package_height_unit_of_measure': '包装规格单位',  # 注释掉，使用市场默认值
    # 'package_width_unit_of_measure': '包装规格单位',  # 注释掉，使用市场默认值
    
    # 🔧 修复：package重量单位字段不使用产品资料表，直接使用市场默认值
    # 'package_weight_unit_of_measure': '单品毛重单位',  # 注释掉，使用市场默认值
    # 🔧 修复：item_weight_unit_of_measure不使用产品资料表，始终使用固定的KG单位
    # 'item_weight_unit_of_measure': '单品毛重单位'  # 注释掉，使用固定的KG单位
}

# 忽略的模板字段
IGNORED_TEMPLATE_FIELDS = [
    'currency', 'fulfillment_latency', 'map_price',
    'offering_end_date', 'offering_start_date', 'package_contains_identifier',
    'package_contains_quantity', 'package_level', 'product_site_launch_date',
    'quantity', 'restock_date', 'target_audience_keywords', 'merchant_shipping_group_name',
    'max_order_quantity', 'part_number', "are_batteries_included"
    
]

# 默认值配置
def get_default_values(market='US'):
    """根据市场获取默认值"""
    base_defaults = {        
        'batteries_required': 'No',
        'supplier_declared_dg_hz_regulation1': 'Not Applicable',
        'supplier_declared_dg_hz_regulation2': 'Not Applicable',
        'supplier_declared_dg_hz_regulation3': 'Not Applicable', 
        'supplier_declared_dg_hz_regulation4': 'Not Applicable',
        'supplier_declared_dg_hz_regulation5': 'Not Applicable',
        'cpsia_cautionary_statement': 'NoWarningApplicable',
        'offering_can_be_giftwrapped': 'Yes',
        'offering_can_be_gift_messaged': 'No',
        'country_of_origin': 'CHINA',
        'warranty_description': '1-year warranty against defects.',
        'number_of_items': '1',
        'number_of_boxes': '1',
        'external_product_id_type': 'ASIN',
        'update_delete': 'Update',  # 新增：固定默认值
        'relationship_type': 'Variation',  # 新增：默认值
        'is_expiration_dated_product': 'No',# 新增：默认值
        'unit_count_type': 'Foot',# 新增：默认值
        'is_assembly_required': 'No',# 新增：默认值   
        'parent_child': 'Child',# 新增：默认值   
        'condition_type': 'New',# 新增：默认值
        # 通用单位设置（不区分市场）
        'item_length_unit_of_measure': 'CM',
        'item_width_unit_of_measure': 'CM',
        'item_height_unit_of_measure': 'CM',
        'item_weight_unit_of_measure': 'KG'
    }
    
    # 根据市场设置履约中心ID和其他特定单位
    if market == 'US':
        base_defaults['fulfillment_center_id'] = 'AMAZON_NA'
        # 🔧 修改：统一单位设置，不区分市场
        base_defaults.update({
            'length_longer_edge_unit_of_measure': 'Inches',  # 边缘尺寸统一Inches
            'width_shorter_edge_unit_of_measure': 'Inches',  # 边缘尺寸统一Inches
            'package_weight_unit_of_measure': 'KG',          # 重量统一KG
            'package_length_unit_of_measure': 'CM',          # 其他尺寸统一CM
            'package_height_unit_of_measure': 'CM',          # 其他尺寸统一CM
            'package_width_unit_of_measure': 'CM'            # 其他尺寸统一CM
        })
    elif market == 'UK':
        base_defaults['fulfillment_center_id'] = 'AMAZON_EU'
        # 🔧 修改：统一单位设置，不区分市场
        base_defaults.update({
            'length_longer_edge_unit_of_measure': 'Inches',  # 边缘尺寸统一Inches
            'width_shorter_edge_unit_of_measure': 'Inches',  # 边缘尺寸统一Inches
            'package_weight_unit_of_measure': 'KG',          # 重量统一KG
            'package_length_unit_of_measure': 'CM',          # 其他尺寸统一CM
            'package_height_unit_of_measure': 'CM',          # 其他尺寸统一CM
            'package_width_unit_of_measure': 'CM'            # 其他尺寸统一CM
        })
    
    return base_defaults

def get_dropdown_default_values(template_path):
    """
    从Excel模板中获取下拉选项的唯一值作为默认值
    主要用于feed_product_type和brand_name字段
    """
    dropdown_defaults = {}
    
    try:
        import signal
        import openpyxl
        
        print("🔍 快速获取Excel下拉选项唯一值...")
        
        # 设置超时机制（5秒）
        def timeout_handler(signum, frame):
            raise TimeoutError("获取下拉选项超时")
        
        # 只在非Windows系统使用signal（Windows不支持SIGALRM）
        use_timeout = hasattr(signal, 'SIGALRM')
        if use_timeout:
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(5)  # 5秒超时
        
        try:
            # 打开Excel文件（只读模式，提高性能）
            wb = openpyxl.load_workbook(template_path, read_only=True, data_only=True)
            
            # 获取Template工作表
            if 'Template' not in wb.sheetnames:
                print("⚠️ 未找到Template工作表，跳过下拉选项获取")
                return dropdown_defaults
            
            ws = wb['Template']
            
            # 快速获取第3行的表头（限制列数，避免过度处理）
            header_row = 3
            max_cols = min(50, ws.max_column)  # 限制最多检查50列
            headers = {}
            
            for col in range(1, max_cols + 1):
                try:
                    cell_value = ws.cell(row=header_row, column=col).value
                    if cell_value:
                        header_name = str(cell_value).strip().lower()
                        if header_name == 'feed_product_type':  # 只查找feed_product_type，brand_name改为从图片映射表获取
                            headers[header_name] = col
                            print(f"✅ 找到目标字段 {header_name} 在第 {col} 列")
                except:
                    continue
            
            # 如果没有找到目标字段，快速退出
            if not headers:
                print("⚠️ 未找到目标字段 feed_product_type，跳过")
                wb.close()
                return dropdown_defaults
            
            # 极速获取第一个有效值（找到一个就立即使用）
            for field, col_idx in headers.items():
                try:
                    first_valid_value = None
                    max_rows = min(header_row + 10, ws.max_row + 1)  # 只检查前10行
                    
                    # 找到第一个有效值就立即使用
                    for row in range(header_row + 1, max_rows):
                        try:
                            cell_value = ws.cell(row=row, column=col_idx).value
                            if cell_value and str(cell_value).strip():
                                value = str(cell_value).strip()
                                if len(value) > 0 and value.lower() not in ['none', 'null', '']:
                                    first_valid_value = value
                                    print(f"✅ {field} 找到第一个有效值: {first_valid_value} (第{row}行)")
                                    break  # 找到第一个有效值就立即退出
                        except:
                            continue
                    
                    if first_valid_value:
                        dropdown_defaults[field] = first_valid_value
                        print(f"🎯 {field} 使用第一个有效值作为所有行的默认值")
                    else:
                        print(f"⚠️ {field} 未找到有效数据，使用备用默认值")
                        # 使用备用默认值
                        if field == 'feed_product_type':
                            dropdown_defaults[field] = 'Home'
                        
                except Exception as e:
                    print(f"⚠️ 处理字段 {field} 时出错，使用备用默认值: {str(e)}")
                    # 使用备用默认值
                    if field == 'feed_product_type':
                        dropdown_defaults[field] = 'Home'
            
            wb.close()
            
        finally:
            # 取消超时
            if use_timeout:
                signal.alarm(0)
        
    except TimeoutError:
        print("⚠️ 获取下拉选项超时，使用备用默认值")
        # 使用备用默认值
        dropdown_defaults['feed_product_type'] = 'Home'
    except Exception as e:
        print(f"⚠️ 获取下拉选项时出错，使用备用默认值: {str(e)}")
        # 使用备用默认值
        dropdown_defaults['feed_product_type'] = 'Home'
    
    print(f"✅ 下拉选项获取完成: {dropdown_defaults}")
    return dropdown_defaults

def get_brand_name_from_mapping(mapping_dict):
    """
    从图片映射表获取店铺信息作为brand_name默认值
    brand_name = 图片映射表.店铺
    """
    brand_name = None
    
    try:
        print("🏪 从图片映射表获取店铺信息作为brand_name...")
        
        # 从映射字典中查找"店铺"字段
        for msku, mapping_row in mapping_dict.items():
            if '店铺' in mapping_row:
                shop_value = mapping_row['店铺']
                if shop_value and pd.notna(shop_value) and str(shop_value).strip():
                    shop_val = str(shop_value).strip()
                    if shop_val.lower() not in ['none', 'null', '', 'nan']:
                        brand_name = shop_val
                        print(f"✅ 找到店铺信息: {shop_val}")
                        print(f"🎯 使用店铺 '{brand_name}' 作为brand_name默认值")
                        break
        
        if not brand_name:
            print("⚠️ 图片映射表中未找到店铺信息，使用备用默认值")
            brand_name = 'Generic'
            
    except Exception as e:
        print(f"⚠️ 从图片映射表获取店铺信息时出错: {str(e)}")
        brand_name = 'Generic'
    
    return brand_name

def get_parent_sku_by_skc(report_df, sku_to_skc_mapping):
    """
    基于SKC计算parent_sku的唯一值映射
    只要相同SKC的parent_sku存在唯一值，则其他在商品分类报告不存在的SKU都按照这个唯一值填充
    """
    skc_parent_sku_mapping = {}
    
    try:
        print("🔗 开始计算SKC的parent_sku唯一值映射...")
        
        if report_df is None or len(report_df) == 0 or not sku_to_skc_mapping:
            print("⚠️ 没有报告数据或SKC映射，跳过parent_sku处理")
            return skc_parent_sku_mapping
        
        # 按SKC分组收集parent_sku
        skc_parent_skus = {}
        
        for _, row in report_df.iterrows():
            sku = str(row.get('item_sku', '')).strip().upper()
            parent_sku = None
            
            # 获取parent_sku值
            if 'parent_sku' in row and pd.notna(row['parent_sku']):
                parent_sku = str(row['parent_sku']).strip()
            elif 'parent-sku' in row and pd.notna(row['parent-sku']):
                parent_sku = str(row['parent-sku']).strip()
            
            if sku and parent_sku and sku in sku_to_skc_mapping:
                skc = sku_to_skc_mapping[sku]
                if skc not in skc_parent_skus:
                    skc_parent_skus[skc] = []
                skc_parent_skus[skc].append(parent_sku)
        
        # 为每个SKC计算唯一的parent_sku
        for skc, parent_skus in skc_parent_skus.items():
            unique_parent_skus = list(set(parent_skus))
            if len(unique_parent_skus) == 1:
                skc_parent_sku_mapping[skc] = unique_parent_skus[0]
                print(f"✅ SKC {skc} 的唯一parent_sku: {unique_parent_skus[0]}")
            else:
                print(f"⚠️ SKC {skc} 的parent_sku不唯一: {unique_parent_skus}")
        
        print(f"✅ SKC parent_sku映射完成，共 {len(skc_parent_sku_mapping)} 个SKC有唯一值")
        
    except Exception as e:
        print(f"⚠️ 计算SKC parent_sku映射时出错: {str(e)}")
    
    return skc_parent_sku_mapping

def get_variation_theme_by_skc(mapping_dict, product_df, sku_to_skc_mapping):
    """
    基于SKC和产品资料计算variation_theme
    多个颜色 + 多个宽度或单一宽度 → variation_theme = "color-size"
    仅多个颜色，宽度无 → variation_theme = "Color"
    """
    skc_variation_theme_mapping = {}
    
    try:
        print("🎨 开始计算SKC的variation_theme...")
        
        if not mapping_dict or product_df is None or len(product_df) == 0 or not sku_to_skc_mapping:
            print("⚠️ 缺少必要数据，跳过variation_theme处理")
            return skc_variation_theme_mapping
        
        # 检查产品资料中是否有必要字段
        required_fields = ['宽度（分）（必填）', '颜色(自动）']
        missing_fields = [field for field in required_fields if field not in product_df.columns]
        if missing_fields:
            print(f"⚠️ 产品资料缺少必要字段: {missing_fields}，跳过variation_theme处理")
            return skc_variation_theme_mapping
        
        # 标准化产品资料的SKU字段
        product_df['product_key'] = product_df['*SKU（必填）'].astype(str).str.strip().str.upper()
        
        # 按SKC分组收集颜色和宽度信息
        skc_variations = {}
        
        for msku, mapping_row in mapping_dict.items():
            mapping_sku = mapping_row.get('SKU', None)
            if mapping_sku and pd.notna(mapping_sku):
                mapping_sku_upper = str(mapping_sku).strip().upper()
                
                # 在产品资料中查找匹配的SKU
                matching_products = product_df[product_df['product_key'] == mapping_sku_upper]
                
                if not matching_products.empty:
                    product_row = matching_products.iloc[0]
                    
                    # 获取SKC
                    if mapping_sku_upper in sku_to_skc_mapping:
                        skc = sku_to_skc_mapping[mapping_sku_upper]
                        
                        if skc not in skc_variations:
                            skc_variations[skc] = {'colors': set(), 'widths': set()}
                        
                        # 收集颜色信息
                        color_value = product_row.get('颜色(自动）', None)
                        if color_value and pd.notna(color_value) and str(color_value).strip():
                            color_str = str(color_value).strip()
                            if color_str.lower() not in ['none', 'null', '', 'nan']:
                                skc_variations[skc]['colors'].add(color_str)
                        
                        # 收集宽度信息
                        width_value = product_row.get('宽度（分）（必填）', None)
                        if width_value and pd.notna(width_value) and str(width_value).strip():
                            width_str = str(width_value).strip()
                            if width_str.lower() not in ['none', 'null', '', 'nan']:
                                skc_variations[skc]['widths'].add(width_str)
        
        # 为每个SKC计算variation_theme
        for skc, variations in skc_variations.items():
            color_count = len(variations['colors'])
            width_count = len(variations['widths'])
            
            print(f"🔍 SKC {skc}: 颜色数量={color_count}, 宽度数量={width_count}")
            print(f"   颜色值: {variations['colors']}")
            print(f"   宽度值: {variations['widths']}")
            
            if color_count > 1 and width_count >= 1:
                # 多个颜色 + 多个宽度或单一宽度
                skc_variation_theme_mapping[skc] = 'color-size'
                print(f"✅ SKC {skc} variation_theme: color-size (多颜色+存在宽度)")
            elif color_count > 1 and width_count == 0:
                # 仅多个颜色，宽度无
                skc_variation_theme_mapping[skc] = 'Color'
                print(f"✅ SKC {skc} variation_theme: Color (仅多颜色+无宽度)")
            else:
                print(f"⚠️ SKC {skc} 不满足variation_theme条件，保持默认或空值")
        
        print(f"✅ SKC variation_theme映射完成，共 {len(skc_variation_theme_mapping)} 个SKC有特定主题")
        
    except Exception as e:
        print(f"⚠️ 计算SKC variation_theme映射时出错: {str(e)}")
    
    return skc_variation_theme_mapping

def fuzzy_match_header(target_field, header_list):
    """模糊匹配表头字段（去除后缀）"""
    for header in header_list:
        if not header or pd.isna(header):
            continue
        header_str = str(header).strip()
        # 去除常见后缀进行匹配
        clean_header = header_str.replace('(US)', '').replace('(UK)', '').replace('USD', '').replace('GBP', '').strip()
        if target_field.lower() in clean_header.lower() or clean_header.lower() in target_field.lower():
            return header_str
    return None

def process_skc_aggregation(product_df):
    """处理SKC聚合逻辑"""
    print("\n🔄 开始SKC聚合处理...")
    
    skc_package_info = {}
    sku_to_skc_mapping = {}
    
    if product_df is None or len(product_df) == 0:
        return skc_package_info, sku_to_skc_mapping
    
    # 检查必要列
    required_cols = ['*SKU（必填）', 'SKC', '单品毛重', '包装规格长', '包装规格宽', '包装规格高']
    missing_cols = [col for col in required_cols if col not in product_df.columns]
    if missing_cols:
        print(f"⚠️ 产品资料缺少必要列：{missing_cols}")
        return skc_package_info, sku_to_skc_mapping
    
    # 第一步：收集原始数据
    sku_raw_data = {}
    for idx, row in product_df.iterrows():
        sku = str(row['*SKU（必填）']).strip().upper() if pd.notna(row['*SKU（必填）']) else None
        skc = str(row['SKC']).strip().upper() if pd.notna(row['SKC']) else None
        
        if not sku or not skc:
            continue
        
        # 建立SKU到SKC的映射
        sku_to_skc_mapping[sku] = skc
        
        # 处理包装数据
        package_data = {'skc': skc}
        
        # 处理重量（默认单位为g，需要转换为KG）
        try:
            weight = float(row['单品毛重']) if pd.notna(row['单品毛重']) and str(row['单品毛重']).strip() != "" else None
            if weight is not None and weight > 0:
                # 产品资料的"单品毛重"字段默认单位为g，转换为KG
                converted_weight = convert_weight_g_to_kg(weight)
                package_data['weight'] = converted_weight
                print(f"📦 [SKC聚合] SKU {sku} 重量转换完成")
        except (ValueError, TypeError):
            pass
        
        # 处理尺寸
        try:
            length = float(row['包装规格长']) if pd.notna(row['包装规格长']) and str(row['包装规格长']).strip() != "" else None
            if length is not None and length > 0:
                package_data['length'] = length
        except (ValueError, TypeError):
            pass
        
        try:
            width = float(row['包装规格宽']) if pd.notna(row['包装规格宽']) and str(row['包装规格宽']).strip() != "" else None
            if width is not None and width > 0:
                package_data['width'] = width
        except (ValueError, TypeError):
            pass
        
        try:
            height = float(row['包装规格高']) if pd.notna(row['包装规格高']) and str(row['包装规格高']).strip() != "" else None
            if height is not None and height > 0:
                package_data['height'] = height
        except (ValueError, TypeError):
            pass
        
        sku_raw_data[sku] = package_data
    
    # 第二步：按SKC分组
    skc_groups = {}
    for sku, data in sku_raw_data.items():
        skc = data['skc']
        if skc not in skc_groups:
            skc_groups[skc] = []
        skc_groups[skc].append(data)
    
    # 第三步：为每个SKC计算聚合值
    for skc, data_list in skc_groups.items():
        skc_min_values = {}
        
        # 重量取最小值
        weight_values = [data['weight'] for data in data_list if 'weight' in data and data['weight'] is not None]
        if weight_values:
            skc_min_values['weight'] = min(weight_values)
        
        # 尺寸使用频率最高的组合
        dimension_combos = []
        for data in data_list:
            if all(dim in data and data[dim] is not None for dim in ['length', 'width', 'height']):
                combo = (data['length'], data['width'], data['height'])
                dimension_combos.append(combo)
        
        if dimension_combos:
            combo_counter = Counter(dimension_combos)
            max_count = max(combo_counter.values())
            most_frequent_combos = [combo for combo, count in combo_counter.items() if count == max_count]
            
            if len(most_frequent_combos) == 1:
                chosen_combo = most_frequent_combos[0]
            else:
                # 频率相同时选择体积最小的
                chosen_combo = min(most_frequent_combos, key=lambda x: x[0] * x[1] * x[2])
            
            skc_min_values['length'] = chosen_combo[0]
            skc_min_values['width'] = chosen_combo[1]
            skc_min_values['height'] = chosen_combo[2]
        
        skc_package_info[skc] = skc_min_values
    
    print(f"✅ SKC聚合完成，处理了 {len(skc_package_info)} 个SKC")
    return skc_package_info, sku_to_skc_mapping

def convert_weight_g_to_kg(weight_value):
    """
    将重量从g转换为KG
    产品资料的"单品毛重"字段默认单位为g
    
    参数:
        weight_value (float): 重量数值（单位：g）
        
    返回:
        float: 转换后的KG重量
    """
    if weight_value is None or weight_value == 0:
        return weight_value
    
    # 1g = 0.001kg
    converted_weight = weight_value * 0.001
    print(f"🔄 重量转换: {weight_value} g -> {converted_weight:.3f} KG")
    
    return round(converted_weight, 3)

def get_unit_values(product_df):
    """从产品资料表获取单位值"""
    unit_values = {}
    
    if product_df is None or len(product_df) == 0:
        return unit_values
    
    # 获取包装规格单位
    if '包装规格单位' in product_df.columns:
        unit_value = product_df['包装规格单位'].dropna().iloc[0] if not product_df['包装规格单位'].dropna().empty else None
        if unit_value:
            unit_values['包装规格单位'] = str(unit_value).strip()
    
    # 获取单品毛重单位
    if '单品毛重单位' in product_df.columns:
        unit_value = product_df['单品毛重单位'].dropna().iloc[0] if not product_df['单品毛重单位'].dropna().empty else None
        if unit_value:
            unit_values['单品毛重单位'] = str(unit_value).strip()
    
    return unit_values

def fill_amazon_template_web(template_file, image_mapping_file, classification_file=None):
    """
    Web版亚马逊模板填充功能
    
    参数:
        template_file (str): 亚马逊模板文件路径
        image_mapping_file (str): 图片映射文件路径
        classification_file (str, optional): 商品分类文件路径
        
    返回:
        dict: 填充结果 {'success': bool, 'output_file': str, 'message': str}
    """
    try:
        logger.info("开始执行Web版模板填充")
        
        # 这里可以实现实际的模板填充逻辑
        # 目前返回一个基本的成功结果
        
        output_file = template_file.replace('.xlsx', '_filled.xlsx')
        
        return {
            'success': True,
            'output_file': output_file,
            'message': '模板填充功能正在开发中'
        }
        
    except Exception as e:
        logger.error(f"Web版模板填充失败: {str(e)}")
        return {
            'success': False,
            'output_file': None,
            'message': f'模板填充失败: {str(e)}'
        }

def get_amazon_template_web(template_path, report_path, mapping_path=None, product_info_path=None, output_path=None, market='US', use_product_info=False):
    """
    Web版本：按照文档逻辑填充亚马逊模板
    实现双表头匹配机制、单位字段映射、SKC聚合等完整功能
    """
    print("\n" + "="*60)
    print("🚀 Web版模板填充开始 - 按照文档逻辑实现")
    print("="*60)
    
    start_time = time.time()
    
    try:
        # 步骤1：自动检测并读取商品分类报告
        print("\n📋 步骤1：自动检测并读取商品分类报告...")
        print(f"文件路径：{report_path}")
        print("🔍 支持新旧格式自动检测和兼容")
        
        try:
            # 🆕 自动检测商品分类报告格式
            header_row, sku_field_name, format_info = detect_report_format(report_path)
            print(f"📊 检测到格式：{format_info}")
            
            # 读取第2行表头（价格和日期字段）- 价格字段通常在第2行
            try:
                report_header2_df = pd.read_excel(report_path, sheet_name='Template', header=1, nrows=0, engine='openpyxl')
                header2_columns = report_header2_df.columns.tolist()
                print(f"📊 第2行价格表头包含 {len(header2_columns)} 列：{header2_columns[:5]}...")
            except:
                print("⚠️ 无法读取第2行价格表头，可能不影响主要功能")
            
            # 使用检测到的格式读取报告数据
            report_df = pd.read_excel(report_path, sheet_name='Template', header=header_row, engine='openpyxl')
            print(f"✅ 成功读取报告文件，共 {len(report_df)} 行数据")
            print(f"📊 表头包含 {len(report_df.columns)} 列：{list(report_df.columns)[:5]}...")
            
            # 检查SKU字段是否存在
            if sku_field_name not in report_df.columns:
                available_columns = list(report_df.columns)[:10]
                return False, f"商品分类报告Template工作表未找到{sku_field_name}字段。可用列名：{available_columns}"
            
            # 标准化SKU字段为统一的item_sku命名（保持代码兼容性）
            report_df['item_sku'] = report_df[sku_field_name].astype(str).str.strip().str.upper()
            report_df['item_sku_upper'] = report_df['item_sku']
            
            # 记录原始字段名，便于调试
            report_df['_original_sku_field'] = sku_field_name
            
            sku_count = report_df['item_sku'].notna().sum()
            print(f"📈 有效{sku_field_name}数量：{sku_count}")
            print(f"🔄 已统一映射为item_sku字段，保持代码兼容性")
            
            if sku_count == 0:
                return False, f"商品分类报告中没有有效的{sku_field_name}数据"
                
        except Exception as e:
            return False, f"读取商品分类报告失败：{str(e)}"
        
        # 步骤2：读取图片映射表
        mapping_df = None
        mapping_dict = {}
        mapping_matched = 0
        
        if mapping_path:
            print(f"\n🖼️ 步骤2：读取图片映射表...")
            print(f"文件路径：{mapping_path}")
            print("📊 标准关系：MSKU/SKU字段，第1行表头，第一个工作表")
            
            try:
                excel_file = pd.ExcelFile(mapping_path, engine='openpyxl')
                first_sheet = excel_file.sheet_names[0]
                print(f"📋 使用工作表：{first_sheet}")
                
                mapping_df = pd.read_excel(mapping_path, sheet_name=first_sheet, header=0, engine='openpyxl')
                print(f"✅ 成功读取图片映射表，共 {len(mapping_df)} 行数据")
                
                # 查找映射关键字段 - 使用MSKU字段与商品分类报告的SKU字段进行精确匹配
                mapping_key_field = None
                if 'MSKU' in mapping_df.columns:
                    mapping_key_field = 'MSKU'
                else:
                    return False, f"图片映射表第一个工作表第1行未找到MSKU字段，需要MSKU字段与商品分类报告的{sku_field_name}字段进行匹配"
                
                print(f"🔑 使用映射关键字段：{mapping_key_field}")
                
                # 标准化映射字段 - 使用MSKU字段进行精确匹配
                mapping_df['mapping_key'] = mapping_df[mapping_key_field].astype(str).str.strip().str.upper()
                
                # 创建映射字典 - 基于MSKU字段的精确匹配，清理nan值
                for _, row in mapping_df.iterrows():
                    key = str(row.get('mapping_key', '')).strip().upper()
                    if key:
                        # 清理row数据中的nan值，将nan转换为空字符串
                        clean_row = {}
                        for col, val in row.to_dict().items():
                            if pd.isna(val) or str(val).strip().lower() == 'nan':
                                clean_row[col] = ''
                            else:
                                clean_row[col] = val
                        mapping_dict[key] = clean_row
                
                print(f"🔗 建立MSKU映射字典，包含 {len(mapping_dict)} 个映射关系")
                    
            except Exception as e:
                return False, f"读取图片映射表失败：{str(e)}"
        
        # 步骤3：关键数据筛选
        print(f"\n🎯 步骤3：关键数据筛选...")
        
        if mapping_dict:
            # 根据图片映射表筛选报告数据
            valid_skus = set(mapping_dict.keys())
            report_valid_skus = set(report_df['item_sku_upper'])
            common_skus = valid_skus.intersection(report_valid_skus)
            
            print(f"📊 筛选统计：")
            print(f"  - 图片映射MSKU数量：{len(valid_skus)}")
            print(f"  - 报告{sku_field_name}数量：{len(report_valid_skus)}")
            print(f"  - MSKU与{sku_field_name}精确匹配数量：{len(common_skus)}")
            
            if not common_skus:
                # 提供详细的排查建议
                error_details = []
                error_details.append(f"❌ 图片映射表MSKU与商品分类报告{sku_field_name}没有匹配记录")
                error_details.append("")
                error_details.append("🔍 可能的原因和解决方案：")
                error_details.append("1. 🏪 **店铺不一致** - 最常见原因")
                error_details.append("   - 请确认图片映射表、商品分类报告来自同一个店铺")
                error_details.append("   - 不同店铺的MSKU编码规则可能不同")
                error_details.append("")
                error_details.append("2. 📅 **数据时效性问题**")
                error_details.append("   - 图片映射表和商品分类报告的生成时间差异较大")
                error_details.append("   - 建议使用相近时间生成的文件")
                error_details.append("")
                error_details.append("3. 📄 **文件格式问题**")
                error_details.append("   - 图片映射表：需要MSKU列，第1行表头")
                error_details.append(f"   - 商品分类报告：需要{sku_field_name}列，{format_info}")
                error_details.append("")
                error_details.append("4. 🔤 **数据格式差异**")
                error_details.append(f"   - MSKU/{sku_field_name}编码格式不一致（大小写、空格等）")
                error_details.append("   - 系统已自动处理大小写和空格，但其他格式差异可能影响匹配")
                
                # 显示部分示例数据用于诊断
                if valid_skus:
                    sample_mapping = list(valid_skus)[:5]
                    error_details.append("")
                    error_details.append("📋 图片映射表MSKU示例（前5个）：")
                    for i, sku in enumerate(sample_mapping, 1):
                        error_details.append(f"   {i}. {sku}")
                
                if report_valid_skus:
                    sample_report = list(report_valid_skus)[:5]
                    error_details.append("")
                    error_details.append(f"📋 商品分类报告{sku_field_name}示例（前5个）：")
                    for i, sku in enumerate(sample_report, 1):
                        error_details.append(f"   {i}. {sku}")
                
                error_details.append("")
                error_details.append("💡 建议操作：")
                error_details.append("1. 优先检查所有文件是否来自同一个亚马逊店铺")
                error_details.append("2. 确认文件生成时间相近（建议1周内）")
                error_details.append(f"3. 检查MSKU/{sku_field_name}格式是否一致")
                
                return False, "\\n".join(error_details)
            
            # 筛选报告数据
            original_count = len(report_df)
            report_df = report_df[report_df['item_sku_upper'].isin(common_skus)].copy()
            filtered_count = len(report_df)
            
            print(f"🎯 数据筛选完成：{original_count} -> {filtered_count} 行")
            mapping_matched = filtered_count
        
        # 步骤4：读取产品资料表
        product_df = None
        product_dict = {}
        skc_package_info = {}
        sku_to_skc_mapping = {}
        unit_values = {}
        product_matched = 0
        
        # 🔍 使用MSKU进行精确匹配，图片映射表.MSKU = 商品分类报告.SKU字段
        print(f"🔗 匹配关系：图片映射表.MSKU = 商品分类报告.{sku_field_name}")
        
        if use_product_info and product_info_path:
            print(f"\n📦 步骤4：读取产品资料表...")
            print(f"文件路径：{product_info_path}")
            print("📊 标准关系：*SKU（必填）字段，第1行表头，'单个产品'工作表")
            
            try:
                product_df = pd.read_excel(product_info_path, sheet_name='单个产品', header=0, engine='openpyxl')
                print(f"✅ 成功读取产品资料，共 {len(product_df)} 行数据")
                
                if '*SKU（必填）' not in product_df.columns:
                    return False, f"产品资料'单个产品'工作表第1行未找到*SKU（必填）字段"
                
                # 标准化SKU字段 - 用于与item_sku匹配
                product_df['product_key'] = product_df['*SKU（必填）'].astype(str).str.strip().str.upper()
                
                # 处理MSKU字段（如果存在）- 用于与图片映射表关联
                if 'MSKU' in product_df.columns:
                    product_df['msku_key'] = product_df['MSKU'].astype(str).str.strip().str.upper()
                
                # 处理SKC聚合
                skc_package_info, sku_to_skc_mapping = process_skc_aggregation(product_df)
                
                # 获取单位值
                unit_values = get_unit_values(product_df)
                print(f"📏 单位值：{unit_values}")
                
                # 创建产品字典 - 基于*SKU（必填）字段的精确匹配
                for _, row in product_df.iterrows():
                    key = str(row.get('product_key', '')).strip().upper()
                    if key:
                        product_dict[key] = row.to_dict()
                
                # 计算精确匹配的产品数量
                product_matched = len([sku for sku in report_df['item_sku_upper'] if sku in product_dict])
                print(f"📦 产品资料精确匹配：{product_matched} 个SKU")
                
            except Exception as e:
                print(f"⚠️ 处理产品资料时出错：{str(e)}")
                product_df = None
                product_dict = {}
        
        # 步骤5：分析模板文件结构
        print(f"\n📄 步骤5：分析模板文件结构...")
        
        try:
            temp_dir = tempfile.mkdtemp()
            temp_template = os.path.join(temp_dir, f"temp_template_{int(time.time())}.xlsm")
            shutil.copy2(template_path, temp_template)
            
            excel_file = pd.ExcelFile(temp_template, engine='openpyxl')
            sheet_names = excel_file.sheet_names
            
            if 'Template' not in sheet_names:
                return False, f"模板文件中未找到'Template'工作表"
            
            # 读取模板表头（第3行）
            template_header_df = pd.read_excel(temp_template, sheet_name='Template', engine='openpyxl', header=2, nrows=0)
            template_headers = template_header_df.columns.tolist()
            print(f"📊 模板表头包含 {len(template_headers)} 列")
            
            if 'item_sku' not in template_headers:
                return False, f"模板文件Template工作表第3行未找到item_sku字段"
            
        except Exception as e:
            return False, f"分析模板文件结构失败：{str(e)}"
        
        # 步骤6：数据整合和填充
        print(f"\n🔄 步骤6：数据整合和填充...")
        
        try:
            # 🔍 调试：检查商品分类报告中的parent_sku字段情况
            print(f"\n🔍 [调试] 检查商品分类报告中的parent_sku字段...")
            parent_sku_fields = []
            if 'parent_sku' in report_df.columns:
                parent_sku_fields.append('parent_sku')
            if 'parent-sku' in report_df.columns:
                parent_sku_fields.append('parent-sku')
            
            print(f"🔍 [调试] 找到的parent_sku相关字段: {parent_sku_fields}")
            
            for field in parent_sku_fields:
                non_null_count = report_df[field].notna().sum()
                total_count = len(report_df)
                sample_values = report_df[field].dropna().head(5).tolist()
                print(f"🔍 [调试] 字段 '{field}': 非空值 {non_null_count}/{total_count}, 示例值: {sample_values}")
            
            # 🔍 调试：检查产品资料表中的SPU款名（内部）字段情况
            if product_df is not None:
                print(f"\n🔍 [调试] 检查产品资料表中的SPU款名（内部）字段...")
                if 'SPU款名（内部）' in product_df.columns:
                    spu_non_null_count = product_df['SPU款名（内部）'].notna().sum()
                    spu_total_count = len(product_df)
                    spu_sample_values = product_df['SPU款名（内部）'].dropna().head(5).tolist()
                    spu_unique_values = product_df['SPU款名（内部）'].dropna().unique()
                    print(f"🔍 [调试] SPU款名（内部）字段: 非空值 {spu_non_null_count}/{spu_total_count}")
                    print(f"🔍 [调试] SPU款名（内部）示例值: {spu_sample_values}")
                    print(f"🔍 [调试] SPU款名（内部）唯一值数量: {len(spu_unique_values)}")
                    print(f"🔍 [调试] SPU款名（内部）所有唯一值: {list(spu_unique_values)}")
                else:
                    print(f"❌ [调试] 产品资料表中未找到 'SPU款名（内部）' 字段")
                    print(f"🔍 [调试] 产品资料表可用字段: {list(product_df.columns)}")
            else:
                print(f"⚠️ [调试] 产品资料表为空，无法检查SPU款名（内部）字段")
            
            # 获取默认值
            default_values = get_default_values(market)
            
            # 获取下拉选项默认值并合并
            dropdown_defaults = get_dropdown_default_values(temp_template)
            if dropdown_defaults:
                print(f"📋 合并下拉选项默认值: {dropdown_defaults}")
                default_values.update(dropdown_defaults)
            
            # 从图片映射表获取brand_name（店铺信息）
            if mapping_dict:
                brand_name_from_mapping = get_brand_name_from_mapping(mapping_dict)
                default_values['brand_name'] = brand_name_from_mapping
                print(f"🏪 设置brand_name默认值: {brand_name_from_mapping}")
            
            # 如果有产品资料的单位值，覆盖默认值
            if unit_values:
                for template_field, source_field in UNIT_FIELD_MAPPING.items():
                    if source_field in unit_values:
                        default_values[template_field] = unit_values[source_field]
                        print(f"📏 单位映射：{template_field} = {unit_values[source_field]}")
            
            # 🆕 计算SKC的variation_theme映射
            skc_variation_theme_mapping = {}
            if mapping_dict and product_df is not None and sku_to_skc_mapping:
                skc_variation_theme_mapping = get_variation_theme_by_skc(mapping_dict, product_df, sku_to_skc_mapping)
            
            # 使用xlwings进行高性能填充
            print("⚡ 使用xlwings进行高性能填充...")
            
            app = xw.App(visible=False, add_book=False)
            app.display_alerts = False
            app.screen_updating = False
            
            try:
                wb = app.books.open(temp_template)
                ws = wb.sheets['Template']
                
                # 获取表头行
                header_row = ws.range('A3').expand('right').value
                if not isinstance(header_row, list):
                    header_row = [header_row]
                
                # 清除现有数据（从第4行开始）
                max_row = ws.range('A1').current_region.last_cell.row
                if max_row > 3:
                    ws.range(f'A4:ZZ{max_row}').clear_contents()
                
                # 🎯 关键修复：以图片映射表为基准进行数据填充
                # 确保输出包含图片映射表的所有记录
                batch_data = []
                processed_count = 0
                
                print(f"🎯 [关键修复] 以图片映射表为基准填充，共 {len(mapping_dict)} 条记录")
                
                # 遍历图片映射表的每一条记录
                for msku, mapping_row in mapping_dict.items():
                    print(f"📋 [映射表基准] 处理MSKU: {msku}")
                    
                    # 查找对应的商品分类报告数据
                    matching_reports = report_df[report_df['item_sku_upper'] == msku]
                    
                    if not matching_reports.empty:
                        # 使用商品分类报告的数据
                        row_data = matching_reports.iloc[0].to_dict()
                        print(f"✅ [映射表基准] MSKU {msku} 找到商品分类报告数据")
                    else:
                        # 如果没有找到商品分类报告数据，创建基础记录
                        row_data = {'item_sku': msku}
                        print(f"⚠️ [映射表基准] MSKU {msku} 未找到商品分类报告数据，使用基础记录")
                    
                    sku_val = msku  # 使用MSKU作为主键
                    
                    row_values = [''] * len(header_row)
                    
                    # 特殊字段值收集
                    standard_price_value = None
                    size_name_value = None
                    
                    # 🔧 修复：直接从商品分类报告数据中获取价格字段
                    print(f"🔍 [价格调试] 开始查找价格字段，MSKU: {msku}")
                    
                    # 🔧 修改：只进行精确匹配，不使用模糊匹配
                    price_fields_to_check = ['standard_price', 'your_price', 'list_price']
                    
                    if hasattr(row_data, 'index'):
                        # row_data是pandas Series
                        available_columns = list(row_data.index)
                        print(f"🔍 [价格调试] 可用列名: {available_columns}")
                        
                        for price_field in price_fields_to_check:
                            if price_field in row_data.index and pd.notna(row_data[price_field]):
                                standard_price_value = row_data[price_field]
                                print(f"✅ [价格调试] 精确匹配到价格字段 {price_field}: {standard_price_value}")
                                break
                    else:
                        # row_data是字典
                        available_keys = list(row_data.keys())
                        print(f"🔍 [价格调试] 可用键名: {available_keys}")
                        
                        for price_field in price_fields_to_check:
                            if price_field in row_data and pd.notna(row_data[price_field]):
                                standard_price_value = row_data[price_field]
                                print(f"✅ [价格调试] 精确匹配到价格字段 {price_field}: {standard_price_value}")
                                break
                    
                    if standard_price_value is None:
                        print(f"⚠️ [价格调试] MSKU {msku} 未找到任何价格字段")
                    else:
                        print(f"🎯 [价格调试] 最终获取的价格值: {standard_price_value}")
                    
                    # 🔧 移除旧的复杂第2行表头匹配逻辑，简化处理
                    # for template_field, source_field in SECOND_ROW_HEADER_MAPPING.items():
                    #     matched_header = fuzzy_match_header(source_field, header2_columns)
                    #     if matched_header:
                    #         # 需要从原始Excel中读取第2行表头的数据
                    #         # 这里简化处理，从第3行数据中查找相似字段
                    #         for col in report_df.columns:
                    #             if source_field.lower().replace(' ', '') in str(col).lower().replace(' ', '').replace('(us)', '').replace('usd', ''):
                    #                 if template_field == 'standard_price' and col in row_data:
                    #                     standard_price_value = row_data[col]
                    #                 break
                    
                    # 从第3行表头获取size_name (支持多种字段名变体)
                    print(f"🔍 [size_name调试] 商品分类报告可用字段: {list(row_data.index) if hasattr(row_data, 'index') else list(row_data.keys())}")
                    
                    # 支持多种size相关字段名
                    size_field_candidates = ['size_name', 'Size', 'size', 'SIZE', 'Size Name', 'size name']
                    
                    if hasattr(row_data, 'index'):
                        for field_name in size_field_candidates:
                            if field_name in row_data.index and pd.notna(row_data[field_name]):
                                size_name_value = row_data[field_name]
                                print(f"✅ [size_name调试] 从商品分类报告字段 '{field_name}' 获取: {size_name_value}")
                                break
                    else:
                        for field_name in size_field_candidates:
                            if field_name in row_data and pd.notna(row_data[field_name]):
                                size_name_value = row_data[field_name]
                                print(f"✅ [size_name调试] 从商品分类报告字段 '{field_name}' 获取: {size_name_value}")
                                break
                    
                    if size_name_value is None:
                        print(f"⚠️ [size_name调试] 商品分类报告中未找到任何size相关字段")
                    
                    # 🔍 获取包装信息 - 根据标准映射关系
                    # 标准：图片映射表.SKU = 产品资料表.*SKU（必填）
                    package_values = {}
                    
                    # 从图片映射表获取对应的SKU字段
                    mapping_sku = None
                    if msku in mapping_dict:
                        mapping_data = mapping_dict[msku]
                        mapping_sku = mapping_data.get('SKU', None)
                        if mapping_sku:
                            mapping_sku = str(mapping_sku).strip().upper()  # 标准化
                        print(f"🔍 [包装调试] MSKU {msku} 对应的映射表SKU: {mapping_sku}")
                    
                    if mapping_sku and mapping_sku in sku_to_skc_mapping:
                        print(f"✅ [包装调试] 找到映射表SKU {mapping_sku} 在产品资料中")
                        print(f"🔍 [包装调试] sku_to_skc_mapping总数: {len(sku_to_skc_mapping)}")
                        print(f"🔍 [包装调试] skc_package_info总数: {len(skc_package_info)}")
                        
                        # 🔧 精确匹配逻辑 - 只使用完全相同的SKU
                        if mapping_sku in sku_to_skc_mapping:
                            skc = sku_to_skc_mapping[mapping_sku]
                            print(f"✅ [包装调试] SKU {mapping_sku} 精确匹配到 SKC: {skc}")
                            
                            if skc in skc_package_info:
                                package_info = skc_package_info[skc]
                                print(f"✅ [包装调试] 找到SKC {skc} 的包装信息: {package_info}")
                                package_values = {
                                    'package_height': package_info.get('height'),
                                    'package_length': package_info.get('length'),
                                    'package_weight': package_info.get('weight'),
                                    'package_width': package_info.get('width')
                                }
                                print(f"✅ [包装调试] 最终package_values: {package_values}")
                            else:
                                print(f"❌ [包装调试] SKC {skc} 不在包装信息中")
                        else:
                            print(f"❌ [包装调试] 映射表SKU {mapping_sku} 无法精确匹配到产品资料")
                    else:
                        print(f"⚠️ [包装调试] MSKU {msku} 没有对应的映射表SKU或SKU不在产品资料中")

                    
                    # 填充每一列
                    for i, col_name in enumerate(header_row):
                        if not col_name:
                            continue
                        
                        col_name_lower = str(col_name).lower()
                        
                        # 忽略字段
                        if col_name_lower in [f.lower() for f in IGNORED_TEMPLATE_FIELDS]:
                            continue
                        
                        # 特殊字段处理
                        if col_name_lower == 'list_price':
                            if standard_price_value is not None:
                                row_values[i] = standard_price_value
                                print(f"✅ [list_price] 使用商品分类报告价格: {standard_price_value}")
                            continue
                        
                        # 🆕 standard_price字段处理：同样使用商品分类报告的价格数据
                        if col_name_lower == 'standard_price':
                            if standard_price_value is not None:
                                row_values[i] = standard_price_value
                                print(f"✅ [standard_price] 使用商品分类报告价格: {standard_price_value}")
                            continue
                        
                        if col_name_lower == 'size_map':
                            # 🔧 修复：size_map字段完全复用size_name的计算逻辑
                            final_size_map_value = None
                            
                            # 第一优先级：使用商品分类报告的size_name值
                            if size_name_value is not None and pd.notna(size_name_value):
                                size_name_str = str(size_name_value).strip()
                                if size_name_str and size_name_str.lower() not in ['nan', '', 'none', 'null']:
                                    final_size_map_value = size_name_str
                                    print(f"✅ [size_map] 优先使用商品分类报告的size_name值: {final_size_map_value}")
                            
                            # 第二优先级：如果商品分类报告没有值，从产品资料表计算（完全复用size_name逻辑）
                            if not final_size_map_value and mapping_sku and product_df is not None:
                                print(f"🔍 [size_map] 商品分类报告无值，从产品资料表计算尺寸")
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    
                                    # 宽度映射表（复用size_name的映射表）
                                    width_mapping = {
                                        '0.5分': {'inch': '1/16', 'mm': '2'},
                                        '1分': {'inch': '1/8', 'mm': '3'},
                                        '1.5分': {'inch': '3/16', 'mm': '5'},
                                        '2分': {'inch': '1/4', 'mm': '6'},
                                        '3分': {'inch': '3/8', 'mm': '10'},
                                        '4分': {'inch': '1/2', 'mm': '13'},
                                        '5分': {'inch': '5/8', 'mm': '16'},
                                        '6分': {'inch': '3/4', 'mm': '19'},
                                        '7分': {'inch': '7/8', 'mm': '22'},
                                        '8分': {'inch': '1', 'mm': '25'},
                                        '12分': {'inch': '1 1/2', 'mm': '38'},
                                        '16分': {'inch': '2', 'mm': '51'},
                                        '20分': {'inch': '2 1/2', 'mm': '64'},
                                        '24分': {'inch': '3', 'mm': '76'},
                                        '28分': {'inch': '3 1/2', 'mm': '89'},
                                        '32分': {'inch': '4', 'mm': '102'},
                                        '48分': {'inch': '6', 'mm': '152'},
                                        '64分': {'inch': '8', 'mm': '203'},
                                        '80分': {'inch': '10', 'mm': '254'}
                                    }
                                    
                                    # 长度映射表（复用size_name的映射表）
                                    length_mapping = {
                                        '3码': {'yd': '3', 'm': '3'},
                                        '5码': {'yd': '5', 'm': '5'},
                                        '6码': {'yd': '6', 'm': '5'},
                                        '7码': {'yd': '7', 'm': '6'},
                                        '10码': {'yd': '10', 'm': '9'},
                                        '11码': {'yd': '11', 'm': '10'},
                                        '12码': {'yd': '12', 'm': '11'},
                                        '15码': {'yd': '15', 'm': '14'},
                                        '20码': {'yd': '20', 'm': '18'},
                                        '22码': {'yd': '22', 'm': '20'},
                                        '25码': {'yd': '25', 'm': '23'},
                                        '27码': {'yd': '27', 'm': '25'},
                                        '50码': {'yd': '50', 'm': '46'},
                                        '100码': {'yd': '100', 'm': '91'}
                                    }
                                    
                                    width_value = None
                                    length_value = None
                                    
                                    # 获取宽度信息
                                    if '宽度（分）（必填）' in product_row and pd.notna(product_row['宽度（分）（必填）']):
                                        width_raw = str(product_row['宽度（分）（必填）']).strip()
                                        if width_raw in width_mapping:
                                            width_value = width_raw
                                            print(f"✅ [size_map] SKU {mapping_sku} 宽度: {width_value}")
                                        else:
                                            print(f"⚠️ [size_map] SKU {mapping_sku} 宽度 '{width_raw}' 未在映射表中")
                                    
                                    # 获取长度信息
                                    if '长度（码）（必填）' in product_row and pd.notna(product_row['长度（码）（必填）']):
                                        length_raw = str(product_row['长度（码）（必填）']).strip()
                                        if length_raw in length_mapping:
                                            length_value = length_raw
                                            print(f"✅ [size_map] SKU {mapping_sku} 长度: {length_value}")
                                        else:
                                            print(f"⚠️ [size_map] SKU {mapping_sku} 长度 '{length_raw}' 未在映射表中")
                                    
                                    # 如果宽度和长度都找到了，根据市场格式化输出
                                    if width_value and length_value:
                                        if market == 'US':
                                            # 美国格式：5/8" x 25Yd
                                            width_spec = width_mapping[width_value]['inch']
                                            length_spec = length_mapping[length_value]['yd']
                                            final_size_map_value = f'{width_spec}" x {length_spec}Yd'
                                            print(f"✅ [size_map] 产品资料表计算-美国格式: {final_size_map_value}")
                                        elif market == 'UK':
                                            # 英国格式：16mm – 23m
                                            width_spec = width_mapping[width_value]['mm']
                                            length_spec = length_mapping[length_value]['m']
                                            final_size_map_value = f'{width_spec}mm – {length_spec}m'
                                            print(f"✅ [size_map] 产品资料表计算-英国格式: {final_size_map_value}")
                                        else:
                                            print(f"⚠️ [size_map] 未知市场 '{market}'，留空")
                                    else:
                                        print(f"⚠️ [size_map] SKU {mapping_sku} 宽度或长度信息不完整")
                                else:
                                    print(f"⚠️ [size_map] SKU {mapping_sku} 在产品资料表中未找到")
                            elif final_size_map_value:
                                print(f"✅ [size_map] 使用商品分类报告的值，跳过产品资料表计算")
                            
                            # 设置最终值
                            if final_size_map_value:
                                row_values[i] = final_size_map_value
                            else:
                                row_values[i] = ''
                                print(f"⚠️ [size_map] 无可用值，留空")
                            continue
                        
                        # manufacturer字段处理：直接从图片映射表获取店铺字段
                        if col_name_lower == 'manufacturer':
                            manufacturer_value = None
                            # 直接从图片映射表获取店铺信息
                            if msku in mapping_dict:
                                mapping_row = mapping_dict[msku]
                                if '店铺' in mapping_row:
                                    shop_value = mapping_row['店铺']
                                    if shop_value and pd.notna(shop_value) and str(shop_value).strip():
                                        shop_name = str(shop_value).strip()
                                        if shop_name.lower() not in ['none', 'null', '', 'nan']:
                                            manufacturer_value = f"{shop_name} Official"
                                            print(f"✅ [manufacturer] 从映射表获取店铺: {shop_name} -> {manufacturer_value}")
                            
                            if manufacturer_value:
                                row_values[i] = manufacturer_value
                            else:
                                print(f"⚠️ [manufacturer] MSKU {msku} 未找到店铺信息，留空")
                                row_values[i] = ''  # 留空
                            continue
                        
                        # 🆕 model字段处理：model = item_sku
                        if col_name_lower == 'model' and sku_val:
                            row_values[i] = str(sku_val).strip()
                            continue
                        
                        # 🆕 model_name字段处理：直接使用填充后的parent_sku去除"-"及之后的内容
                        if col_name_lower == 'model_name':
                            model_name_value = None
                            print(f"\n🔍 [model_name调试] 开始处理MSKU: {msku}")
                            
                            # 🔧 修复：直接使用与parent_sku相同的逻辑获取值，而不是依赖已填充的值
                            parent_sku_source_value = None
                            
                            # 第一步：尝试从商品分类报告获取parent_sku
                            if hasattr(row_data, 'index'):
                                # row_data是pandas Series
                                if 'parent_sku' in row_data.index and pd.notna(row_data['parent_sku']):
                                    val = row_data['parent_sku']
                                    if str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_source_value = str(val).strip()
                                        print(f"✅ [model_name调试] 从商品分类报告parent_sku获取: {parent_sku_source_value}")
                                
                                if not parent_sku_source_value and 'parent-sku' in row_data.index and pd.notna(row_data['parent-sku']):
                                    val = row_data['parent-sku']
                                    if str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_source_value = str(val).strip()
                                        print(f"✅ [model_name调试] 从商品分类报告parent-sku获取: {parent_sku_source_value}")
                            else:
                                # row_data是字典
                                if 'parent_sku' in row_data and pd.notna(row_data['parent_sku']):
                                    val = row_data['parent_sku']
                                    if str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_source_value = str(val).strip()
                                        print(f"✅ [model_name调试] 从商品分类报告parent_sku获取: {parent_sku_source_value}")
                                
                                if not parent_sku_source_value and 'parent-sku' in row_data and pd.notna(row_data['parent-sku']):
                                    val = row_data['parent-sku']
                                    if str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_source_value = str(val).strip()
                                        print(f"✅ [model_name调试] 从商品分类报告parent-sku获取: {parent_sku_source_value}")
                            
                            # 第二步：如果商品分类报告没有，使用SPU款名备用机制
                            if not parent_sku_source_value and product_df is not None:
                                print(f"🔍 [model_name调试] 商品分类报告无parent_sku，启动SPU款名备用机制")
                                
                                # 收集所有目标SKU对应的SPU款名（内部）值
                                spu_names = []
                                
                                for check_msku, check_mapping_row in mapping_dict.items():
                                    check_mapping_sku = check_mapping_row.get('SKU', None)
                                    if check_mapping_sku and pd.notna(check_mapping_sku):
                                        check_mapping_sku_upper = str(check_mapping_sku).strip().upper()
                                        
                                        # 在产品资料中查找匹配的SKU
                                        matching_products = product_df[product_df['product_key'] == check_mapping_sku_upper]
                                        if not matching_products.empty:
                                            product_row = matching_products.iloc[0]
                                            if 'SPU款名（内部）' in product_row and pd.notna(product_row['SPU款名（内部）']):
                                                spu_name = str(product_row['SPU款名（内部）']).strip()
                                                if spu_name and spu_name.lower() not in ['none', 'null', '', 'nan']:
                                                    spu_names.append(spu_name)
                                
                                # 检查SPU款名一致性，如果一致则从商品分类报告查找parent_sku
                                if spu_names:
                                    unique_spu_names = list(set(spu_names))
                                    if len(unique_spu_names) == 1:
                                        print(f"✅ [model_name调试] SPU款名一致，从商品分类报告查找parent_sku")
                                        
                                        # 从商品分类报告中查找有parent_sku值的记录
                                        for _, report_row in report_df.iterrows():
                                            parent_sku_candidates = []
                                            if 'parent_sku' in report_row.index and pd.notna(report_row['parent_sku']):
                                                val = str(report_row['parent_sku']).strip()
                                                if val and val.lower() not in ['nan', '', 'none', 'null']:
                                                    parent_sku_candidates.append(val)
                                            
                                            if 'parent-sku' in report_row.index and pd.notna(report_row['parent-sku']):
                                                val = str(report_row['parent-sku']).strip()
                                                if val and val.lower() not in ['nan', '', 'none', 'null']:
                                                    parent_sku_candidates.append(val)
                                            
                                            if parent_sku_candidates:
                                                parent_sku_source_value = parent_sku_candidates[0]
                                                print(f"✅ [model_name调试] 从商品分类报告备用机制获取: {parent_sku_source_value}")
                                                break
                            
                            # 第三步：处理parent_sku值生成model_name
                            if parent_sku_source_value:
                                # 去除"-"及"-"后面的内容
                                if '-' in parent_sku_source_value:
                                    model_name_value = parent_sku_source_value.split('-')[0].strip()
                                    print(f"🔧 [model_name调试] 去除'-'后内容: {parent_sku_source_value} -> {model_name_value}")
                                else:
                                    model_name_value = parent_sku_source_value
                                    print(f"🔧 [model_name调试] 无需处理: {model_name_value}")
                            
                            if model_name_value:
                                row_values[i] = model_name_value
                                print(f"✅ [model_name调试] 最终填充值: {model_name_value}")
                            else:
                                row_values[i] = ''
                                print(f"⚠️ [model_name调试] MSKU {msku} 留空")
                            continue
                        

                        # 🆕 variation_theme字段处理：基于SKC的颜色和宽度分析
                        if col_name_lower == 'variation_theme':
                            variation_theme_value = None
                            
                            # 首先尝试从报告数据中获取
                            if hasattr(row_data, 'index'):
                                # row_data是pandas Series
                                if 'variation_theme' in row_data.index and pd.notna(row_data['variation_theme']):
                                    variation_theme_value = str(row_data['variation_theme']).strip()
                            else:
                                # row_data是字典
                                if 'variation_theme' in row_data and pd.notna(row_data['variation_theme']):
                                    variation_theme_value = str(row_data['variation_theme']).strip()
                            
                            # 如果报告数据中没有，则使用SKC计算的值
                            if not variation_theme_value and mapping_sku and mapping_sku in sku_to_skc_mapping:
                                skc = sku_to_skc_mapping[mapping_sku]
                                if skc in skc_variation_theme_mapping:
                                    variation_theme_value = skc_variation_theme_mapping[skc]
                                    print(f"✅ [variation_theme] 使用SKC {skc} 计算的值: {variation_theme_value}")
                            
                            if variation_theme_value:
                                row_values[i] = variation_theme_value
                            continue
                        
                        # 🆕 color_name字段处理：从产品资料表.颜色(自动）字段获取
                        if col_name_lower == 'color_name':
                            color_name_value = None
                            
                            # 通过映射表SKU从产品资料表获取颜色信息
                            if mapping_sku and product_df is not None:
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    if '颜色(自动）' in product_row and pd.notna(product_row['颜色(自动）']):
                                        color_value = str(product_row['颜色(自动）']).strip()
                                        if color_value and color_value.lower() not in ['none', 'null', '', 'nan']:
                                            color_name_value = color_value
                                            print(f"✅ [color_name] SKU {mapping_sku} 颜色: {color_name_value}")
                            
                            if color_name_value:
                                row_values[i] = color_name_value
                            else:
                                print(f"⚠️ [color_name] SKU {mapping_sku} 未找到颜色信息")
                                row_values[i] = ''
                            continue
                        
                        # 🆕 color_map字段处理：等于color_name的值
                        if col_name_lower == 'color_map':
                            color_name_value = None
                            
                            # 通过映射表SKU从产品资料表获取颜色信息
                            if mapping_sku and product_df is not None:
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    if '颜色(自动）' in product_row and pd.notna(product_row['颜色(自动）']):
                                        color_value = str(product_row['颜色(自动）']).strip()
                                        if color_value and color_value.lower() not in ['none', 'null', '', 'nan']:
                                            color_name_value = color_value
                                            print(f"✅ [color_map] SKU {mapping_sku} 颜色: {color_name_value}")
                            
                            if color_name_value:
                                row_values[i] = color_name_value
                            else:
                                print(f"⚠️ [color_map] SKU {mapping_sku} 未找到颜色信息")
                                row_values[i] = ''
                            continue
                        
                        # 🆕 unit_count字段处理：从产品资料表获取长度并转换为Foot单位
                        if col_name_lower == 'unit_count':
                            unit_count_value = None
                            
                            # 🔧 前提条件检查：只有当unit_count_type为"Foot"时才进行填充
                            unit_count_type_value = None
                            
                            # 查找unit_count_type字段的值
                            for j, header_col in enumerate(header_row):
                                if header_col and str(header_col).lower() == 'unit_count_type':
                                    # 检查是否已经设置了默认值
                                    if 'unit_count_type' in default_values:
                                        unit_count_type_value = default_values['unit_count_type']
                                        print(f"🔍 [unit_count] 从默认值获取unit_count_type: {unit_count_type_value}")
                                    break
                            
                            # 只有当unit_count_type为"Foot"时才进行填充
                            if unit_count_type_value == 'Foot':
                                print(f"✅ [unit_count] unit_count_type为Foot，开始进行长度转换填充")
                                
                                # 通过映射表SKU从产品资料表获取长度信息
                                if mapping_sku and product_df is not None:
                                    matching_products = product_df[product_df['product_key'] == mapping_sku]
                                    if not matching_products.empty:
                                        product_row = matching_products.iloc[0]
                                        if '长度（码）（必填）' in product_row and pd.notna(product_row['长度（码）（必填）']):
                                            length_raw = str(product_row['长度（码）（必填）']).strip()
                                            print(f"🔍 [unit_count] SKU {mapping_sku} 原始长度字段: {length_raw}")
                                            
                                            # 提取数值部分（例如从"6码"中提取"6"）
                                            import re
                                            match = re.search(r'(\d+(?:\.\d+)?)', length_raw)
                                            if match:
                                                length_yards = float(match.group(1))
                                                print(f"🔍 [unit_count] 提取到码数: {length_yards}")
                                                
                                                # 1码 = 3英尺，所以码数 × 3 = 英尺数
                                                length_feet = length_yards * 3
                                                unit_count_value = int(length_feet)  # 转换为整数
                                                print(f"✅ [unit_count] SKU {mapping_sku} 长度转换: {length_yards}码 -> {length_feet}英尺 -> {unit_count_value}")
                                            else:
                                                print(f"⚠️ [unit_count] SKU {mapping_sku} 无法从 '{length_raw}' 提取数值")
                                        else:
                                            print(f"⚠️ [unit_count] SKU {mapping_sku} 未找到长度（码）（必填）字段")
                                    else:
                                        print(f"⚠️ [unit_count] SKU {mapping_sku} 不在产品资料表中")
                            else:
                                print(f"⚠️ [unit_count] unit_count_type不为Foot (当前值: {unit_count_type_value})，跳过填充")
                            
                            if unit_count_value is not None:
                                row_values[i] = unit_count_value
                            continue
                        
                        # 🆕 pattern_name字段处理：基于印刷(必填）字段判断
                        if col_name_lower == 'pattern_name':
                            pattern_name_value = None
                            
                            # 通过映射表SKU从产品资料表获取印刷信息
                            if mapping_sku and product_df is not None:
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    if '印刷(必填）' in product_row and pd.notna(product_row['印刷(必填）']):
                                        print_value = str(product_row['印刷(必填）']).strip()
                                        
                                        if print_value == '无印刷':
                                            pattern_name_value = 'Solid'
                                            print(f"✅ [pattern_name] SKU {mapping_sku} 无印刷 -> Solid")
                                        elif print_value == '有印刷':
                                            # 获取"元素"列的值
                                            if '元素' in product_row and pd.notna(product_row['元素']):
                                                element_value = str(product_row['元素']).strip()
                                                if element_value and element_value.lower() not in ['none', 'null', '', 'nan']:
                                                    pattern_name_value = element_value
                                                    print(f"✅ [pattern_name] SKU {mapping_sku} 有印刷，元素: {element_value}")
                                        else:
                                            print(f"⚠️ [pattern_name] SKU {mapping_sku} 印刷字段值为: {print_value}，留空")
                            
                            if pattern_name_value:
                                row_values[i] = pattern_name_value
                            else:
                                row_values[i] = ''
                            continue
                        
                        # 🆕 material_type字段处理：基于产品材质（必填）字段映射
                        if col_name_lower == 'material_type':
                            material_type_value = None
                            
                            # 材质映射字典
                            material_mapping = {
                                '纯棉': 'Cotton',
                                '涤纶': 'Polyester',
                                '涤棉': 'Polyester-cotton blend',
                                '棉麻': 'Cotton-linen blend',
                                '尼龙': 'Nylon'
                            }
                            
                            # 通过映射表SKU从产品资料表获取材质信息
                            if mapping_sku and product_df is not None:
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    if '产品材质（必填）' in product_row and pd.notna(product_row['产品材质（必填）']):
                                        material_value = str(product_row['产品材质（必填）']).strip()
                                        
                                        if material_value in material_mapping:
                                            material_type_value = material_mapping[material_value]
                                            print(f"✅ [material_type] SKU {mapping_sku} 材质: {material_value} -> {material_type_value}")
                                        else:
                                            print(f"⚠️ [material_type] SKU {mapping_sku} 材质 '{material_value}' 未在映射表中，留空")
                            
                            if material_type_value:
                                row_values[i] = material_type_value
                            else:
                                row_values[i] = ''
                            continue
                        
                        # 🆕 size_name字段处理：优先使用商品分类报告，备用从产品资料表计算
                        if col_name_lower == 'size_name':
                            # 🔧 修复：不要重置size_name_value，优先使用商品分类报告的值
                            final_size_name_value = None
                            
                            # 第一优先级：使用商品分类报告的size_name值
                            if size_name_value is not None and pd.notna(size_name_value):
                                size_name_str = str(size_name_value).strip()
                                if size_name_str and size_name_str.lower() not in ['nan', '', 'none', 'null']:
                                    final_size_name_value = size_name_str
                                    print(f"✅ [size_name] 优先使用商品分类报告的值: {final_size_name_value}")
                            
                            # 第二优先级：如果商品分类报告没有值，从产品资料表计算
                            
                            # 宽度映射表
                            width_mapping = {
                                '0.5分': {'inch': '1/16', 'mm': '2'},
                                '1分': {'inch': '1/8', 'mm': '3'},
                                '1.5分': {'inch': '3/16', 'mm': '5'},
                                '2分': {'inch': '1/4', 'mm': '6'},
                                '3分': {'inch': '3/8', 'mm': '10'},
                                '4分': {'inch': '1/2', 'mm': '13'},
                                '5分': {'inch': '5/8', 'mm': '16'},
                                '6分': {'inch': '3/4', 'mm': '19'},
                                '7分': {'inch': '7/8', 'mm': '22'},
                                '8分': {'inch': '1', 'mm': '25'},
                                '12分': {'inch': '1 1/2', 'mm': '38'},
                                '16分': {'inch': '2', 'mm': '51'},
                                '20分': {'inch': '2 1/2', 'mm': '64'},
                                '24分': {'inch': '3', 'mm': '76'},
                                '28分': {'inch': '3 1/2', 'mm': '89'},
                                '32分': {'inch': '4', 'mm': '102'},
                                '48分': {'inch': '6', 'mm': '152'},
                                '64分': {'inch': '8', 'mm': '203'},
                                '80分': {'inch': '10', 'mm': '254'}
                            }
                            
                            # 长度映射表
                            length_mapping = {
                                '3码': {'yd': '3', 'm': '3'},
                                '5码': {'yd': '5', 'm': '5'},
                                '6码': {'yd': '6', 'm': '5'},
                                '7码': {'yd': '7', 'm': '6'},
                                '10码': {'yd': '10', 'm': '9'},
                                '11码': {'yd': '11', 'm': '10'},
                                '12码': {'yd': '12', 'm': '11'},
                                '15码': {'yd': '15', 'm': '14'},
                                '20码': {'yd': '20', 'm': '18'},
                                '22码': {'yd': '22', 'm': '20'},
                                '25码': {'yd': '25', 'm': '23'},
                                '27码': {'yd': '27', 'm': '25'},
                                '50码': {'yd': '50', 'm': '46'},
                                '100码': {'yd': '100', 'm': '91'}
                            }
                            
                            # 只有当商品分类报告没有值时，才从产品资料表计算
                            if not final_size_name_value and mapping_sku and product_df is not None:
                                print(f"🔍 [size_name] 商品分类报告无值，从产品资料表计算尺寸")
                                matching_products = product_df[product_df['product_key'] == mapping_sku]
                                if not matching_products.empty:
                                    product_row = matching_products.iloc[0]
                                    
                                    width_value = None
                                    length_value = None
                                    
                                    # 获取宽度信息
                                    if '宽度（分）（必填）' in product_row and pd.notna(product_row['宽度（分）（必填）']):
                                        width_raw = str(product_row['宽度（分）（必填）']).strip()
                                        if width_raw in width_mapping:
                                            width_value = width_raw
                                            print(f"✅ [size_name] SKU {mapping_sku} 宽度: {width_value}")
                                        else:
                                            print(f"⚠️ [size_name] SKU {mapping_sku} 宽度 '{width_raw}' 未在映射表中")
                                    
                                    # 获取长度信息
                                    if '长度（码）（必填）' in product_row and pd.notna(product_row['长度（码）（必填）']):
                                        length_raw = str(product_row['长度（码）（必填）']).strip()
                                        if length_raw in length_mapping:
                                            length_value = length_raw
                                            print(f"✅ [size_name] SKU {mapping_sku} 长度: {length_value}")
                                        else:
                                            print(f"⚠️ [size_name] SKU {mapping_sku} 长度 '{length_raw}' 未在映射表中")
                                    
                                    # 如果宽度和长度都找到了，根据市场格式化输出
                                    if width_value and length_value:
                                        if market == 'US':
                                            # 美国格式：5/8" x 25Yd
                                            width_spec = width_mapping[width_value]['inch']
                                            length_spec = length_mapping[length_value]['yd']
                                            final_size_name_value = f'{width_spec}" x {length_spec}Yd'
                                            print(f"✅ [size_name] 产品资料表计算-美国格式: {final_size_name_value}")
                                        elif market == 'UK':
                                            # 英国格式：16mm – 23m
                                            width_spec = width_mapping[width_value]['mm']
                                            length_spec = length_mapping[length_value]['m']
                                            final_size_name_value = f'{width_spec}mm – {length_spec}m'
                                            print(f"✅ [size_name] 产品资料表计算-英国格式: {final_size_name_value}")
                                        else:
                                            print(f"⚠️ [size_name] 未知市场 '{market}'，留空")
                                    else:
                                        print(f"⚠️ [size_name] SKU {mapping_sku} 宽度或长度信息不完整")
                                else:
                                    print(f"⚠️ [size_name] SKU {mapping_sku} 在产品资料表中未找到")
                            elif final_size_name_value:
                                print(f"✅ [size_name] 使用商品分类报告的值，跳过产品资料表计算")
                            
                            # 设置最终值
                            if final_size_name_value:
                                row_values[i] = final_size_name_value
                            else:
                                row_values[i] = ''
                                print(f"⚠️ [size_name] 无可用值，留空")
                            continue
                        
                        # 包装字段处理
                        if col_name_lower in package_values and package_values[col_name_lower] is not None:
                            value = package_values[col_name_lower]
                            # 🔄 重量字段特殊处理：已在SKC聚合阶段转换为KG
                            if col_name_lower in ['package_weight', 'item_weight']:
                                print(f"💪 [重量字段] {col_name_lower} = {value} KG (已转换)")
                            row_values[i] = value
                            continue
                        
                        # item字段等于package字段
                        if col_name_lower.startswith('item_') and col_name_lower.replace('item_', 'package_') in package_values:
                            package_field = col_name_lower.replace('item_', 'package_')
                            if package_values[package_field] is not None:
                                value = package_values[package_field]
                                # 🔄 重量字段特殊处理：已在SKC聚合阶段转换为KG
                                if col_name_lower == 'item_weight':
                                    print(f"💪 [重量字段] {col_name_lower} = {value} KG (已转换)")
                                row_values[i] = value
                                continue
                        
                        # 🔧 边缘尺寸计算逻辑（严格检查 + 详细调试）
                        if col_name_lower == 'length_longer_edge':
                            print(f"🔍 [调试] 检查length_longer_edge字段计算，SKU: {sku_val}")
                            print(f"🔍 [调试] package_values内容: {package_values}")
                            
                            # 检查所有必需的包装尺寸是否存在
                            required_dimensions = ['package_height', 'package_length', 'package_width']
                            missing_dimensions = []
                            available_dimensions = {}
                            
                            for dim in required_dimensions:
                                if package_values.get(dim) is not None:
                                    try:
                                        available_dimensions[dim] = float(package_values[dim])
                                        print(f"🔍 [调试] {dim}: {available_dimensions[dim]}")
                                    except (ValueError, TypeError) as e:
                                        print(f"🔍 [调试] {dim}数值转换失败: {package_values[dim]}, 错误: {e}")
                                        missing_dimensions.append(dim)
                                else:
                                    missing_dimensions.append(dim)
                                    print(f"🔍 [调试] {dim}: 缺失或为None")
                            
                            # 只有当所有尺寸都存在时才计算
                            if not missing_dimensions and len(available_dimensions) == 3:
                                dimensions_list = list(available_dimensions.values())
                                max_value = max(dimensions_list)
                                # 🔧 新增：从CM转换为inches (1 CM = 0.393701 inches)
                                max_value_inches = max_value * 0.393701
                                row_values[i] = round(max_value_inches, 2)  # 保留2位小数
                                print(f"✅ [调试] 设置length_longer_edge: {max_value}CM → {round(max_value_inches, 2)}inches (来源尺寸: {dimensions_list})")
                            else:
                                print(f"❌ [调试] length_longer_edge计算跳过，缺失尺寸: {missing_dimensions}")
                                print(f"❌ [调试] 可用尺寸数量: {len(available_dimensions)}/3")
                            continue
                        
                        if col_name_lower == 'width_shorter_edge':
                            print(f"🔍 [调试] 检查width_shorter_edge字段计算，SKU: {sku_val}")
                            print(f"🔍 [调试] package_values内容: {package_values}")
                            
                            # 检查所有必需的包装尺寸是否存在
                            required_dimensions = ['package_height', 'package_length', 'package_width']
                            missing_dimensions = []
                            available_dimensions = {}
                            
                            for dim in required_dimensions:
                                if package_values.get(dim) is not None:
                                    try:
                                        available_dimensions[dim] = float(package_values[dim])
                                        print(f"🔍 [调试] {dim}: {available_dimensions[dim]}")
                                    except (ValueError, TypeError) as e:
                                        print(f"🔍 [调试] {dim}数值转换失败: {package_values[dim]}, 错误: {e}")
                                        missing_dimensions.append(dim)
                                else:
                                    missing_dimensions.append(dim)
                                    print(f"🔍 [调试] {dim}: 缺失或为None")
                            
                            # 只有当所有尺寸都存在时才计算
                            if not missing_dimensions and len(available_dimensions) == 3:
                                dimensions_list = list(available_dimensions.values())
                                min_value = min(dimensions_list)
                                # 🔧 新增：从CM转换为inches (1 CM = 0.393701 inches)
                                min_value_inches = min_value * 0.393701
                                row_values[i] = round(min_value_inches, 2)  # 保留2位小数
                                print(f"✅ [调试] 设置width_shorter_edge: {min_value}CM → {round(min_value_inches, 2)}inches (来源尺寸: {dimensions_list})")
                            else:
                                print(f"❌ [调试] width_shorter_edge计算跳过，缺失尺寸: {missing_dimensions}")
                                print(f"❌ [调试] 可用尺寸数量: {len(available_dimensions)}/3")
                            continue
                        
                        # 图片URL字段
                        if col_name_lower in IMAGE_FIELD_MAPPING and mapping_dict:
                            sku_upper = str(sku_val).upper()
                            if sku_upper in mapping_dict:
                                mapping_row = mapping_dict[sku_upper]
                                url_value = mapping_row.get(col_name_lower, '')
                                # 检查URL值是否有效，排除nan、空字符串等
                                if url_value and pd.notna(url_value) and str(url_value).strip() and str(url_value).strip().lower() != 'nan':
                                    row_values[i] = str(url_value).strip()
                                else:
                                    row_values[i] = ''  # 明确设置为空字符串
                            else:
                                row_values[i] = ''  # 没有匹配到MSKU时设置为空字符串
                            continue
                        
                        # 单位字段
                        if col_name_lower in UNIT_FIELD_MAPPING:
                            source_unit_field = UNIT_FIELD_MAPPING[col_name_lower]
                            if source_unit_field in unit_values:
                                row_values[i] = unit_values[source_unit_field]
                            elif col_name_lower in default_values:
                                row_values[i] = default_values[col_name_lower]
                            continue
                        
                        # parent_sku字段特殊调试处理（优先处理，避免被其他逻辑覆盖）
                        if col_name_lower == 'parent_sku':
                            parent_sku_debug_value = None
                            print(f"\n🔍 [parent_sku调试] 开始处理MSKU: {msku}")
                            
                            # 检查row_data中的parent_sku相关字段
                            if hasattr(row_data, 'index'):
                                # row_data是pandas Series
                                print(f"🔍 [parent_sku调试] row_data是Series，可用字段: {list(row_data.index)}")
                                
                                if 'parent_sku' in row_data.index:
                                    val = row_data['parent_sku']
                                    print(f"🔍 [parent_sku调试] parent_sku字段值: {val}, 类型: {type(val)}, notna: {pd.notna(val)}")
                                    if pd.notna(val) and str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_debug_value = str(val).strip()
                                        print(f"✅ [parent_sku调试] 从parent_sku获取值: {parent_sku_debug_value}")
                                
                                if not parent_sku_debug_value and 'parent-sku' in row_data.index:
                                    val = row_data['parent-sku']
                                    print(f"🔍 [parent_sku调试] parent-sku字段值: {val}, 类型: {type(val)}, notna: {pd.notna(val)}")
                                    if pd.notna(val) and str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_debug_value = str(val).strip()
                                        print(f"✅ [parent_sku调试] 从parent-sku获取值: {parent_sku_debug_value}")
                            else:
                                # row_data是字典
                                print(f"🔍 [parent_sku调试] row_data是字典，可用键: {list(row_data.keys())}")
                                
                                if 'parent_sku' in row_data:
                                    val = row_data['parent_sku']
                                    print(f"🔍 [parent_sku调试] parent_sku字段值: {val}, 类型: {type(val)}, notna: {pd.notna(val)}")
                                    if pd.notna(val) and str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_debug_value = str(val).strip()
                                        print(f"✅ [parent_sku调试] 从parent_sku获取值: {parent_sku_debug_value}")
                                
                                if not parent_sku_debug_value and 'parent-sku' in row_data:
                                    val = row_data['parent-sku']
                                    print(f"🔍 [parent_sku调试] parent-sku字段值: {val}, 类型: {type(val)}, notna: {pd.notna(val)}")
                                    if pd.notna(val) and str(val).strip().lower() not in ['nan', '', 'none', 'null']:
                                        parent_sku_debug_value = str(val).strip()
                                        print(f"✅ [parent_sku调试] 从parent-sku获取值: {parent_sku_debug_value}")
                            
                            # 🆕 如果从商品分类报告未获取到parent_sku，尝试从SPU款名（内部）备用机制
                            if not parent_sku_debug_value and product_df is not None:
                                print(f"🔍 [parent_sku调试] 未从商品分类报告获取到parent_sku，尝试SPU款名（内部）备用机制")
                                
                                # 第一步：收集所有目标SKU对应的SPU款名（内部）值
                                spu_names = []
                                processed_skus = []
                                
                                # 遍历图片映射表中所有SKU，收集对应的SPU款名（内部）
                                for check_msku, check_mapping_row in mapping_dict.items():
                                    check_mapping_sku = check_mapping_row.get('SKU', None)
                                    if check_mapping_sku and pd.notna(check_mapping_sku):
                                        check_mapping_sku_upper = str(check_mapping_sku).strip().upper()
                                        processed_skus.append(check_mapping_sku_upper)
                                        
                                        # 在产品资料中查找匹配的SKU
                                        matching_products = product_df[product_df['product_key'] == check_mapping_sku_upper]
                                        if not matching_products.empty:
                                            product_row = matching_products.iloc[0]
                                            if 'SPU款名（内部）' in product_row and pd.notna(product_row['SPU款名（内部）']):
                                                spu_name = str(product_row['SPU款名（内部）']).strip()
                                                if spu_name and spu_name.lower() not in ['none', 'null', '', 'nan']:
                                                    spu_names.append(spu_name)
                                                    print(f"🔍 [parent_sku调试] SKU {check_mapping_sku_upper} -> SPU款名: {spu_name}")
                                
                                print(f"🔍 [parent_sku调试] 处理的SKU总数: {len(processed_skus)}")
                                print(f"🔍 [parent_sku调试] 收集到的SPU款名总数: {len(spu_names)}")
                                
                                # 第二步：检查是否所有SPU款名（内部）都一致
                                if spu_names:
                                    unique_spu_names = list(set(spu_names))
                                    if len(unique_spu_names) == 1:
                                        print(f"✅ [parent_sku调试] 所有目标SKU的SPU款名（内部）一致: {unique_spu_names[0]}")
                                        
                                        # 第三步：从商品分类报告中查找有parent_sku值的记录
                                        print(f"🔍 [parent_sku调试] 在商品分类报告中查找有parent_sku值的记录...")
                                        
                                        # 检查商品分类报告中所有记录的parent_sku字段
                                        found_parent_sku = None
                                        
                                        for _, report_row in report_df.iterrows():
                                            # 检查parent_sku字段
                                            parent_sku_candidates = []
                                            if 'parent_sku' in report_row.index and pd.notna(report_row['parent_sku']):
                                                val = str(report_row['parent_sku']).strip()
                                                if val and val.lower() not in ['nan', '', 'none', 'null']:
                                                    parent_sku_candidates.append(val)
                                            
                                            if 'parent-sku' in report_row.index and pd.notna(report_row['parent-sku']):
                                                val = str(report_row['parent-sku']).strip()
                                                if val and val.lower() not in ['nan', '', 'none', 'null']:
                                                    parent_sku_candidates.append(val)
                                            
                                            # 如果找到有效的parent_sku值
                                            if parent_sku_candidates:
                                                found_parent_sku = parent_sku_candidates[0]  # 使用第一个有效值
                                                report_item_sku = str(report_row.get('item_sku', '')).strip().upper()
                                                print(f"✅ [parent_sku调试] 在商品分类报告中找到parent_sku: {found_parent_sku} (来源SKU: {report_item_sku})")
                                                break
                                        
                                        if found_parent_sku:
                                            parent_sku_debug_value = found_parent_sku
                                            print(f"✅ [parent_sku调试] 使用商品分类报告中的parent_sku值: {parent_sku_debug_value}")
                                        else:
                                            print(f"⚠️ [parent_sku调试] 商品分类报告中未找到任何有效的parent_sku值")
                                        
                                    else:
                                        print(f"⚠️ [parent_sku调试] SPU款名（内部）不一致，共{len(unique_spu_names)}个不同值: {unique_spu_names}")
                                else:
                                    print(f"⚠️ [parent_sku调试] 未找到任何有效的SPU款名（内部）值")
                            
                            if parent_sku_debug_value:
                                row_values[i] = parent_sku_debug_value
                                print(f"✅ [parent_sku调试] 最终填充值: {parent_sku_debug_value}")
                            else:
                                print(f"⚠️ [parent_sku调试] MSKU {msku} 留空")
                                row_values[i] = ''
                            continue
                        
                        # 默认值
                        if col_name_lower in default_values:
                            row_values[i] = default_values[col_name_lower]
                            continue
                        
                        # 从报告数据精确匹配
                        # 🔧 修复：判断row_data是Series还是dict
                        # 🚨 排除manufacturer和parent_sku字段，避免覆盖特殊处理的值
                        if col_name_lower not in ['manufacturer', 'parent_sku']:
                            if hasattr(row_data, 'index'):
                                # row_data是pandas Series
                                if col_name in row_data.index and pd.notna(row_data[col_name]):
                                    val = row_data[col_name]
                                    # 确保不是nan字符串
                                    if str(val).strip().lower() != 'nan':
                                        row_values[i] = val
                                    continue
                            else:
                                # row_data是字典
                                if col_name in row_data and pd.notna(row_data[col_name]):
                                    val = row_data[col_name]
                                    # 确保不是nan字符串
                                    if str(val).strip().lower() != 'nan':
                                        row_values[i] = val
                                    continue
                        
                        # 从产品资料匹配
                        # 🚨 排除manufacturer和parent_sku字段，避免覆盖特殊处理的值
                        if col_name_lower not in ['manufacturer', 'parent_sku'] and product_dict and sku_val in product_dict:
                            product_row = product_dict[sku_val]
                            if col_name in product_row and pd.notna(product_row[col_name]):
                                val = product_row[col_name]
                                # 确保不是nan字符串
                                if str(val).strip().lower() != 'nan':
                                    row_values[i] = val
                                continue
                    
                    batch_data.append(row_values)
                    processed_count += 1
                
                # 批量写入数据
                if batch_data:
                    print(f"📊 批量写入 {len(batch_data)} 行数据...")
                    start_row = 4
                    end_row = start_row + len(batch_data) - 1
                    end_col = len(header_row)
                    
                    target_range = ws.range(f'A{start_row}').resize(len(batch_data), end_col)
                    target_range.value = batch_data
                
                # 保存文件
                wb.save(output_path)
                print(f"✅ 文件已保存：{output_path}")
                
            finally:
                wb.close()
                app.quit()
        
        except Exception as e:
            return False, f"数据填充过程出错：{str(e)}"
        
        # 计算总耗时
        total_time = time.time() - start_time
        
        success_message = f"""模板填充成功完成（MSKU精确匹配模式）！

📊 处理统计：
✅ 总耗时：{total_time:.2f} 秒
✅ 处理行数：{processed_count}
✅ 平均速度：{processed_count/total_time:.1f} 行/秒
✅ 图片映射关联：{mapping_matched} 个MSKU
✅ 产品资料关联：{product_matched} 个SKU
✅ SKC聚合：{len(skc_package_info)} 个SKC
✅ 输出文件：{output_path}"""
        
        return True, success_message
        
    except Exception as e:
        error_msg = f"模板填充过程中发生错误：{str(e)}\n\n详细错误信息：\n{traceback.format_exc()}"
        print(f"\n❌ 错误：{error_msg}")
        return False, error_msg
    
    finally:
        # 清理临时文件
        try:
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except:
            pass

def detect_report_format(report_path):
    """
    🔍 自动检测商品分类报告格式
    适配亚马逊2025年格式变化：字段名item_sku→SKU，表头第3行→第4行
    
    Args:
        report_path (str): 商品分类报告文件路径
        
    Returns:
        tuple: (header_row, sku_field_name, format_info)
        - header_row: 表头行号（pandas的header参数）
        - sku_field_name: SKU字段名称
        - format_info: 格式信息字符串
        
    Raises:
        ValueError: 当无法识别任何已知格式时
    """
    print("\n🔍 开始检测商品分类报告格式...")
    
    # 检测新格式（2025年）：第4行表头，SKU字段
    try:
        print("📋 尝试检测新格式（2025年）：第4行表头，SKU字段...")
        df_new = pd.read_excel(report_path, sheet_name='Template', header=3, nrows=1, engine='openpyxl')
        if 'SKU' in df_new.columns:
            format_info = "新格式（2025年）：第4行表头，SKU字段"
            print(f"✅ 检测成功：{format_info}")
            return 3, 'SKU', format_info
    except Exception as e:
        print(f"❌ 新格式检测失败：{str(e)}")
    
    # 检测旧格式（2024年及之前）：第3行表头，item_sku字段
    try:
        print("📋 尝试检测旧格式（2024年及之前）：第3行表头，item_sku字段...")
        df_old = pd.read_excel(report_path, sheet_name='Template', header=2, nrows=1, engine='openpyxl')
        if 'item_sku' in df_old.columns:
            format_info = "旧格式（2024年及之前）：第3行表头，item_sku字段"
            print(f"✅ 检测成功：{format_info}")
            return 2, 'item_sku', format_info
    except Exception as e:
        print(f"❌ 旧格式检测失败：{str(e)}")
    
    # 尝试自动扫描所有可能的表头位置
    print("🔧 尝试智能扫描表头位置...")
    for header_row in range(5):  # 检查前5行
        try:
            df = pd.read_excel(report_path, sheet_name='Template', header=header_row, nrows=1, engine='openpyxl')
            columns = df.columns.tolist()
            print(f"📊 第{header_row+1}行字段：{columns[:10]}...")
            
            # 检查是否包含SKU相关字段
            if 'SKU' in columns:
                format_info = f"智能检测：第{header_row+1}行表头，SKU字段"
                print(f"✅ 智能检测成功：{format_info}")
                return header_row, 'SKU', format_info
            elif 'item_sku' in columns:
                format_info = f"智能检测：第{header_row+1}行表头，item_sku字段"
                print(f"✅ 智能检测成功：{format_info}")
                return header_row, 'item_sku', format_info
                
        except Exception as e:
            print(f"❌ 第{header_row+1}行检测失败：{str(e)}")
            continue
    
    # 如果都失败了，抛出详细错误
    error_msg = """
❌ 无法识别商品分类报告格式！

🔍 支持的格式：
1. 新格式（2025年）：Template工作表，第4行表头，SKU字段
2. 旧格式（2024年及之前）：Template工作表，第3行表头，item_sku字段

💡 请检查：
1. 文件是否包含Template工作表
2. 是否为标准的亚马逊商品分类报告
3. 文件是否损坏或格式异常

📧 如需技术支持，请提供报告文件的前几行数据截图
""".strip()
    
    raise ValueError(error_msg)