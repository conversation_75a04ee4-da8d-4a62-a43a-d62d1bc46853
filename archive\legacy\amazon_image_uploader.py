# -*- coding: utf-8 -*-
"""
亚马逊图片上传工具 - 主程序
集成图片优化、批量上传、Excel处理功能
"""

import os
import base64
import requests
import json
import tkinter as tk
import time
import random
import concurrent.futures
import threading
import datetime
import shutil
import openpyxl
import re
from tkinter import filedialog, messagebox, simpledialog
from urllib.parse import quote
from collections import defaultdict, Counter
from PIL import Image
import io
import sys
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
import traceback
import signal
import tempfile
import logging
import tkinter.ttk as ttk
from string import ascii_uppercase

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 从配置文件导入设置
try:
    from config.mohe_config import (
        API_KEY, 
        API_URL, 
        FILE_PARAM_NAME,
        UPLOAD_MODE,
        UPLOAD_FORMAT
    )
    print("成功从配置文件加载设置")
except ImportError as e:
    print(f"警告: 配置文件导入失败 - {str(e)}")
    messagebox.showerror("配置错误", "无法加载配置文件，请确保config/mohe_config.py文件存在且配置正确。")
    raise

# 验证必要的配置项
if not API_KEY or API_KEY == "YOUR_API_KEY_HERE":
    messagebox.showerror("配置错误", "API密钥未设置，请在mohe_config.py中设置正确的API_KEY。")
    raise ValueError("API密钥未设置")

if not API_URL:
    messagebox.showerror("配置错误", "API地址未设置，请在mohe_config.py中设置正确的API_URL。")
    raise ValueError("API地址未设置")

# 输出 Markdown 表格文件
OUTPUT_FILE = "image_mapping.md"
# 历史映射表文件夹
HISTORY_DIR = "历史映射表"
# 最大重试次数
MAX_RETRIES = 3
# 优化后的并发上传线程数
DEFAULT_MAX_WORKERS = 10  # 增加到10个并发线程
# 优化后的上传延迟（避免API限制）
DEFAULT_UPLOAD_DELAY = 0.1  # 减少延迟到0.1秒
# 默认图片压缩质量（1-100）
DEFAULT_IMAGE_QUALITY = 90  # 图片质量
# 默认最大尺寸（像素）
DEFAULT_MAX_SIZE = 2000  # 最大尺寸
# 是否启用图片优化
ENABLE_IMAGE_OPTIMIZATION = False  # 默认不启用图片优化

# 创建全局的 requests Session 对象，用于连接池复用
session = requests.Session()
# 配置重试策略
retry_strategy = Retry(
    total=MAX_RETRIES,
    backoff_factor=0.5,
    status_forcelist=[429, 500, 502, 503, 504],
)
# 🚀 高性能连接池配置 - 支持大量并发上传
adapter = HTTPAdapter(
    max_retries=retry_strategy,
    pool_connections=50,  # 增大连接池大小，支持更多并发
    pool_maxsize=50,      # 增大最大连接数
    pool_block=False      # 连接池满时不阻塞
)
session.mount("http://", adapter)
session.mount("https://", adapter)

# 线程锁，用于同步上传操作
upload_lock = threading.Lock()

def optimize_image(file_path, quality=90, max_size=2000):
    """
    优化图片大小，保证图片质量，返回优化后的图片数据
    
    参数:
        file_path: 原始图片路径
        quality: 压缩质量（1-100）
        max_size: 最大尺寸（宽或高，以像素为单位）
    
    返回:
        优化后的图片数据
    """
    # 获取文件扩展名
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()
    
    # 跳过非图片文件或GIF动画
    if ext not in ['.jpg', '.jpeg', '.png', '.webp'] or ext == '.gif':
        with open(file_path, 'rb') as f:
            return f.read()
    
    try:
        # 打开图片
        img = Image.open(file_path)
        
        # 检查图片是否需要调整大小
        needs_resize = False
        if max_size and (img.width > max_size or img.height > max_size):
            needs_resize = True
            if img.width > img.height:
                new_width = max_size
                new_height = int(img.height * (max_size / img.width))
            else:
                new_height = max_size
                new_width = int(img.width * (max_size / img.height))
        
        # 检查文件大小
        original_size = os.path.getsize(file_path)
        original_too_large = original_size > 2 * 1024 * 1024  # 超过2MB
        
        # 如果图片不需要调整大小且大小适中，则使用原图
        if not needs_resize and not original_too_large:
            with open(file_path, 'rb') as f:
                return f.read()
        
        # 调整尺寸（如果需要）
        if needs_resize:
            img = img.resize((new_width, new_height), Image.LANCZOS)
        
        # 保存到内存
        output = io.BytesIO()
        
        # 根据文件类型选择保存格式和质量
        if ext in ['.jpg', '.jpeg']:
            # JPEG使用较高质量
            save_quality = quality
            img.save(output, format='JPEG', quality=save_quality, optimize=True)
        elif ext == '.png':
            # PNG使用无损压缩
            img.save(output, format='PNG', optimize=True)
        elif ext == '.webp':
            # WebP使用较高质量
            save_quality = quality
            img.save(output, format='WEBP', quality=save_quality)
        else:
            # 其他格式
            img.save(output, format=img.format, quality=quality)
        
        # 获取优化后的图片数据
        optimized_data = output.getvalue()
        
        # 如果优化后比原图还大，则使用原图
        if len(optimized_data) >= original_size:
            with open(file_path, 'rb') as f:
                return f.read()
        
        # 打印优化信息
        optimized_size = len(optimized_data)
        save_percent = (1 - optimized_size / original_size) * 100
        if save_percent > 5:  # 如果节省超过5%的空间
            print(f"优化图片 {os.path.basename(file_path)}: {original_size/1024:.1f}KB → {optimized_size/1024:.1f}KB (节省 {save_percent:.1f}%)")
        
        return optimized_data
    except Exception as e:
        print(f"图片优化失败: {e}，使用原始图片")
        with open(file_path, 'rb') as f:
            return f.read()

def select_image_directory():
    """弹出对话框选择图片目录"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 设置对话框标题
    directory = filedialog.askdirectory(
        title="选择包含图片的文件夹",
        initialdir=os.path.expanduser("~/Documents")  # 默认从文档目录开始
    )
    
    if not directory:
        print("未选择任何目录，退出程序")
        return None
    
    print(f"已选择目录: {directory}")
    return directory

def ask_upload_folder():
    root = tk.Tk()
    root.withdraw()
    while True:
        folder = simpledialog.askstring("输入上传目录", "请输入要存放图片的一级目录名（不能包含/或\\，不能为空）：")
        if folder is None:
            return None  # 用户取消
        folder = folder.strip()
        if not folder:
            messagebox.showerror("错误", "目录名不能为空！")
            continue
        if '/' in folder or '\\' in folder:
            messagebox.showerror("错误", "只允许一级目录，不能包含/或\\！")
            continue
        return folder

def upload_image_to_mohecdn(file_path, retry_count=0, upload_folder=None):
    """上传图片到图床，添加重试机制"""
    file_size = os.path.getsize(file_path) / (1024 * 1024)
    if file_size > 8:
        print(f"警告: 文件 {os.path.basename(file_path)} 大小为 {file_size:.2f}MB，超过8MB可能导致上传失败")
    try:
        data = {
            "api_token": API_KEY,
            "upload_format": UPLOAD_FORMAT,
            "mode": UPLOAD_MODE,
            "protocol_type": "http"  # 返回HTTP协议的链接
        }
        if upload_folder:
            data["uploadPath"] = upload_folder
        with open(file_path, 'rb') as file:
            files = {FILE_PARAM_NAME: (os.path.basename(file_path), file, 'image/jpeg')}
            print(f"开始上传: {os.path.basename(file_path)}")
            with upload_lock:
                response = session.post(API_URL, data=data, files=files, timeout=30)
        
        # 输出响应状态和内容（调试用）
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"上传响应内容: {response.text}")
        
        if response.status_code == 200:  # 上传成功
            # 尝试解析响应
            try:
                result = response.json()
                
                # 解析不同格式的响应
                if 'url' in result:
                    return result['url']
                elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                    return result['data']['url']
                elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                    return result['data']
                else:
                    print(f"上传成功但未返回URL: {response.text}")
                    return response.text if 'http' in response.text else None
            except Exception as e:
                print(f"解析响应时出错: {str(e)}")
                return response.text if 'http' in response.text else None
        else:
            print(f"上传失败: {file_path}, 状态码: {response.status_code}")
            print(f"错误响应: {response.text}")
            
            # API 速率限制检查
            if response.status_code == 429:  # Too Many Requests
                wait_time = 60 + random.randint(1, 30)  # 随机等待时间，避免同时重试
                print(f"达到API速率限制，等待{wait_time}秒...")
                time.sleep(wait_time)
                if retry_count < MAX_RETRIES:
                    print(f"正在重试 ({retry_count+1}/{MAX_RETRIES})...")
                    return upload_image_to_mohecdn(file_path, retry_count + 1, upload_folder)
            
            return None
            
    except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
            requests.exceptions.ConnectionError, requests.exceptions.SSLError) as e:
        print(f"网络错误: {str(e)}")
        if retry_count < MAX_RETRIES:
            retry_delay = 5 + random.randint(1, 5) * retry_count
            print(f"等待 {retry_delay} 秒后重试 ({retry_count+1}/{MAX_RETRIES})...")
            time.sleep(retry_delay)
            return upload_image_to_mohecdn(file_path, retry_count + 1, upload_folder)
        else:
            print(f"重试 {MAX_RETRIES} 次后仍然失败")
            return None

def save_to_amazon_excel(data_dict, output_path, title="亚马逊图片映射表"):
    """按照亚马逊模板格式保存图片映射数据到Excel文件"""
    # 创建新的Excel工作簿和工作表
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "亚马逊图片模板"
    
    # 列标题 - 修改为程序期望的标准格式（小写+下划线）
    columns = [
        "ASIN", "原文件名", "MSKU", "SKU", "main_image_url", 
        "other_image_url1", "other_image_url2", "other_image_url3", 
        "other_image_url4", "other_image_url5", "other_image_url6", 
        "other_image_url7", "other_image_url8", "swatch_image_url"
    ]
    
    # 添加标题行
    ws.append(columns)
    
    # 设置标题行格式
    for cell in ws[1]:
        cell.font = openpyxl.styles.Font(bold=True)
        cell.alignment = openpyxl.styles.Alignment(horizontal='center')
    
    # 尝试获取图片任务列表信息
    task_list_info = {}
    try:
        # 弹出文件选择对话框，让用户选择图片任务列表文件
        from tkinter import filedialog, messagebox
        import pandas as pd
        
        task_list_path = filedialog.askopenfilename(
            title="选择图片任务列表文件",
            filetypes=[("Excel文件", "*.xlsx;*.xls;*.xlsm"), ("所有文件", "*.*")],
            initialdir=os.path.dirname(output_path)
        )
        
        if task_list_path:
            # 读取图片任务列表
            try:
                task_df = pd.read_excel(task_list_path)
                print(f"成功读取图片任务列表，共 {len(task_df)} 行")
                
                # 检查必要的列是否存在
                required_cols = ['ASIN', 'SKU', 'MSKU']
                missing_cols = [col for col in required_cols if col not in task_df.columns]
                
                if missing_cols:
                    print(f"图片任务列表缺少必要的列: {missing_cols}")
                    # 尝试查找替代列
                    alt_cols = []
                    for col in task_df.columns:
                        col_lower = str(col).lower()
                        if 'asin' in col_lower:
                            alt_cols.append(('ASIN', col))
                        elif 'sku' in col_lower and 'msku' not in col_lower:
                            alt_cols.append(('SKU', col))
                        elif 'msku' in col_lower:
                            alt_cols.append(('MSKU', col))
                    
                    if alt_cols:
                        print(f"找到可能的替代列: {alt_cols}")
                        for required, found in alt_cols:
                            if required in missing_cols:
                                task_df[required] = task_df[found]
                                missing_cols.remove(required)
                                print(f"使用 {found} 列替代 {required}")
                
                # 如果仍然缺少必要列，显示警告但继续处理
                if missing_cols:
                    messagebox.showwarning("警告", f"图片任务列表缺少必要的列: {', '.join(missing_cols)}\n将使用空值填充这些列。")
                
                # 创建ASIN到SKU和MSKU的映射
                for _, row in task_df.iterrows():
                    asin = str(row.get('ASIN', '')).strip()
                    if asin:
                        sku = str(row.get('SKU', '')).strip() if 'SKU' in row else ''
                        msku = str(row.get('MSKU', '')).strip() if 'MSKU' in row else ''
                        task_list_info[asin] = {'SKU': sku, 'MSKU': msku}
                
                print(f"成功创建ASIN映射，共 {len(task_list_info)} 个ASIN")
            except Exception as e:
                print(f"读取图片任务列表时出错: {str(e)}")
                messagebox.showerror("错误", f"读取图片任务列表时出错: {str(e)}")
    except Exception as e:
        print(f"获取图片任务列表信息时出错: {str(e)}")
    
    # 添加数据行
    row_count = 0
    for asin, images in data_dict.items():
        row = [asin]  # ASIN
        
        # 原文件名 - 使用所有图片的文件名，用逗号分隔
        all_filenames = ', '.join([img['filename'] for img in images if 'filename' in img])
        row.append(all_filenames)
        
        # 从任务列表中获取MSKU和SKU
        msku = task_list_info.get(asin, {}).get('MSKU', '')
        sku = task_list_info.get(asin, {}).get('SKU', '')
        
        # 添加MSKU和SKU列
        row.append(msku)  # MSKU
        row.append(sku)   # SKU
        
        # 默认所有URL为空
        url_columns = [""] * 10  # Main + 8 Others + Swatch
        
        # 填充图片URL到对应列
        for img in images:
            if 'type' in img and 'url' in img:
                img_type = img['type']
                url = img['url']
                
                if img_type == 'MAIN':
                    url_columns[0] = url  # Main Image URL
                elif img_type == 'SWATCH':
                    url_columns[9] = url  # Swatch Image URL
                elif img_type.startswith('PT') and len(img_type) == 4:
                    try:
                        # 提取PT后的数字，如PT01中的1
                        pt_num = int(img_type[2:])
                        if 1 <= pt_num <= 8:
                            url_columns[pt_num] = url  # Other Image URL1-8
                    except ValueError:
                        # 如果无法解析数字，忽略
                        pass
        
        # 将URL添加到行中            
        row.extend(url_columns)
        
        # 添加行到工作表
        ws.append(row)
        row_count += 1
    
    # 调整列宽
    column_widths = {
        'A': 15,  # ASIN
        'B': 30,  # 原文件名
        'C': 15,  # MSKU
        'D': 15,  # SKU
    }
    
    # URL列宽度
    for col in range(5, 15):  # E到N列
        # 修改为使用正确的列标识计算
        column_letter = get_column_letter(col)
        column_widths[column_letter] = 60
    
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # 添加信息表头
    info_sheet = wb.create_sheet(title="信息")
    info_sheet.append([title])
    info_sheet.append(["生成时间", datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    info_sheet.append(["薄荷图床", "图片上传服务"])
    info_sheet.append(["产品数量", str(row_count)])
    info_sheet.append(["图片总数", str(sum(len(imgs) for imgs in data_dict.values()))])
    
    # 保存文件
    wb.save(output_path)
    print(f"亚马逊模板Excel已保存到: {output_path}")
    return output_path

def determine_image_type(filename):
    """根据文件名确定图片类型"""
    filename_upper = filename.upper()
    
    if 'MAIN' in filename_upper:
        return 'MAIN'
    elif 'SWATCH' in filename_upper:
        return 'SWATCH'
    else:
        # 查找PT01-PT08格式
        pt_match = re.search(r'PT0?([1-8])', filename_upper)
        if pt_match:
            return f'PT{int(pt_match.group(1)):02d}'  # 确保格式为PT01、PT02等
    
    # 默认返回Other
    return 'OTHER'

def extract_asin(filename):
    """从文件名中提取ASIN（第一个.之前的内容）"""
    # 如果文件名包含点，则提取第一个点之前的内容作为ASIN
    if '.' in filename:
        asin = filename.split('.', 1)[0]
        return asin
    # 如果没有点，则返回整个文件名作为ASIN
    return filename

def process_images_for_amazon(image_data):
    """将图片数据处理成亚马逊模板格式"""
    # 使用defaultdict将相同ASIN的图片分组
    asin_groups = defaultdict(list)
    
    for item in image_data:
        if len(item) >= 3:  # 确保数据格式正确
            asin, filename, url = item[:3]
            
            # 确定图片类型
            img_type = determine_image_type(filename)
            
            # 添加到对应ASIN组
            asin_groups[asin].append({
                'filename': filename,
                'url': url,
                'type': img_type
            })
    
    return asin_groups 

def process_file_with_optimization(file_info, upload_folder=None):
    image_file, file_path = file_info
    try:
        if ENABLE_IMAGE_OPTIMIZATION:
            optimized_data = optimize_image(file_path)
            print(f"图片优化已启用: {image_file}")
        else:
            with open(file_path, 'rb') as f:
                optimized_data = f.read()
            print(f"使用原始图片质量: {image_file}")
        url = upload_optimized_image(image_file, optimized_data, upload_folder=upload_folder)
        if url:
            return (image_file, url, "success")
        else:
            return (image_file, "上传失败", "failed")
    except Exception as e:
        error_msg = str(e)[:50] + "..." if len(str(e)) > 50 else str(e)
        return (image_file, f"处理错误: {error_msg}", "error")

def upload_optimized_image(image_file, image_data, retry_count=0, upload_folder=None):
    """上传优化后的图片数据到图床"""
    try:
        _, ext = os.path.splitext(image_file)
        data = {
            "api_token": API_KEY,
            "upload_format": UPLOAD_FORMAT,
            "mode": UPLOAD_MODE,
            "uploadPath": "TEST",  # 测试图片固定上传到TEST目录
            "protocol_type": "http"  # 返回HTTP协议的链接
        }
        if upload_folder:
            data["uploadPath"] = upload_folder
        files = {
            FILE_PARAM_NAME: (image_file, image_data, f'image/{ext.lstrip(".").lower()}' if ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp'] else 'image/jpeg')
        }
        print(f"开始上传: {image_file}")
        with upload_lock:
            response = session.post(API_URL, data=data, files=files, timeout=30)
        
        # 输出响应状态和内容（调试用）
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"上传响应内容: {response.text}")
        
        if response.status_code == 200:
            # 尝试解析JSON响应
            try:
                result = response.json()
                
                # 如果是错误响应，检查是否提供了错误详情
                if 'status' in result and result['status'] == 'error':
                    error_msg = result.get('resultData', '未知错误')
                    print(f"API返回错误: {error_msg}")
                    
                    # 检测常见错误类型并终止程序
                    if 'Token' in str(error_msg) or 'token' in str(error_msg) or 'API' in str(error_msg):
                        # API密钥错误，终止程序
                        messagebox.showerror("API错误", f"上传失败，API密钥错误:\n\n{error_msg}\n\n请检查并更新配置文件中的API_KEY。")
                        # 终止所有线程并退出
                        import sys
                        sys.exit(1)
                    
                    return None
                    
                # 检查成功响应，按照API文档格式解析
                if 'status' in result and result['status'] == 'success':
                    if 'url' in result:
                        print(f"上传成功: {result['url']}")
                        return result['url']
                    else:
                        print("成功响应但未找到URL字段，尝试其他字段")
                
                # 尝试其他可能的响应格式
                if 'url' in result:
                    return result['url']
                elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                    return result['data']['url']
                elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                    return result['data']
                else:
                    print(f"上传成功但无法解析URL: {response.text}")
                    # 如果响应文本中包含URL，直接返回
                    if 'http' in response.text:
                        url_match = re.search(r'https?://[^\s\"\']+', response.text)
                        if url_match:
                            return url_match.group(0)
                    return None
            except Exception as e:
                print(f"解析响应时出错: {str(e)}")
                # 尝试从响应文本中提取URL
                if 'http' in response.text:
                    url_match = re.search(r'https?://[^\s\"\']+', response.text)
                    if url_match:
                        return url_match.group(0)
                return None
        else:
            print(f"上传失败: {image_file}, 状态码: {response.status_code}")
            print(f"错误响应: {response.text}")
            
            # API 速率限制检查
            if response.status_code == 429:  # Too Many Requests
                wait_time = 60 + random.randint(1, 30)  # 随机等待时间，避免同时重试
                print(f"达到API速率限制，等待{wait_time}秒...")
                time.sleep(wait_time)
                if retry_count < MAX_RETRIES:
                    print(f"正在重试 ({retry_count+1}/{MAX_RETRIES})...")
                    return upload_optimized_image(image_file, image_data, retry_count + 1, upload_folder)
            
            return None
            
    except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
            requests.exceptions.ConnectionError, requests.exceptions.SSLError) as e:
        print(f"网络错误: {str(e)}")
        if retry_count < MAX_RETRIES:
            retry_delay = 5 + random.randint(1, 5) * retry_count
            print(f"等待 {retry_delay} 秒后重试 ({retry_count+1}/{MAX_RETRIES})...")
            time.sleep(retry_delay)
            return upload_optimized_image(image_file, image_data, retry_count + 1, upload_folder)
        else:
            print(f"重试 {MAX_RETRIES} 次后仍然失败")
            return None

def save_progress(md_content, success_count, total_processed):
    """保存当前进度到文件"""
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        f.write(md_content)
    print(f"已保存进度: {success_count}/{total_processed}")

def ensure_history_dir():
    """确保历史映射表目录存在"""
    history_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), HISTORY_DIR)
    if not os.path.exists(history_dir):
        os.makedirs(history_dir)
    return history_dir

def save_to_history(md_content, source_dir):
    """保存映射表到历史记录"""
    # 确保历史目录存在
    history_dir = ensure_history_dir()
    
    # 生成时间戳文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    source_name = os.path.basename(source_dir) if source_dir else "未知来源"
    safe_source_name = "".join(c if c.isalnum() or c in "._- " else "_" for c in source_name)
    history_filename = f"映射表_{timestamp}_{safe_source_name}.md"
    
    # 保存到历史目录
    history_path = os.path.join(history_dir, history_filename)
    with open(history_path, "w", encoding="utf-8") as f:
        # 添加上传信息头
        header = f"# 图片上传记录\n\n"
        header += f"- **上传时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += f"- **来源目录**: {source_dir}\n"
        header += f"- **图床服务**: 薄荷图床\n\n"
        
        f.write(header + md_content)
    
    print(f"历史映射表已保存到: {history_path}")
    return history_path

def view_history_mappings():
    """查看历史映射表"""
    # 确保历史目录存在
    history_dir = ensure_history_dir()
    
    # 获取所有历史映射表
    history_files = [f for f in os.listdir(history_dir) 
                     if os.path.isfile(os.path.join(history_dir, f)) and f.endswith('.md')]
    
    if not history_files:
        messagebox.showinfo("历史记录", "没有找到历史映射表记录。")
        return
    
    # 按时间排序（最新的在前）
    history_files.sort(reverse=True)
    
    # 创建选择窗口
    root = tk.Tk()
    root.title("历史映射表")
    root.geometry("800x500")
    
    # 标题标签
    tk.Label(root, text="选择要查看的历史映射表", font=("Arial", 14)).pack(pady=10)
    
    # 创建框架
    frame = tk.Frame(root)
    frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    # 创建滚动条
    scrollbar = tk.Scrollbar(frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 创建列表框
    listbox = tk.Listbox(frame, yscrollcommand=scrollbar.set, font=("Arial", 12), height=15)
    listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # 配置滚动条
    scrollbar.config(command=listbox.yview)
    
    # 添加历史文件到列表
    for file in history_files:
        # 提取日期和来源
        parts = file.split('_', 2)
        if len(parts) >= 3:
            date_str = parts[1]
            source = parts[2].replace('.md', '')
            # 格式化日期
            try:
                date = datetime.datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                formatted_date = date.strftime("%Y-%m-%d %H:%M:%S")
                display_text = f"{formatted_date} - {source}"
            except:
                display_text = file
        
        listbox.insert(tk.END, display_text)
        
    # 存储文件名映射
    file_map = dict(zip([listbox.get(i) for i in range(listbox.size())], history_files))
    
    # 定义打开文件的函数
    def open_selected_file():
        selection = listbox.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个映射表")
            return
            
        selected_text = listbox.get(selection[0])
        selected_file = file_map[selected_text]
        file_path = os.path.join(history_dir, selected_file)
        
        # 打开文件
        try:
            os.startfile(file_path)
        except:
            # 如果不支持 startfile，尝试其他方法
            try:
                import subprocess
                subprocess.Popen(['start', file_path], shell=True)
            except:
                messagebox.showerror("错误", f"无法打开文件: {file_path}")
    
    # 定义复制文件内容的函数
    def copy_to_clipboard():
        selection = listbox.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个映射表")
            return
            
        selected_text = listbox.get(selection[0])
        selected_file = file_map[selected_text]
        file_path = os.path.join(history_dir, selected_file)
        
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 复制到剪贴板
            root.clipboard_clear()
            root.clipboard_append(content)
            messagebox.showinfo("成功", "映射表内容已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"无法复制文件内容: {str(e)}")
    
    # 创建按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 添加按钮
    open_button = tk.Button(button_frame, text="打开选中文件", command=open_selected_file, 
                           font=("Arial", 12), width=15)
    open_button.pack(side=tk.LEFT, padx=10)
    
    copy_button = tk.Button(button_frame, text="复制到剪贴板", command=copy_to_clipboard,
                           font=("Arial", 12), width=15)
    copy_button.pack(side=tk.LEFT, padx=10)
    
    close_button = tk.Button(button_frame, text="关闭", command=root.destroy,
                            font=("Arial", 12), width=10)
    close_button.pack(side=tk.RIGHT, padx=10)
    
    # 运行主循环
    root.mainloop() 

def validate_image_filename(filename):
    """验证图片文件名是否符合要求：B0开头且包含MAIN、PT或SWATCH关键词"""
    filename_upper = filename.upper()
    
    # 检查是否以B0开头
    if not filename_upper.startswith("B0"):
        return False, "文件名必须以B0开头（Amazon ASIN格式）"
    
    # 检查是否包含MAIN、PT或SWATCH关键词
    if not ('MAIN' in filename_upper or 'PT' in filename_upper or 'SWATCH' in filename_upper):
        return False, "文件名必须包含MAIN、PT或SWATCH关键词以标识图片类型"
    
    return True, "文件名格式正确"

def validate_image_files(image_files):
    """检查所有待上传图片文件名是否符合要求"""
    valid_files = []
    invalid_files = []
    
    for image_file in image_files:
        is_valid, reason = validate_image_filename(image_file)
        if is_valid:
            valid_files.append(image_file)
        else:
            invalid_files.append((image_file, reason))
    
    return valid_files, invalid_files

def test_api_connection():
    """测试API连接，返回测试结果"""
    print("开始测试API连接...")
    
    # 清理可能存在的测试文件
    temp_files = ["test_upload.jpg", "test.jpg"]
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print(f"清理旧测试文件: {temp_file}")
            except:
                pass
    
    # 创建一个测试图片
    test_image = Image.new('RGB', (50, 50), color = 'white')
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='JPEG')
    img_data = img_byte_arr.getvalue()
    
    results = {}
    
    # 测试当前配置的API地址
    print(f"当前配置的API URL: {API_URL}")
    print(f"当前配置的API密钥: {API_KEY[:4]}...{API_KEY[-4:] if len(API_KEY) > 8 else ''}")
    
    # 尝试多种方式请求API
    api_urls_to_test = [
        API_URL,  # 当前配置
        "https://s3.x914.com/tempofan/api/upload/",  # 备用地址1
        "https://s3.x914.com/api/upload/",  # 备用地址2
    ]
    
    # 去除重复的URL
    api_urls_to_test = list(dict.fromkeys(api_urls_to_test))
    
    # 测试各种组合
    for i, url in enumerate(api_urls_to_test):
        print(f"\n测试API地址 {i+1}/{len(api_urls_to_test)}: {url}")
        
        # 使用配置文件中的参数
        try:
            data = {
                "api_token": API_KEY,
                "upload_format": UPLOAD_FORMAT,
                "mode": UPLOAD_MODE,
                "uploadPath": "TEST"  # 测试图片固定上传到TEST目录
            }
            files = {FILE_PARAM_NAME: ('test.jpg', img_data, 'image/jpeg')}
            
            print(f"  尝试连接: 使用配置文件参数")
            response = requests.post(url, data=data, files=files, timeout=10)
            
            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"  响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'status' in result and result['status'] == 'success':
                        print(f"  ✅ 测试成功: {url}")
                        results[f"API连接 ({url})"] = True
                        
                        # 如果成功，提示更新配置
                        if url != API_URL:
                            print(f"\n建议更新配置文件中的API_URL为: {url}")
                        
                        # 尝试解析返回的URL
                        if 'url' in result:
                            print(f"  获取到URL: {result['url']}")
                        elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                            print(f"  获取到URL: {result['data']['url']}")
                        elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                            print(f"  获取到URL: {result['data']}")
                    else:
                        error_msg = result.get('resultData', '未知错误')
                        if 'Token' in str(error_msg) or 'token' in str(error_msg) or 'API' in str(error_msg):
                            print(f"  ❌ 测试失败: {url} (API密钥错误)")
                            results[f"API连接 ({url})"] = "API密钥错误"
                        else:
                            print(f"  ❌ 测试失败: {url} ({error_msg})")
                            results[f"API连接 ({url})"] = error_msg
                except:
                    if 'http' in response.text:
                        print(f"  响应包含URL但格式不是JSON")
                        results[f"API连接 ({url})"] = True
                    else:
                        print(f"  ❌ 测试失败: {url} (无法解析响应)")
                        results[f"API连接 ({url})"] = "无法解析响应"
            else:
                print(f"  ❌ 测试失败: {url} (HTTP错误: {response.status_code})")
                results[f"API连接 ({url})"] = f"HTTP错误: {response.status_code}"
        
        except Exception as e:
            print(f"  ❌ 测试出错: {str(e)}")
            results[f"API连接 ({url})"] = str(e)
    
    # 汇总结果
    success_count = sum(1 for result in results.values() if result is True)
    print(f"\n测试完成: {success_count}/{len(results)} 个测试成功")
    
    # 确保清理所有临时文件
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print(f"删除测试文件: {temp_file}")
            except:
                pass
    
    return results

def show_test_results(results):
    """显示API测试结果"""
    root = tk.Tk()
    root.title("API连接测试结果")
    root.geometry("500x400")
    
    # 设置窗口位置
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    # 标题
    tk.Label(root, text="API连接测试结果", font=("Arial", 16, "bold")).pack(pady=20)
    
    # 创建文本框显示结果
    text = tk.Text(root, wrap=tk.WORD, width=60, height=15)
    text.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
    
    # 添加滚动条
    scrollbar = tk.Scrollbar(text)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    text.config(yscrollcommand=scrollbar.set)
    scrollbar.config(command=text.yview)
    
    # 显示结果
    text.insert(tk.END, "API测试结果:\n\n")
    
    success_count = 0
    for method, result in results.items():
        if result is True:
            text.insert(tk.END, f"✅ {method}: 成功\n")
            success_count += 1
        elif result is False:
            text.insert(tk.END, f"❌ {method}: 失败\n")
        else:
            text.insert(tk.END, f"⚠️ {method}: {result}\n")
    
    text.insert(tk.END, f"\n总计: {success_count}/{len(results)} 个测试成功\n\n")
    
    if success_count > 0:
        text.insert(tk.END, "建议操作:\n")
        text.insert(tk.END, "1. 更新配置文件(mohe_config.py)中的API_URL为能成功连接的地址\n")
        text.insert(tk.END, "2. 确保使用正确的认证方式(token参数或Authorization头)\n")
    else:
        text.insert(tk.END, "所有测试均失败，请检查:\n")
        text.insert(tk.END, "1. API密钥是否正确\n")
        text.insert(tk.END, "2. 网络连接是否正常\n")
        text.insert(tk.END, "3. 服务器是否可用\n")
    
    # 设置只读
    text.config(state=tk.DISABLED)
    
    # 关闭按钮
    tk.Button(root, text="关闭", command=root.destroy, font=("Arial", 12)).pack(pady=20)
    
    # 运行主循环
    root.mainloop()

def start_upload():
    """开始上传流程，先测试API连接"""
    # 先测试API连接
    if not check_api_availability():
        print("API接口检测失败，程序终止")
        return
    
    # 删除可能存在的测试文件
    temp_file = "test_upload.jpg"
    if os.path.exists(temp_file):
        try:
            os.remove(temp_file)
            print(f"已删除测试文件: {temp_file}")
        except Exception as e:
            print(f"无法删除测试文件: {e}")
    
    # API测试通过，继续上传流程
    main()

def test_file_upload():
    """测试文件上传功能，确定正确的文件上传参数"""
    print("开始测试文件上传...")
    
    # 创建一个简单的测试图片
    test_image = Image.new('RGB', (100, 100), color = 'blue')
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='JPEG')
    img_data = img_byte_arr.getvalue()
    
    # 保存临时文件
    temp_file_path = "test_upload.jpg"
    with open(temp_file_path, "wb") as f:
        f.write(img_data)
    
    print(f"创建测试文件: {temp_file_path}")
    
    # 测试不同的参数组合
    results = {}
    
    # 测试不同的文件参数名 - 按照API文档
    file_params = ['uploadedFile', 'file']
    token_formats = [
        {"api_token": API_KEY},
    ]
    
    # 测试不同的命名模式
    mode_options = [
        {},  # 默认命名方式
        {"mode": 1},  # 自动重命名
        {"mode": 2},  # 保留原文件名
        {"mode": 3},  # 短链接模式
    ]
    
    # 使用文件对象的方式
    for file_param in file_params:
        for token_format in token_formats:
            for mode_option in mode_options:
                param_name = list(token_format.keys())[0]
                token_value = list(token_format.values())[0]
                mode_text = f", mode={mode_option.get('mode', '默认')}" if mode_option else ""
                try:
                    print(f"\n测试组合: 文件参数='{file_param}', 认证参数='{param_name}'{mode_text}")
                    with open(temp_file_path, 'rb') as f:
                        files = {
                            file_param: ('test_upload.jpg', f, 'image/jpeg')
                        }
                        data = token_format.copy()
                        data["upload_format"] = "file"
                        if mode_option:
                            data.update(mode_option)
                        data["uploadPath"] = "TEST"  # 测试图片固定上传到TEST目录
                        response = requests.post(API_URL, data=data, files=files, timeout=30)
                    
                    print(f"响应状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"响应内容: {response.text}")
                    
                    # 记录结果
                    test_key = f"{file_param} + {param_name}"
                    
                    # 检查是否是文件上传错误
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if 'status' in result and result['status'] == 'error':
                                error_msg = result.get('resultData', '')
                                if 'no file' in error_msg.lower() or 'uploaded' in error_msg.lower():
                                    results[test_key] = "失败 - 文件未上传"
                                elif 'token' in error_msg.lower() or 'api' in error_msg.lower():
                                    results[test_key] = "失败 - 认证错误"
                                else:
                                    results[test_key] = f"失败 - {error_msg}"
                            elif 'url' in result or ('data' in result and ('url' in result['data'] or ('http' in str(result['data'])))):
                                results[test_key] = "成功 - 获取到URL"
                            else:
                                results[test_key] = "未知响应格式"
                        except:
                            results[test_key] = "无法解析JSON响应"
                    else:
                        results[test_key] = f"HTTP错误: {response.status_code}"
                except Exception as e:
                    results[test_key] = f"请求错误: {str(e)}"
    
    # 清理临时文件
    if os.path.exists(temp_file_path):
        os.remove(temp_file_path)
    
    # 显示结果
    print("\n文件上传测试结果:")
    for test, result in results.items():
        print(f"{test}: {result}")
    
    # 创建结果窗口
    root = tk.Tk()
    root.title("文件上传测试结果")
    root.geometry("600x400")
    
    # 标题
    tk.Label(root, text="文件上传测试结果", font=("Arial", 16, "bold")).pack(pady=10)
    
    # 创建文本框
    text = tk.Text(root, wrap=tk.WORD, width=70, height=20)
    text.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
    
    # 添加滚动条
    scrollbar = tk.Scrollbar(text)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    text.config(yscrollcommand=scrollbar.set)
    scrollbar.config(command=text.yview)
    
    # 显示结果
    text.insert(tk.END, "文件上传测试结果\n\n")
    
    # 先显示成功的结果
    success_found = False
    for test, result in results.items():
        if "成功" in result:
            success_found = True
            text.insert(tk.END, f"✅ {test}: {result}\n")
    
    if success_found:
        text.insert(tk.END, "\n成功的组合可能是正确的配置，请更新mohe_config.py文件\n\n")
    else:
        text.insert(tk.END, "❌ 未找到成功的组合\n\n")
    
    # 显示其他结果
    text.insert(tk.END, "其他测试结果:\n\n")
    for test, result in results.items():
        if "成功" not in result:
            text.insert(tk.END, f"❌ {test}: {result}\n")
    
    # 设为只读
    text.config(state=tk.DISABLED)
    
    # 关闭按钮
    tk.Button(root, text="关闭", command=root.destroy, font=("Arial", 12)).pack(pady=10)
    
    # 显示窗口
    root.mainloop()
    
    return results

def get_urls_from_user():
    import tkinter as tk
    from tkinter import simpledialog
    class UrlInputDialog(simpledialog.Dialog):
        def body(self, master):
            self.title("批量URL输入")
            tk.Label(master, text="请粘贴所有图片URL（每行一个）：").pack(pady=5)
            self.text = tk.Text(master, width=70, height=20, wrap=tk.WORD)
            self.text.pack(padx=10, pady=5, fill=tk.BOTH, expand=True)
            self.text.focus_set()
            # 滚动条
            scrollbar = tk.Scrollbar(master, command=self.text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.text.config(yscrollcommand=scrollbar.set)
            return self.text
        def apply(self):
            self.result = self.text.get("1.0", tk.END).strip()
    root = tk.Tk()
    root.withdraw()
    dialog = UrlInputDialog(root)
    return dialog.result if hasattr(dialog, 'result') and dialog.result else None

def parse_urls_to_amazon_excel():
    import tkinter.simpledialog as simpledialog
    from tkinter import filedialog
    # 使用自定义大窗口获取URL
    url_text = get_urls_from_user()
    if not url_text:
        messagebox.showinfo("提示", "未输入任何URL，操作已取消。")
        return
    urls = [line.strip() for line in url_text.strip().splitlines() if line.strip()]
    if not urls:
        messagebox.showinfo("提示", "未检测到有效URL。")
        return
    # 解析URL，整理为亚马逊模板格式
    from collections import defaultdict
    asin_groups = defaultdict(lambda: defaultdict(list))  # {店铺: {ASIN: [图片信息]}}
    for url in urls:
        try:
            parts = url.split('/')
            if len(parts) < 2:
                continue
            shop = parts[-2]
            filename = parts[-1]
            asin = filename.split('.', 1)[0]
            # 取类型
            name_part = filename.split('.')
            if len(name_part) >= 2:
                type_part = name_part[1].upper()
                if type_part.startswith('MAIN'):
                    img_type = 'MAIN'
                elif type_part.startswith('SWCH') or type_part.startswith('SWATCH'):
                    img_type = 'SWATCH'
                elif type_part.startswith('PT'):
                    img_type = f'PT{name_part[1][2:4]}' if len(name_part[1]) >= 4 else name_part[1].upper()
                else:
                    img_type = 'OTHER'
            else:
                img_type = 'OTHER'
            asin_groups[shop][asin].append({
                'filename': filename,
                'url': url,
                'type': img_type
            })
        except Exception as e:
            print(f"解析URL失败: {url} -> {e}")
    
    # 尝试获取图片任务列表信息
    task_list_info = {}
    try:
        # 弹出文件选择对话框，让用户选择图片任务列表文件
        task_list_path = filedialog.askopenfilename(
            title="选择图片任务列表文件（可选）",
            filetypes=[("Excel文件", "*.xlsx;*.xls;*.xlsm"), ("所有文件", "*.*")],
            initialdir=os.path.expanduser("~/Documents")
        )
        
        if task_list_path:
            # 读取图片任务列表
            try:
                import pandas as pd
                task_df = pd.read_excel(task_list_path)
                print(f"成功读取图片任务列表，共 {len(task_df)} 行")
                
                # 检查必要的列是否存在
                required_cols = ['ASIN', 'SKU', 'MSKU']
                missing_cols = [col for col in required_cols if col not in task_df.columns]
                
                if missing_cols:
                    print(f"图片任务列表缺少必要的列: {missing_cols}")
                    # 尝试查找替代列
                    alt_cols = []
                    for col in task_df.columns:
                        col_lower = str(col).lower()
                        if 'asin' in col_lower:
                            alt_cols.append(('ASIN', col))
                        elif 'sku' in col_lower and 'msku' not in col_lower:
                            alt_cols.append(('SKU', col))
                        elif 'msku' in col_lower:
                            alt_cols.append(('MSKU', col))
                    
                    if alt_cols:
                        print(f"找到可能的替代列: {alt_cols}")
                        for required, found in alt_cols:
                            if required in missing_cols:
                                task_df[required] = task_df[found]
                                missing_cols.remove(required)
                                print(f"使用 {found} 列替代 {required}")
                
                # 如果仍然缺少必要列，显示警告但继续处理
                if missing_cols:
                    messagebox.showwarning("警告", f"图片任务列表缺少必要的列: {', '.join(missing_cols)}\n将使用空值填充这些列。")
                
                # 创建ASIN到SKU和MSKU的映射
                for _, row in task_df.iterrows():
                    asin = str(row.get('ASIN', '')).strip()
                    if asin:
                        sku = str(row.get('SKU', '')).strip() if 'SKU' in row else ''
                        msku = str(row.get('MSKU', '')).strip() if 'MSKU' in row else ''
                        task_list_info[asin] = {'SKU': sku, 'MSKU': msku}
                
                print(f"成功创建ASIN映射，共 {len(task_list_info)} 个ASIN")
            except Exception as e:
                print(f"读取图片任务列表时出错: {str(e)}")
                messagebox.showerror("错误", f"读取图片任务列表时出错: {str(e)}")
    except Exception as e:
        print(f"获取图片任务列表信息时出错: {str(e)}")
    
    # 导出为Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "亚马逊图片模板"
    # 修改列名为程序期望的标准格式（小写+下划线）
    columns = [
        "店铺", "ASIN", "原文件名", "MSKU", "SKU", "main_image_url",
        "other_image_url1", "other_image_url2", "other_image_url3",
        "other_image_url4", "other_image_url5", "other_image_url6",
        "other_image_url7", "other_image_url8", "swatch_image_url"
    ]
    ws.append(columns)
    for shop, asin_dict in asin_groups.items():
        for asin, images in asin_dict.items():
            row = [shop, asin]
            all_filenames = ', '.join([img['filename'] for img in images if 'filename' in img])
            row.append(all_filenames)
            
            # 从任务列表中获取MSKU和SKU
            msku = task_list_info.get(asin, {}).get('MSKU', '')
            sku = task_list_info.get(asin, {}).get('SKU', '')
            
            # 添加MSKU和SKU列
            row.append(msku)  # MSKU
            row.append(sku)   # SKU
            
            url_columns = [""] * 10  # Main + 8 Others + Swatch
            for img in images:
                if 'type' in img and 'url' in img:
                    img_type = img['type']
                    url = img['url']
                    if img_type == 'MAIN':
                        url_columns[0] = url
                    elif img_type == 'SWATCH':
                        url_columns[9] = url
                    elif img_type.startswith('PT') and len(img_type) == 4:
                        try:
                            pt_num = int(img_type[2:])
                            if 1 <= pt_num <= 8:
                                url_columns[pt_num] = url
                        except ValueError:
                            pass
            row.extend(url_columns)
            ws.append(row)
    # 设置列宽
    for col in range(1, 16):  # 增加一列，因为添加了MSKU
        ws.column_dimensions[get_column_letter(col)].width = 20
    # 让用户选择保存路径
    save_path = filedialog.asksaveasfilename(
        title="保存Excel文件",
        defaultextension=".xlsx",
        filetypes=[("Excel文件", "*.xlsx")],
        initialfile="亚马逊图片模板.xlsx"
    )
    if not save_path:
        messagebox.showinfo("提示", "未选择保存路径，操作已取消。")
        return
    wb.save(save_path)
    messagebox.showinfo("导出成功", f"已成功导出Excel文件：\n{save_path}")

def fill_amazon_template():
    """填充亚马逊批量上传模板"""
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox
    import pandas as pd
    import os
    import shutil
    import traceback
    import time
    import tempfile
    import signal
    import threading
    from queue import Queue
    
    def show_error_details(error_msg):
        """显示详细错误信息"""
        error_window = tk.Tk()
        error_window.title("错误详情")
        error_window.geometry("600x400")
        
        # 创建文本框和滚动条
        text = tk.Text(error_window, wrap=tk.WORD, width=70, height=20)
        scrollbar = tk.Scrollbar(text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=text.yview)
        
        # 显示错误信息
        text.insert(tk.END, "发生错误：\n\n")
        text.insert(tk.END, str(error_msg))
        
        # 添加关闭按钮
        tk.Button(error_window, text="关闭", command=error_window.destroy).pack(pady=10)
        
        # 居中显示窗口
        error_window.update_idletasks()
        width = error_window.winfo_width()
        height = error_window.winfo_height()
        x = (error_window.winfo_screenwidth() // 2) - (width // 2)
        y = (error_window.winfo_screenheight() // 2) - (height // 2)
        error_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        error_window.mainloop()
    
    def update_progress(status_label, progress_var, progress, status_text):
        """更新进度显示"""
        status_label.config(text=status_text)
        progress_var.set(progress)
        status_label.master.update()
    
    def read_excel_safely(file_path, sheet_name=None, **kwargs):
        """安全地读取Excel文件，带有错误处理和超时保护"""
        max_attempts = 3
        attempt = 1
        
        # 备份timeout参数，但不传递给pandas read_excel，因为它不支持这个参数
        timeout_val = kwargs.pop('timeout', 30) if 'timeout' in kwargs else 30
        
        # 先检查工作表是否存在
        if sheet_name:
            try:
                excel_file = pd.ExcelFile(file_path, engine='openpyxl')
                available_sheets = excel_file.sheet_names
                print(f"可用的工作表: {available_sheets}")
                if sheet_name not in available_sheets:
                    print(f"警告: 指定的工作表 '{sheet_name}' 不存在，可用的工作表: {available_sheets}")
                    if available_sheets:
                        print(f"将使用第一个可用的工作表: {available_sheets[0]}")
                        sheet_name = available_sheets[0]
            except Exception as e:
                print(f"检查工作表时出错: {str(e)}")
        
        while attempt <= max_attempts:
            try:
                print(f"尝试读取文件 {file_path} (第{attempt}次尝试)")
                
                # 如果指定了sheet_name，输出信息
                if sheet_name:
                    print(f"指定读取工作表: {sheet_name}")
                
                # 设置信号处理器（仅在非Windows系统上有效）
                if os.name != 'nt':  # 如果不是Windows系统
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(timeout_val)  # 设置超时时间（秒）
                
                # 确保明确传递sheet_name参数
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, **kwargs)
                else:
                    df = pd.read_excel(file_path, **kwargs)
                
                # 如果成功，取消定时器
                if os.name != 'nt':
                    signal.alarm(0)
                
                print(f"成功读取文件: {file_path}")
                return df
            
            except Exception as e:
                print(f"读取文件失败 (尝试 {attempt}/{max_attempts}): {str(e)}")
                attempt += 1
                if attempt <= max_attempts:
                    print(f"等待1秒后重试...")
                    time.sleep(1)
                else:
                    # 如果是超时异常，提供更明确的错误信息
                    if isinstance(e, TimeoutError):
                        raise TimeoutError(f"读取文件超时: {file_path}")
                    # 重新抛出原始异常
                    raise
    
    def timeout_handler(signum, frame):
        """超时处理函数"""
        raise TimeoutError("操作超时")
    
    # 创建市场选择窗口
    market_window = tk.Tk()
    market_window.title("选择目标市场")
    market_window.geometry("400x300")
    
    # 居中显示窗口
    market_window.update_idletasks()
    width = market_window.winfo_width()
    height = market_window.winfo_height()
    x = (market_window.winfo_screenwidth() // 2) - (width // 2)
    y = (market_window.winfo_screenheight() // 2) - (height // 2)
    market_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    # 市场选择标签
    tk.Label(market_window, text="请选择目标市场：", font=("Arial", 12)).pack(pady=10)
    
    # 市场选择下拉框
    market_var = tk.StringVar()
    market_combo = ttk.Combobox(market_window, textvariable=market_var, state="readonly")
    market_combo['values'] = ('美国', '英国')
    market_combo.pack(pady=10)
    market_combo.current(0)  # 默认选择美国
    
    # 添加产品资料表格选择选项
    product_info_var = tk.BooleanVar(value=False)
    product_info_check = tk.Checkbutton(market_window, text="使用产品资料表格获取包装信息", variable=product_info_var)
    product_info_check.pack(pady=10)
    
    def on_market_selected():
        try:
            market = market_var.get()
            use_product_info = product_info_var.get()  # 获取用户是否选择使用产品资料表格
            
            market_window.destroy()
            
            # 根据市场选择对应的模板文件路径
            if market == "英国":
                template_file = r"D:\华为家庭存储\工作\运营\Listing相关\模板\DECORATIVE_RIBBON_TRIM_UK.xlsm"
            else:  # 默认美国市场
                template_file = r"D:\华为家庭存储\工作\运营\Listing相关\模板\DECORATIVE_RIBBON_TRIM_US.xlsm"
            
            if not os.path.exists(template_file):
                messagebox.showerror("错误", f"模板文件不存在：\n{template_file}")
                return
            print(f"使用模板文件: {template_file}")
            
            # 用户手动选择分类商品报告
            report_file = filedialog.askopenfilename(
                title="选择分类商品报告",
                filetypes=[("Excel文件", "*.xlsx;*.xlsm;*.xls"), ("所有文件", "*.*")],
                initialdir=r"D:\华为家庭存储\工作\运营\Listing相关\Listing"
            )
            if not report_file:
                messagebox.showinfo("提示", "未选择分类商品报告，操作已取消")
                return
            
            print(f"使用商品报告文件: {report_file}")
            
            # 选择图床映射关系表
            mapping_file = filedialog.askopenfilename(
                title="选择图床映射关系表",
                filetypes=[("Excel文件", "*.xlsx;*.xlsm")],
                initialdir=os.path.expanduser("~/Documents")
            )
            if not mapping_file:
                return
            
            # 创建进度窗口
            progress_window = tk.Tk()
            progress_window.title("处理进度")
            progress_window.geometry("400x150")
            
            # 居中显示进度窗口
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
            
            # 进度标签
            status_label = tk.Label(progress_window, text="正在读取文件...", font=("Arial", 10))
            status_label.pack(pady=10)
            
            # 进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
            progress_bar.pack(fill=tk.X, padx=20, pady=10)
            
            progress_window.update()
            
            try:
                print("开始处理文件...")
                
                # 更新进度
                update_progress(status_label, progress_var, 10, "正在读取分类商品报告...")
                
                # 读取分类商品报告
                update_progress(status_label, progress_var, 20, "读取分类商品报告...")
                try:
                    print("正在读取分类商品报告...")
                    
                    # 首先检查文件中的工作表
                    excel = pd.ExcelFile(report_file, engine='openpyxl')
                    sheets = excel.sheet_names
                    print(f"分类商品报告包含的工作表: {sheets}")
                    
                    # 确认Template工作表是否存在 - 优先使用Template
                    template_sheet = None
                    if 'Template' in sheets:
                        template_sheet = 'Template'
                        print(f"找到Template工作表，将使用它")
                    else:
                        # 尝试查找其他可能的工作表
                        candidates = ['Template', 'Data', 'Main', 'Products']
                        for candidate in candidates:
                            if candidate in sheets:
                                template_sheet = candidate
                                print(f"将使用备选工作表: {template_sheet}")
                                break
                        
                        # 如果没有找到任何预设的工作表，使用第一个可用的工作表
                        if not template_sheet:
                            template_sheet = sheets[0]
                            print(f"未找到预设工作表，使用第一个工作表: {template_sheet}")
                    
                    print(f"将尝试读取工作表: {template_sheet}")
                    
                    # 默认使用第3行作为表头（商品分类报告和亚马逊模版的标准格式）
                    print("使用第3行作为表头（标准格式）")
                    report_df = pd.read_excel(report_file, sheet_name=template_sheet, header=2, engine='openpyxl')
                    print(f"成功读取工作表 {template_sheet}，共 {len(report_df)} 行数据")
                    print(f"列名: {list(report_df.columns)[:20]}")
                    
                    # 验证读取的数据
                    print("\n【调试信息】验证读取的数据:")
                    if len(report_df) > 0:
                        print(f"第1行数据预览: {report_df.iloc[0].to_dict()}")
                        if 'item_sku' in report_df.columns:
                            print(f"item_sku列前5个值: {report_df['item_sku'].head().tolist()}")
                        else:
                            print("警告: 没有找到item_sku列")
                            possible_sku_cols = [col for col in report_df.columns if 'sku' in str(col).lower()]
                            if possible_sku_cols:
                                print(f"可能的SKU列: {possible_sku_cols}")
                                for col in possible_sku_cols:
                                    print(f"{col}列前5个值: {report_df[col].head().tolist()}")
                    
                    # 标准化SKU字段处理 - 使用精确匹配
                    sku_column_found = False
                    
                    # 只使用精确匹配查找item_sku列
                    if 'item_sku' in report_df.columns:
                        report_df['item_sku'] = report_df['item_sku'].astype(str).str.strip().str.upper()
                        print(f"【调试信息】使用 item_sku 列作为SKU标识")
                        sku_column_found = True
                    
                    # 如果没找到，抛出异常
                    if not sku_column_found:
                        raise ValueError("分类商品报告中未找到精确匹配的'item_sku'列，请确认报告格式是否正确")
                    
                    # 显示有效SKU数量
                    sku_count = report_df['item_sku'].notna().sum()
                    print(f"分类商品报告中的有效SKU数量: {sku_count}")
                    
                    # 检查数据是否为空
                    if sku_count == 0:
                        raise ValueError("分类商品报告中没有有效的SKU数据")
                    
                    # 显示SKU列的前几个值用于验证
                    print("SKU列前5个值:")
                    print(report_df['item_sku'].head().to_string())
                    
                    # 标准化SKU格式
                    report_df['item_sku'] = report_df['item_sku'].astype(str).str.strip().str.upper()

                    # 读取产品资料表格并处理包装信息
                    product_info_df = None
                    skc_package_info = {}  # 存储SKC的包装信息 {SKC: {'weight': 值, 'length': 值, 'width': 值, 'height': 值}}
                    
                    if use_product_info:
                        # 选择产品资料表格
                        product_info_file = filedialog.askopenfilename(
                            title="选择产品资料表格",
                            filetypes=[("Excel文件", "*.xlsx;*.xlsm;*.xls")],
                            initialdir=os.path.dirname(report_file) if os.path.exists(report_file) else os.path.expanduser("~/Documents")
                        )
                        
                        if not product_info_file or not os.path.exists(product_info_file):
                            messagebox.showwarning("警告", "未选择产品资料表格，将不使用包装信息")
                            skc_package_info = {}  # 确保变量被定义
                        else:
                            try:
                                # 更新进度
                                update_progress(status_label, progress_var, 25, "正在读取产品资料表格...")
                                
                                # 读取产品资料表格
                                print("正在读取产品资料表格中的'单个产品'工作表...")
                                product_info_df = pd.read_excel(product_info_file, sheet_name="单个产品")
                                print(f"成功读取产品资料表格，共 {len(product_info_df)} 行数据")
                                print(f"产品资料表格列名: {list(product_info_df.columns)}")
                                
                                # 检查必要的列是否存在
                                print("检查产品资料表中的必要列...")
                                # 定义精确的列名映射关系（包括SKC列）
                                required_mapping = {
                                    '*SKU（必填）': 'SKU',  # SKU列
                                    'SKC': 'SKC',  # SKC列（用户已经分好类的）
                                    '单品毛重': 'package_weight',
                                    '包装规格长': 'package_length',
                                    '包装规格宽': 'package_width',
                                    '包装规格高': 'package_height'
                                }
                                missing_cols = []
                                
                                # 检查每个必要列是否存在（使用精确匹配）
                                for req_col in required_mapping.keys():
                                    if req_col not in product_info_df.columns:
                                        missing_cols.append(req_col)
                                        
                                print(f"检查结果: {'全部找到' if not missing_cols else f'缺少 {missing_cols}'}")
                                
                                if missing_cols:
                                    messagebox.showwarning("警告", f"产品资料表格缺少必要列: {', '.join(missing_cols)}\n将不使用包装信息")
                                    skc_package_info = {}  # 清空包装信息
                                else:
                                    # 使用精确的列名映射
                                    sku_col = '*SKU（必填）'  # SKU列
                                    skc_col = 'SKC'  # SKC列（用户已经分好类的）
                                    weight_col = '单品毛重'
                                    length_col = '包装规格长'
                                    width_col = '包装规格宽'
                                    height_col = '包装规格高'
                                    
                                    print(f"使用精确列名映射: SKU={sku_col}, SKC={skc_col}, 重量={weight_col}, 长={length_col}, 宽={width_col}, 高={height_col}")
                                    
                                    # 处理数据，直接使用表格中的SKC列进行聚合
                                    sku_raw_data = {}  # 临时存储：{SKU: {'skc': SKC, 'weight': 值, ...}}
                                    skc_package_info = {}  # 最终存储：{SKC: {'weight': 最小值, ...}}
                                    
                                    # 第一步：收集所有SKU的原始包装数据
                                    print(f"\n【第一步】开始收集SKU原始包装数据...")
                                    for idx, row in product_info_df.iterrows():
                                        sku = str(row[sku_col]).strip().upper() if pd.notna(row[sku_col]) else None
                                        skc = str(row[skc_col]).strip().upper() if pd.notna(row[skc_col]) else None
                                        
                                        if not sku or not skc:
                                            continue
                                        
                                        print(f"\n【调试】第{idx+1}行 - 处理产品资料SKU: {sku} -> SKC: {skc}")
                                        print(f"【调试】原始数据: 重量={row[weight_col]}, 长={row[length_col]}, 宽={row[width_col]}, 高={row[height_col]}")
                                        
                                        # 转换并验证包装数据
                                        package_data = {}
                                        
                                        # 处理重量
                                        try:
                                            weight = float(row[weight_col]) if pd.notna(row[weight_col]) and str(row[weight_col]).strip() != "" else None
                                            # 排除异常值（0或负数）
                                            if weight is not None and weight > 0:
                                                package_data['weight'] = weight
                                            print(f"【调试】重量处理: {row[weight_col]} -> {package_data.get('weight', 'None')}")
                                        except (ValueError, TypeError):
                                            print(f"警告: SKU {sku} 的重量值无法转换: {row[weight_col]}")
                                        
                                        # 处理长度
                                        try:
                                            length = float(row[length_col]) if pd.notna(row[length_col]) and str(row[length_col]).strip() != "" else None
                                            if length is not None and length > 0:
                                                package_data['length'] = length
                                            print(f"【调试】长度处理: {row[length_col]} -> {package_data.get('length', 'None')}")
                                        except (ValueError, TypeError):
                                            print(f"警告: SKU {sku} 的长度值无法转换: {row[length_col]}")
                                        
                                        # 处理宽度
                                        try:
                                            width = float(row[width_col]) if pd.notna(row[width_col]) and str(row[width_col]).strip() != "" else None
                                            if width is not None and width > 0:
                                                package_data['width'] = width
                                            print(f"【调试】宽度处理: {row[width_col]} -> {package_data.get('width', 'None')}")
                                        except (ValueError, TypeError):
                                            print(f"警告: SKU {sku} 的宽度值无法转换: {row[width_col]}")
                                        
                                        # 处理高度
                                        try:
                                            height = float(row[height_col]) if pd.notna(row[height_col]) and str(row[height_col]).strip() != "" else None
                                            if height is not None and height > 0:
                                                package_data['height'] = height
                                            print(f"【调试】高度处理: {row[height_col]} -> {package_data.get('height', 'None')}")
                                        except (ValueError, TypeError):
                                            print(f"警告: SKU {sku} 的高度值无法转换: {row[height_col]}")
                                        
                                        # 存储原始数据
                                        sku_raw_data[sku] = {
                                            'skc': skc,
                                            **package_data
                                        }
                                        print(f"【调试】SKU {sku} 存储的完整数据: {sku_raw_data[sku]}")
                                    
                                    print(f"\n【第一步完成】共收集了 {len(sku_raw_data)} 个SKU的原始数据")
                                    
                                    # 第二步：按SKC分组并计算最小值
                                    print(f"\n【第二步】开始按SKC分组...")
                                    skc_groups = {}  # {SKC: [所有该SKC的包装数据]}
                                    for sku, data in sku_raw_data.items():
                                        skc = data['skc']
                                        if skc not in skc_groups:
                                            skc_groups[skc] = []
                                        skc_groups[skc].append(data)
                                    
                                    print(f"【第二步完成】共分组为 {len(skc_groups)} 个SKC")
                                    
                                    # 第三步：为每个SKC计算最小值
                                    print(f"\n【第三步】开始为每个SKC计算最小值...")
                                    for skc, data_list in skc_groups.items():
                                        print(f"\n【SKC聚合】处理SKC: {skc}，包含 {len(data_list)} 个SKU")
                                        print(f"【SKC聚合】该SKC的所有SKU数据:")
                                        for i, data in enumerate(data_list):
                                            print(f"  SKU #{i+1}: {data}")
                                        
                                        skc_min_values = {}
                                        
                                        # 重量仍然取最小值
                                        print(f"  【重量计算】开始计算重量最小值:")
                                        weight_values = []
                                        for i, data in enumerate(data_list):
                                            if 'weight' in data and data['weight'] is not None:
                                                weight_values.append(data['weight'])
                                                print(f"    SKU #{i+1} 重量: {data['weight']}")
                                            else:
                                                print(f"    SKU #{i+1} 重量: 无效或缺失")
                                        
                                        if weight_values:
                                            min_weight = min(weight_values)
                                            skc_min_values['weight'] = min_weight
                                            print(f"  【重量结果】所有重量值: {weight_values} -> 最小值: {min_weight}")
                                        else:
                                            skc_min_values['weight'] = None
                                            print(f"  【重量结果】无有效重量数据")
                                        
                                        # 尺寸：使用频率最高的组合，如果频率一样再取最小值
                                        print(f"  【详细尺寸计算】开始为SKC {skc} 计算频率最高的尺寸组合:")
                                        
                                        # 收集所有完整的长宽高组合
                                        dimension_combos = []
                                        
                                        for idx, data in enumerate(data_list):
                                            # 检查是否有完整的长宽高数据
                                            if ('length' in data and data['length'] is not None and
                                                'width' in data and data['width'] is not None and
                                                'height' in data and data['height'] is not None):
                                                
                                                combo = (data['length'], data['width'], data['height'])
                                                dimension_combos.append(combo)
                                                volume = data['length'] * data['width'] * data['height']
                                                print(f"    SKU #{idx}: 长{data['length']} × 宽{data['width']} × 高{data['height']} = 体积{volume}")
                                            else:
                                                print(f"    SKU #{idx}: 数据不完整，跳过")
                                        
                                        # 选择最佳尺寸组合
                                        if dimension_combos:
                                            # 统计每个组合的出现频率
                                            combo_counter = Counter(dimension_combos)
                                            print(f"    【频率统计】尺寸组合频率: {dict(combo_counter)}")
                                            
                                            # 找出最高频率
                                            max_count = max(combo_counter.values())
                                            most_frequent_combos = [combo for combo, count in combo_counter.items() if count == max_count]
                                            
                                            print(f"    【频率分析】最高频率: {max_count}, 最高频率组合: {most_frequent_combos}")
                                            
                                            if len(most_frequent_combos) == 1:
                                                # 只有一个最高频率组合，直接选择
                                                chosen_combo = most_frequent_combos[0]
                                                print(f"    【选择结果】唯一最高频率组合: {chosen_combo}")
                                            else:
                                                # 多个组合频率相同，选择体积最小的
                                                print(f"    【体积比较】多个组合频率相同，比较体积:")
                                                min_volume = float('inf')
                                                chosen_combo = None
                                                
                                                for combo in most_frequent_combos:
                                                    volume = combo[0] * combo[1] * combo[2]
                                                    print(f"      组合 {combo} 体积: {volume}")
                                                    if volume < min_volume:
                                                        min_volume = volume
                                                        chosen_combo = combo
                                                
                                                print(f"    【选择结果】频率相同时选择最小体积组合: {chosen_combo} (体积: {min_volume})")
                                            
                                            # 设置选中的尺寸
                                            skc_min_values['length'] = chosen_combo[0]
                                            skc_min_values['width'] = chosen_combo[1]  
                                            skc_min_values['height'] = chosen_combo[2]
                                            chosen_volume = chosen_combo[0] * chosen_combo[1] * chosen_combo[2]
                                            
                                            print(f"  【最终选择】频率最高的尺寸组合: 长{chosen_combo[0]}, 宽{chosen_combo[1]}, 高{chosen_combo[2]}, 体积{chosen_volume}, 频率{combo_counter[chosen_combo]}")
                                        else:
                                            # 如果没有完整的尺寸数据，则分别取各维度的最小值（备用方案）
                                            print(f"  警告: 没有找到完整的长宽高数据，使用备用方案（分别取最小值）")
                                            for dimension in ['length', 'width', 'height']:
                                                valid_values = []
                                                for data in data_list:
                                                    if dimension in data and data[dimension] is not None:
                                                        valid_values.append(data[dimension])
                                                
                                                if valid_values:
                                                    min_value = min(valid_values)
                                                    skc_min_values[dimension] = min_value
                                                    print(f"    {dimension}: {valid_values} -> 最小值: {min_value}")
                                                else:
                                                    skc_min_values[dimension] = None
                                                    print(f"    {dimension}: 无有效数据")
                                        
                                        skc_package_info[skc] = skc_min_values
                                        print(f"  【SKC完成】{skc} 的最终包装信息: {skc_min_values}")
                                
                                    print(f"\n【第三步完成】成功处理产品资料表格，获取了 {len(skc_package_info)} 个SKC的包装信息")
                                    print(f"【调试】SKC列表: {list(skc_package_info.keys())}")
                                    
                                    # 特别分析问题SKC
                                    problem_skc = 'DL-LW-PZ-BZ-02-050'
                                    if problem_skc in skc_package_info:
                                        print(f"\n🔍【问题SKC详细分析】{problem_skc}")
                                        print(f"最终结果: {skc_package_info[problem_skc]}")
                                        
                                        # 重新分析这个SKC的原始数据
                                        print(f"该SKC包含的所有SKU原始数据：")
                                        skc_sku_list = []
                                        for sku, data in sku_raw_data.items():
                                            if data['skc'] == problem_skc:
                                                skc_sku_list.append((sku, data))
                                                print(f"  SKU: {sku}")
                                                print(f"    重量: {data.get('weight')}g")
                                                print(f"    长度: {data.get('length')}cm")
                                                print(f"    宽度: {data.get('width')}cm") 
                                                print(f"    高度: {data.get('height')}cm")
                                                if all(data.get(dim) is not None for dim in ['length', 'width', 'height']):
                                                    volume = data['length'] * data['width'] * data['height']
                                                    print(f"    体积: {volume}cm³")
                                        
                                        print(f"🎯【分析结论】为什么选择了这个组合？")
                                        final_info = skc_package_info[problem_skc]
                                        print(f"选择的组合: 长{final_info['length']}cm × 宽{final_info['width']}cm × 高{final_info['height']}cm")
                                        if final_info.get('length') and final_info.get('width') and final_info.get('height'):
                                            selected_volume = final_info['length'] * final_info['width'] * final_info['height']
                                            print(f"选择的体积: {selected_volume}cm³")
                                    
                                    # 详细输出每个SKC的包装信息用于调试
                                    print("\n【详细调试】所有SKC的包装信息:")
                                    for skc, info in skc_package_info.items():
                                        print(f"  SKC {skc}: 重量={info.get('weight')}, 长={info.get('length')}, 宽={info.get('width')}, 高={info.get('height')}")
                                    
                                    # 根据市场转换单位
                                    print(f"\n【单位转换】目标市场: {market}")
                                    for skc, info in skc_package_info.items():
                                        # 特别关注问题SKC
                                        is_problem_skc = skc == 'DL-LW-PZ-BZ-02-050'
                                        if is_problem_skc:
                                            print(f"\n  ⭐【重点追踪】问题SKC: {skc}")
                                        
                                        print(f"  【转换前】SKC {skc}: 重量={info.get('weight')}g, 长={info.get('length')}cm, 宽={info.get('width')}cm, 高={info.get('height')}cm")
                                        
                                        if market == '美国':
                                            # cm转换为英寸，g转换为盎司
                                            if info['weight'] is not None:
                                                original_weight = info['weight']
                                                info['weight'] = info['weight'] / 28.35  # g转盎司
                                                info['weight'] = round(info['weight'], 2)
                                                if is_problem_skc:
                                                    print(f"    ⭐重量转换: {original_weight}g ÷ 28.35 = {info['weight']}oz")
                                                else:
                                                    print(f"    重量转换: {original_weight}g ÷ 28.35 = {info['weight']}oz")
                                            if info['length'] is not None:
                                                original_length = info['length']
                                                info['length'] = info['length'] / 2.54  # cm转英寸
                                                info['length'] = round(info['length'], 2)
                                                if is_problem_skc:
                                                    print(f"    ⭐长度转换: {original_length}cm ÷ 2.54 = {info['length']}inch")
                                                else:
                                                    print(f"    长度转换: {original_length}cm ÷ 2.54 = {info['length']}inch")
                                            if info['width'] is not None:
                                                original_width = info['width']
                                                info['width'] = info['width'] / 2.54  # cm转英寸
                                                info['width'] = round(info['width'], 2)
                                                if is_problem_skc:
                                                    print(f"    ⭐宽度转换: {original_width}cm ÷ 2.54 = {info['width']}inch")
                                                else:
                                                    print(f"    宽度转换: {original_width}cm ÷ 2.54 = {info['width']}inch")
                                            if info['height'] is not None:
                                                original_height = info['height']
                                                info['height'] = info['height'] / 2.54  # cm转英寸
                                                info['height'] = round(info['height'], 2)
                                                if is_problem_skc:
                                                    print(f"    ⭐高度转换: {original_height}cm ÷ 2.54 = {info['height']}inch")
                                                else:
                                                    print(f"    高度转换: {original_height}cm ÷ 2.54 = {info['height']}inch")
                                        # 其他市场暂时保持原单位
                                        
                                        if is_problem_skc:
                                            print(f"  ⭐【转换后】SKC {skc}: 重量={info.get('weight')}, 长={info.get('length')}, 宽={info.get('width')}, 高={info.get('height')}")
                                        else:
                                            print(f"  【转换后】SKC {skc}: 重量={info.get('weight')}, 长={info.get('length')}, 宽={info.get('width')}, 高={info.get('height')}")
                                    
                                    # 创建SKU到SKC的映射（用于后续快速查找）
                                    sku_to_skc_mapping = {}
                                    # 直接使用产品资料表格中的真实SKU与SKC对应关系
                                    for sku, data in sku_raw_data.items():
                                        skc = data['skc']
                                        sku_to_skc_mapping[sku] = skc
                                    
                                    
                                    print(f"创建了SKU到SKC映射，包含 {len(sku_to_skc_mapping)} 个映射关系")
                                    print(f"【调试】SKU到SKC映射示例:")
                                    for i, (sku, skc) in enumerate(list(sku_to_skc_mapping.items())[:5]):
                                        print(f"  {sku} -> {skc}")
                                    if len(sku_to_skc_mapping) > 5:
                                        print(f"  ... 还有 {len(sku_to_skc_mapping) - 5} 个映射")
                            
                            except Exception as e:
                                print(f"读取产品资料表格时出错: {str(e)}")
                                traceback.print_exc()
                                messagebox.showerror("错误", f"读取产品资料表格失败: {str(e)}")
                                skc_package_info = {}  # 清空包装信息
                    
                except Exception as e:
                    raise Exception(f"读取分类商品报告失败: {str(e)}")
                
                # 再次验证读取的列名是否包含SKU列
                print(f"分类商品报告读取完成，共 {len(report_df)} 行数据")
                print(f"分类商品报告列名: {list(report_df.columns)}")
                
                # 检查读取的数据是否与预览阶段一致
                actual_cols_lower = [str(col).lower() for col in report_df.columns]
                print("\n【调试信息】列名检查:")
                print(f"列名小写转换结果: {actual_cols_lower}")
                
                if 'item_sku' not in actual_cols_lower and 'sku' not in actual_cols_lower:
                    print(f"警告: 读取的列名中没有找到SKU列！")
                    print(f"实际读取列名: {actual_cols_lower[:10]}")
                    
                # 标准化SKU字段处理 - 更灵活地查找SKU列
                sku_column_found = False
                
                # 方法1：直接查找item_sku或sku列
                for possible_name in ['item_sku', 'sku', 'ITEM_SKU', 'SKU', 'Item_Sku', 'Sku']:
                    if possible_name in report_df.columns:
                        report_df['item_sku'] = report_df[possible_name].astype(str).str.strip().str.upper()
                        print(f"【调试信息】使用 {possible_name} 列作为SKU标识")
                        sku_column_found = True
                        break
                
                # 方法2：模糊匹配含有sku的列名
                if not sku_column_found:
                    for col in report_df.columns:
                        if 'sku' in str(col).lower():
                            report_df['item_sku'] = report_df[col].astype(str).str.strip().str.upper()
                            print(f"【调试信息】使用模糊匹配的列 {col} 作为SKU标识")
                            sku_column_found = True
                            break
                    
                # 如果还是没找到，抛出异常
                if not sku_column_found:
                    raise ValueError("分类商品报告中未找到'item_sku'或'sku'列，请确认报告格式是否正确")
                
                # 显示有效SKU数量
                sku_count = report_df['item_sku'].notna().sum()
                print(f"分类商品报告中的有效SKU数量: {sku_count}")
                
                # 输出前5个SKU值作为参考
                print("SKU列前5个值:")
                for i, sku in enumerate(report_df['item_sku'].head(5)):
                    print(f"{i}: {sku}")
                
                # 检查数据是否为空
                if sku_count == 0:
                    raise ValueError("分类商品报告中没有有效的SKU数据")
                    
                # 显示SKU列的前几个值用于验证
                print("SKU列前5个值:")
                print(report_df['item_sku'].head().to_string())
                
                # 标准化SKU格式
                report_df['item_sku'] = report_df['item_sku'].astype(str).str.strip().str.upper()
            except Exception as e:
                raise Exception(f"读取分类商品报告失败: {str(e)}")

            # 更新进度
            update_progress(status_label, progress_var, 30, "正在读取图床映射关系表...")
            
            # 读取图床映射关系表
            update_progress(status_label, progress_var, 40, "读取图床映射关系表...")
            try:
                print(f"尝试读取文件 {mapping_file} (第1次尝试)")
                
                # 首先尝试预览文件结构
                preview_df = pd.read_excel(mapping_file, nrows=10, engine='openpyxl', header=0)  # 图床映射表第一行表头
                print(f"图床映射表预览，前几行:")
                for i in range(min(len(preview_df), 5)):
                    print(f"行 {i}: {list(preview_df.iloc[i])[:10]}")
                
                # 直接使用第一行作为表头读取完整数据
                mapping_df = read_excel_safely(mapping_file, header=0)  # 图床映射表第一行表头
                print(f"成功读取文件: {mapping_file}")
                
                # 检查必要的列 - 精确匹配"SKU"列
                if 'SKU' in mapping_df.columns:
                    # 直接使用精确匹配的SKU列
                    mapping_df['msku'] = mapping_df['SKU']
                    print("使用精确匹配的'SKU'列")
                else:
                    # 如果没有精确匹配的SKU列，抛出异常
                    raise Exception(f"图床映射关系表缺少必要列: SKU")
                
                # 转换图片URL列为小写
                for col in mapping_df.columns:
                    if 'image' in str(col).lower() and 'url' in str(col).lower():
                        if col != col.lower():
                            mapping_df[col.lower()] = mapping_df[col]
                
                print(f"图床映射关系表读取完成，共 {len(mapping_df)} 行数据")
                print(f"图床映射关系表列名: {list(mapping_df.columns)}")
                
                # 检查有效的MSKU
                valid_msku_count = mapping_df['msku'].notna().sum()
                print(f"图床映射关系表中的有效MSKU数量: {valid_msku_count}")
                
                if valid_msku_count == 0:
                    raise ValueError("图床映射关系表没有有效的MSKU数据")
                
                # 标准化MSKU格式
                mapping_df['msku'] = mapping_df['msku'].astype(str).str.strip().str.upper()
                
            except Exception as e:
                raise Exception(f"读取图床映射关系表失败: {str(e)}")
            
            # 更新进度
            update_progress(status_label, progress_var, 50, "正在准备处理模板文件...")
            
            
            # 创建临时文件用于复制模板
            temp_dir = tempfile.mkdtemp()
            temp_template = os.path.join(temp_dir, f"temp_template_{int(time.time())}.xlsm")
            
            try:
                # 复制模板文件到临时目录
                print(f"复制模板文件到临时位置: {temp_template}")
                shutil.copy2(template_file, temp_template)
                
                # 提示用户选择保存路径
                output_file = filedialog.asksaveasfilename(
                    title="保存填充后的模板",
                    defaultextension=".xlsm",
                    filetypes=[("Excel文件", "*.xlsm")],
                    initialfile=f"填充后的亚马逊模板_{market}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsm"
                )
                
                if not output_file:
                    print("用户取消选择保存路径")
                    return
                
                # 更新进度
                update_progress(status_label, progress_var, 60, "正在分析模板文件结构...")
                
                # 使用pandas读取模板文件的基本信息
                try:
                    print(f"读取模板文件基本信息: {temp_template}")
                    excel_file = pd.ExcelFile(temp_template, engine='openpyxl')
                    sheet_names = excel_file.sheet_names
                    print(f"工作表列表: {sheet_names}")
                    
                    # 确定主要工作表
                    template_sheet_name = 'Template' if 'Template' in sheet_names else sheet_names[0]
                    print(f"使用工作表: {template_sheet_name}")
                    
                    # 读取前几行确定列结构
                    template_header_df = pd.read_excel(temp_template, sheet_name=template_sheet_name, engine='openpyxl', header=2, nrows=0)
                    template_headers = template_header_df.columns.tolist()
                    print(f"模板表头(第3行)包含 {len(template_headers)} 列")
                    
                    # 需要忽略的模板字段（非必要字段）
                    IGNORED_TEMPLATE_FIELDS = [
                        'currency', 'fulfillment_latency', 'map_price',
                        'offering_end_date', 'offering_start_date', 'package_contains_identifier',
                        'package_contains_quantity', 'package_level', 'product_site_launch_date',
                        'quantity', 'restock_date', 'target_audience_keywords', 'merchant_shipping_group_name',
                        'max_order_quantity', 'model_name', 'model', 'part_number', 
                        'supplier_declared_dg_hz_regulation1'
                    ]
                    IGNORED_TEMPLATE_FIELDS = [f.strip().lower() for f in IGNORED_TEMPLATE_FIELDS]

                    # 手动匹配关系（模板字段名: 手动匹配内容）
                    MANUAL_MAPPING = {
                        'sale_end_date': 'purchasable_offer[marketplace_id=ATVPDKIKX0DER]#1.discounted_price#1.schedule#1.end_at',
                        'sale_from_date': 'purchasable_offer[marketplace_id=ATVPDKIKX0DER]#1.discounted_price#1.schedule#1.start_at',
                        'sale_price': 'purchasable_offer[marketplace_id=ATVPDKIKX0DER]#1.discounted_price#1.schedule#1.value_with_tax',
                        'standard_price': 'purchasable_offer[marketplace_id=ATVPDKIKX0DER]#1.our_price#1.schedule#1.value_with_tax',
                    }
                    MANUAL_MAPPING = {k.strip().lower(): v for k, v in MANUAL_MAPPING.items()}
                    
                    # 立即显示字段映射关系
                    def show_mapping_info():
                        """只显示匹配结果表格，内容与debug_mapping_viewer.py一致"""
                        mapping_window = tk.Toplevel()
                        mapping_window.title("字段匹配关系")
                        mapping_window.geometry("700x600")
                        mapping_window.update_idletasks()
                        width = mapping_window.winfo_width()
                        height = mapping_window.winfo_height()
                        x = (mapping_window.winfo_screenwidth() // 2) - (width // 2)
                        y = (mapping_window.winfo_screenheight() // 2) - (height // 2)
                        mapping_window.geometry(f'{width}x{height}+{x}+{y}')

                        # 复用debug_mapping_viewer.py的build_match_table逻辑
                        def build_match_table(report_df, template_headers):
                            report_cols = {str(col).strip().lower(): col for col in report_df.columns}
                            template_cols = {str(col).strip().lower(): col for col in template_headers if col and pd.notna(col)}
                            rows = []
                            for tmpl_norm, tmpl_col in template_cols.items():
                                if tmpl_norm in IGNORED_TEMPLATE_FIELDS:
                                    continue
                                if tmpl_norm in report_cols:
                                    rows.append({
                                        "报告字段": report_cols[tmpl_norm],
                                        "模板字段": tmpl_col,
                                        "匹配状态": "精确匹配"
                                    })
                                elif tmpl_norm in MANUAL_MAPPING:
                                    rows.append({
                                        "报告字段": MANUAL_MAPPING[tmpl_norm],
                                        "模板字段": tmpl_col,
                                        "匹配状态": "手动匹配"
                                    })
                                else:
                                    rows.append({
                                        "报告字段": "",
                                        "模板字段": tmpl_col,
                                        "匹配状态": "未匹配"
                                    })
                            return pd.DataFrame(rows)

                        match_df = build_match_table(report_df, template_headers)

                        # Treeview三列
                        tree = ttk.Treeview(mapping_window, columns=("报告字段", "模板字段", "匹配状态"), show="headings")
                        tree.heading("报告字段", text="报告字段")
                        tree.heading("模板字段", text="模板字段")
                        tree.heading("匹配状态", text="匹配状态")
                        tree.column("报告字段", width=220)
                        tree.column("模板字段", width=220)
                        tree.column("匹配状态", width=100)
                        # 滚动条
                        scrollbar = ttk.Scrollbar(mapping_window, orient="vertical", command=tree.yview)
                        tree.configure(yscrollcommand=scrollbar.set)
                        scrollbar.pack(side="right", fill="y")
                        tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
                        # 插入数据
                        for _, row in match_df.iterrows():
                            tree.insert("", "end", values=(row["报告字段"], row["模板字段"], row["匹配状态"]))
                        # 导出按钮
                        def export_mapping():
                            save_path = filedialog.asksaveasfilename(
                                title="导出字段映射表",
                                defaultextension=".xlsx",
                                filetypes=[("Excel文件", "*.xlsx"), ("CSV文件", "*.csv")],
                                initialfile="字段匹配关系表.xlsx"
                            )
                            if not save_path:
                                messagebox.showinfo("提示", "未选择导出路径，操作已取消。")
                                return
                            if save_path.lower().endswith('.csv'):
                                match_df.to_csv(save_path, index=False, encoding='utf-8-sig')
                            else:
                                match_df.to_excel(save_path, index=False)
                            messagebox.showinfo("导出成功", f"字段映射表已导出到：\n{save_path}")
                        export_button = ttk.Button(mapping_window, text="导出映射关系", command=export_mapping)
                        export_button.pack(side="left", padx=10, pady=10)
                        continue_button = ttk.Button(mapping_window, text="继续填充模板", command=mapping_window.destroy)
                        continue_button.pack(side="right", padx=10, pady=10)
                        mapping_window.wait_window()
                    
                    # 显示映射关系窗口
                    if template_headers:
                        show_mapping_info()
                    
                    # 准备匹配
                    update_progress(status_label, progress_var, 65, "准备数据匹配...")
                    
                    # 复制模板文件到输出位置，而不是创建新文件
                    print(f"复制模板到输出文件: {output_file}")
                    shutil.copy2(temp_template, output_file)
                    
                    # 先获取匹配的数据
                    matched_data = []
                    processed_rows = 0
                    matched_count = 0
                    total_rows = len(mapping_df)
                    
                    print("开始匹配数据...")
                    print(f"【调试】图床映射表包含 {total_rows} 行数据")
                    print(f"【调试】分类商品报告包含 {len(report_df)} 行数据")
                    
                    # 检查数据格式
                    if 'msku' in mapping_df.columns:
                        print(f"【调试】图床映射表MSKU列前5个值: {mapping_df['msku'].head().tolist()}")
                    else:
                        print(f"【调试】错误: 图床映射表中没有找到msku列")
                        print(f"【调试】图床映射表实际列名: {mapping_df.columns.tolist()}")
                    
                    if 'item_sku' in report_df.columns:
                        print(f"【调试】分类商品报告item_sku列前5个值: {report_df['item_sku'].head().tolist()}")
                    else:
                        print(f"【调试】错误: 分类商品报告中没有找到item_sku列")
                        print(f"【调试】分类商品报告实际列名: {report_df.columns.tolist()}")
                    
                    # 图片字段列表
                    image_columns = [
                        'main_image_url', 'other_image_url1', 'other_image_url2',
                        'other_image_url3', 'other_image_url4', 'other_image_url5',
                        'other_image_url6', 'other_image_url7', 'other_image_url8',
                        'swatch_image_url'
                    ]

                    print(f"【调试】图床映射表实际列名: {mapping_df.columns.tolist()}")
                    print(f"【调试】检查图片URL列是否存在:")
                    for img_col in image_columns:
                        if img_col in mapping_df.columns:
                            print(f"  ✅ 找到: {img_col}")
                        else:
                            print(f"  ❌ 缺失: {img_col}")

                    # 优化：先批量构建结果数据
                    for idx, mapping_row in mapping_df.iterrows():
                        processed_rows += 1
                        msku = mapping_row['msku']
                        if pd.isna(msku):
                            print(f"【调试】跳过第{idx+1}行: MSKU为空")
                            continue
                        msku = str(msku).strip().upper()
                        
                        # 查找匹配的报告行
                        report_rows = report_df[report_df['item_sku'] == msku]
                        
                        if not report_rows.empty:
                            matched_count += 1
                            if matched_count <= 3:  # 只显示前3个匹配的详细信息
                                print(f"【调试】匹配成功第{matched_count}个: MSKU={msku}")
                            report_row = report_rows.iloc[0]
                            # 复制所有非图片字段
                            result_row = {col: report_row[col] for col in report_row.index if col not in image_columns}
                            result_row['item_sku'] = msku  # 确保SKU字段
                            
                            # 直接使用标准列名获取图片URL
                            for img_col in image_columns:
                                if img_col in mapping_df.columns:
                                    url_value = mapping_row[img_col]
                                    if pd.notna(url_value) and url_value:
                                        result_row[img_col] = str(url_value)
                                        if matched_count <= 3:
                                            print(f"  添加URL: {img_col} = {str(url_value)[:30]}...")
                                else:
                                    if matched_count <= 3:
                                        print(f"  图床映射表中缺少列: {img_col}")
                            
                            matched_data.append(result_row)
                        else:
                            if processed_rows <= 5:  # 只显示前5个未匹配的详细信息
                                print(f"【调试】未找到匹配第{processed_rows}个: MSKU={msku}")
                    
                    print(f"【调试】数据匹配完成: 处理了{processed_rows}行，成功匹配{matched_count}行")
                    
                    # 更新进度
                    update_progress(status_label, progress_var, 85, "数据填充完成，检查空值中...")
                    
                    if matched_data:
                        # 将匹配数据转换为DataFrame
                        result_df = pd.DataFrame(matched_data)
                        
                        print(f"【调试】准备写入数据:")
                        print(f"  匹配数据行数: {len(result_df)}")
                        if len(result_df) > 0:
                            print(f"  数据列数: {len(result_df.columns)}")
                            print(f"  前5列名: {result_df.columns.tolist()[:5]}")
                            print(f"  第1行前5个值: {result_df.iloc[0].head(5).tolist()}")
                            
                            # 检查关键字段
                            if 'item_sku' in result_df.columns:
                                print(f"  包含item_sku列，前3个值: {result_df['item_sku'].head(3).tolist()}")
                            else:
                                print(f"  ⚠️ 警告: result_df中没有item_sku列")
                                print(f"  实际列名: {result_df.columns.tolist()}")
                        else:
                            print("  ⚠️ 警告: result_df是空的!")
                            messagebox.showwarning("警告", "没有找到任何匹配的数据，无法填充模板。")
                            return
                        
                        # 使用xlwings直接操作Excel文件保留格式和VBA
                        # 复制完整的模板文件而不是重建
                        print(f"复制模板到输出文件: {output_file}")
                        shutil.copy2(template_file, output_file)
                        
                        # 添加默认值填充字段（移到try-except之前，确保两种方法都能使用）
                        DEFAULT_VALUES = {}
                        
                        # 根据不同市场设置不同的默认值
                        if market == '美国':
                            # 美国市场默认值
                            DEFAULT_VALUES = {
                                'are_batteries_included': 'No',
                                'supplier_declared_dg_hz_regulation2': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation3': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation4': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation5': 'Not Applicable',
                                'offering_can_be_giftwrapped': 'Yes',
                                'offering_can_be_gift_messaged': 'No',
                                'cpsia_cautionary_statement': 'NoWarningApplicable',
                                'country_of_origin': 'CHINA',
                                'warranty_description': '1-year warranty against defects.',
                                'number_of_items': '1',
                                'number_of_boxes': '1',
                                'external_product_id_type': 'ASIN',
                                'batteries_required': 'No',
                                # 美国市场履约中心ID
                                'fulfillment_center_id': 'AMAZON_NA',
                                # 美国单位默认值
                                'item_length_unit_of_measure': 'IN',
                                'item_width_unit_of_measure': 'IN',
                                'item_height_unit_of_measure': 'IN',
                                'length_longer_edge_unit_of_measure': 'Inches',
                                'width_shorter_edge_unit_of_measure': 'Inches',
                                'package_weight_unit_of_measure': 'OZ',
                                'package_length_unit_of_measure': 'IN',
                                'package_height_unit_of_measure': 'IN',
                                'package_width_unit_of_measure': 'IN',
                                'item_weight_unit_of_measure': 'OZ'
                            }
                            print(f"已设置美国市场的默认值字段")
                        elif market == '英国':
                            # 英国市场默认值 - 未来可以在此添加英国特定的默认值
                            DEFAULT_VALUES = {
                                'are_batteries_included': 'No',
                                'supplier_declared_dg_hz_regulation2': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation3': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation4': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation5': 'Not Applicable',
                                'offering_can_be_giftwrapped': 'Yes',
                                'offering_can_be_gift_messaged': 'No',
                                'cpsia_cautionary_statement': 'NoWarningApplicable',
                                'country_of_origin': 'CHINA',
                                'warranty_description': '1-year warranty against defects.',
                                'number_of_items': '1',
                                'number_of_boxes': '1',
                                'external_product_id_type': 'ASIN',
                                'batteries_required': 'No',
                                # 英国市场履约中心ID
                                'fulfillment_center_id': 'AMAZON_EU',
                                # 英国使用公制单位
                                'item_length_unit_of_measure': 'CM',
                                'item_width_unit_of_measure': 'CM',
                                'item_height_unit_of_measure': 'CM',
                                'length_longer_edge_unit_of_measure': 'CM',
                                'width_shorter_edge_unit_of_measure': 'CM',
                                'package_weight_unit_of_measure': 'GR',
                                'package_length_unit_of_measure': 'CM',
                                'package_height_unit_of_measure': 'CM',
                                'package_width_unit_of_measure': 'CM',
                                'item_weight_unit_of_measure': 'GR'
                            }
                            print(f"已设置英国市场的默认值字段")
                        else:
                            # 其他市场使用基本默认值
                            DEFAULT_VALUES = {
                                'are_batteries_included': 'No',
                                'supplier_declared_dg_hz_regulation2': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation3': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation4': 'Not Applicable',
                                'supplier_declared_dg_hz_regulation5': 'Not Applicable',
                                'offering_can_be_giftwrapped': 'Yes',
                                'offering_can_be_gift_messaged': 'No',
                                'cpsia_cautionary_statement': 'NoWarningApplicable',
                                'country_of_origin': 'CHINA',
                                'warranty_description': '1-year warranty against defects.',
                                'number_of_items': '1',
                                'number_of_boxes': '1',
                                'external_product_id_type': 'ASIN',
                                'batteries_required': 'No'
                            }
                            print(f"已设置基本默认值字段")
                        
                        # 将所有键转为小写
                        DEFAULT_VALUES = {k.lower(): v for k, v in DEFAULT_VALUES.items()}
                        
                        print("已设置默认值填充字段:")
                        for field, value in DEFAULT_VALUES.items():
                            print(f"  {field}: {value}")
                        
                        try:
                            import xlwings as xw
                            print("使用xlwings处理文件...")
                            
                            # 尝试加载xlwings，如果没有安装，则使用备用方法
                            app = xw.App(visible=False)
                            print("成功创建xlwings应用实例")
                            
                            try:
                                # 打开复制后的模板文件
                                print(f"尝试打开文件: {output_file}")
                                wb = app.books.open(output_file)
                                print("成功打开工作簿")
                                
                                # 获取Template工作表
                                try:
                                    sheet = wb.sheets[template_sheet_name]
                                    print(f"成功获取工作表: {template_sheet_name}")
                                except Exception as e:
                                    print(f"获取工作表 {template_sheet_name} 失败: {str(e)}")
                                    # 如果找不到Template工作表，尝试获取第一个工作表
                                    sheet = wb.sheets[0]
                                    template_sheet_name = sheet.name
                                    print(f"使用第一个工作表: {template_sheet_name}")
                                
                                # 清除现有数据（从第4行开始）
                                print("准备清除现有数据")
                                if sheet.range('A4').value is not None:
                                    last_row = sheet.used_range.last_cell.row
                                    if last_row > 3:
                                        print(f"清除第4行到第{last_row}行的数据")
                                        sheet.range(f'A4:XFD{last_row}').clear_contents()
                                        print("清除完成")
                                
                                # 获取列名到列索引的映射
                                print("查找列映射...")
                                header_row = sheet.range('A3').expand('right').value
                                if not isinstance(header_row, list):
                                    header_row = [header_row]
                                
                                # 打印找到的列头信息用于调试
                                print(f"模板中第3行(列头)包含 {len(header_row)} 个列:")
                                for i, col in enumerate(header_row):
                                    # 使用openpyxl的get_column_letter函数获取正确的列标识
                                    col_letter = get_column_letter(i+1)
                                    print(f"  列 {i+1} ({col_letter}): {col}")
                                
                                # 创建双向映射 - 不仅支持精确匹配，还支持模糊匹配
                                col_indices = {}
                                for i, col_name in enumerate(header_row):
                                    if col_name:  # 确保列名不为空
                                        col_indices[str(col_name).strip()] = i
                                
                                # 为图片URL列创建特殊映射
                                image_col_mapping = {}
                                for target_col in image_columns:
                                    target_col_lower = target_col.lower()
                                    
                                    # 精确映射优先 - 查找完全相同的列名
                                    exact_match_found = False
                                    for i, col_name in enumerate(header_row):
                                        if not col_name:
                                            continue
                                        col_name_str = str(col_name).lower().strip()
                                        
                                        # 精确匹配
                                        if col_name_str == target_col_lower:
                                            image_col_mapping[target_col] = i
                                            print(f"找到精确匹配: {target_col} -> {col_name} (列 {i+1})")
                                            exact_match_found = True
                                            break
                                    
                                    # 如果没有找到精确匹配，尝试部分匹配
                                    if not exact_match_found:
                                        # 特殊处理URL列，确保每个URL类型映射到自己对应的列
                                        for i, col_name in enumerate(header_row):
                                            if not col_name:
                                                continue
                                            col_name_str = str(col_name).lower().strip()
                                            
                                            # 只匹配特定的URL类型，避免所有URL映射到同一列
                                            url_type = target_col_lower.replace('_url', '').strip()
                                            if url_type in col_name_str and 'image' in col_name_str:
                                                # 确保main映射到main，other1映射到other1等
                                                if (('main' in url_type and 'main' in col_name_str) or
                                                    ('other' in url_type and 'other' in col_name_str and 
                                                     url_type[-1] == col_name_str[-1]) or
                                                    ('swatch' in url_type and 'swatch' in col_name_str)):
                                                    image_col_mapping[target_col] = i
                                                    print(f"找到部分匹配: {target_col} -> {col_name} (列 {i+1})")
                                                    break
                                    
                                # 打印映射信息
                                print(f"发现 {len(image_col_mapping)} 个图片URL列映射:")
                                for col, idx in image_col_mapping.items():
                                    print(f"  {col} -> 列 {idx+1} ({header_row[idx]})")
                                
                                # 准备手动映射的字典
                                manual_mapping_lower = {}
                                for k, v in MANUAL_MAPPING.items():
                                    manual_mapping_lower[k.lower()] = v
                                
                                # 设置起始行
                                start_row = 4  # 数据从第4行开始
                                
                                print(f"开始批量准备数据，共 {len(result_df)} 行...")
                                
                                # 批量准备数据
                                batch_data = []
                                matched_items = 0  # 初始化matched_items变量
                                
                                print(f"【调试】开始遍历result_df，共{len(result_df)}行")
                                
                                for index, row_data in result_df.iterrows():
                                    if matched_items < 3:  # 只为前3行显示详细调试信息
                                        print(f"\n【调试】处理第{index+1}行数据:")
                                        print(f"  row_data类型: {type(row_data)}")
                                        print(f"  row_data长度: {len(row_data)}")
                                        if 'item_sku' in row_data.index:
                                            print(f"  item_sku值: {row_data['item_sku']}")
                                        else:
                                            print(f"  ⚠️ 警告: row_data中没有item_sku字段")
                                            print(f"  row_data的前5个字段: {row_data.head().to_dict()}")
                                    
                                    sku_val = row_data.get('item_sku')
                                    if pd.isna(sku_val) or not sku_val:
                                        if matched_items < 3:
                                            print(f"  跳过: 无有效SKU值 (sku_val={sku_val})")
                                        continue
                                    
                                    # 按表头顺序创建一整行数据
                                    row_values = [None] * len(header_row)
                                    
                                    # 收集标准价格，用于list_price特殊处理
                                    standard_price_value = None
                                    # 收集size_name，用于size_map特殊处理
                                    size_name_value = None
                                    
                                    # 调试：显示当前skc_package_info状态
                                    print(f"\n【SKC调试】填充前skc_package_info状态:")
                                    print(f"  skc_package_info是否为空: {not skc_package_info}")
                                    print(f"  skc_package_info类型: {type(skc_package_info)}")
                                    if skc_package_info:
                                        print(f"  包含的SKC数量: {len(skc_package_info)}")
                                        print(f"  SKC列表: {list(skc_package_info.keys())[:5]}...")  # 只显示前5个
                                    else:
                                        print(f"  没有SKC包装信息可用")
                                    
                                    for i, col_name in enumerate(header_row):
                                        if not col_name:
                                            continue
                                        # 查找标准价格
                                        if col_name and str(col_name).lower() == 'standard_price':
                                            if col_name in row_data.index and pd.notna(row_data[col_name]):
                                                standard_price_value = row_data[col_name]
                                            # 检查手动映射
                                            elif col_name in MANUAL_MAPPING:
                                                manual_col = MANUAL_MAPPING[col_name]
                                                if manual_col in row_data and pd.notna(row_data[manual_col]):
                                                    standard_price_value = row_data[manual_col]
                                        
                                        # 查找size_name
                                        if col_name and str(col_name).lower() == 'size_name':
                                            if col_name in row_data.index and pd.notna(row_data[col_name]):
                                                size_name_value = row_data[col_name]
                                    
                                    if standard_price_value is not None:
                                        print(f"找到standard_price值: {standard_price_value}，将用于填充list_price")
                                    if size_name_value is not None:
                                        print(f"找到size_name值: {size_name_value}，将用于填充size_map")
                                    
                                    # 收集特殊"等于"关系的字段值
                                    package_height_value = None
                                    package_length_value = None
                                    package_weight_value = None
                                    package_width_value = None
                                    item_height_value = None
                                    item_length_value = None
                                    item_width_value = None
                                    
                                    # 仅从产品资料表格数据获取包装信息（改为SKC匹配）
                                    if skc_package_info and 'item_sku' in row_data:
                                        sku_val = row_data['item_sku']
                                        if pd.notna(sku_val):
                                            sku_str = str(sku_val).strip().upper()
                                            
                                            print(f"\n【填充调试】处理目标SKU: {sku_str}")
                                            
                                            # 特别关注问题SKU
                                            is_problem_sku = sku_str == '3DB-SJ08-0-0000355'
                                            if is_problem_sku:
                                                print(f"  ⭐【重点追踪】问题SKU: {sku_str}")
                                            
                                            # 直接使用SKU到SKC的映射进行匹配
                                            if sku_str in sku_to_skc_mapping:
                                                best_skc = sku_to_skc_mapping[sku_str]
                                                is_problem_skc = best_skc == 'DL-LW-PZ-BZ-02-050'
                                                
                                                if is_problem_sku or is_problem_skc:
                                                    print(f"  ⭐【重点追踪】找到SKU {sku_str} 的直接SKC匹配: {best_skc}")
                                                else:
                                                    print(f"【填充调试】找到SKU {sku_str} 的直接SKC匹配: {best_skc}")
                                                
                                                # 获取SKC的包装信息
                                                if best_skc in skc_package_info:
                                                    package_info = skc_package_info[best_skc]
                                                    
                                                    if is_problem_sku or is_problem_skc:
                                                        print(f"  ⭐【重点追踪】SKC {best_skc} 的包装信息: {package_info}")
                                                    else:
                                                        print(f"【填充调试】SKC {best_skc} 的包装信息: {package_info}")
                                                    
                                                    # 如果有包装信息，设置到对应字段
                                                    package_height_value = package_info.get('height')
                                                    package_length_value = package_info.get('length') 
                                                    package_weight_value = package_info.get('weight')
                                                    package_width_value = package_info.get('width')
                                                    
                                                    if is_problem_sku or is_problem_skc:
                                                        print(f"  ⭐【重点追踪】为SKU {sku_str} 从SKC {best_skc} 设置包装信息:")
                                                        print(f"    ⭐高度: {package_height_value}")
                                                        print(f"    ⭐长度: {package_length_value}")
                                                        print(f"    ⭐重量: {package_weight_value}")
                                                        print(f"    ⭐宽度: {package_width_value}")
                                                    else:
                                                        print(f"【填充调试】为SKU {sku_str} 从SKC {best_skc} 设置包装信息:")
                                                        print(f"  高度: {package_height_value}")
                                                        print(f"  长度: {package_length_value}")
                                                        print(f"  重量: {package_weight_value}")
                                                        print(f"  宽度: {package_width_value}")
                                                else:
                                                    print(f"【填充调试】错误: SKC {best_skc} 不在包装信息中")
                                            else:
                                                best_skc = None
                                                if is_problem_sku:
                                                    print(f"  ⭐【重点追踪】警告: 未找到SKU {sku_str} 的SKC匹配")
                                                else:
                                                    print(f"【填充调试】警告: 未找到SKU {sku_str} 的SKC匹配")
                                        else:
                                            print(f"【填充调试】跳过无效SKU: {sku_val}")
                                    else:
                                        if not skc_package_info:
                                            print(f"【填充调试】没有可用的产品资料包装信息")
                                        if 'item_sku' not in row_data:
                                            print(f"【填充调试】行数据中没有item_sku字段")
                                    
                                    # 填充数据到row_values数组
                                    for i, col_name in enumerate(header_row):
                                        if not col_name:
                                            continue
                                        
                                        # 忽略字段直接跳过
                                        if str(col_name).lower() in IGNORED_TEMPLATE_FIELDS:
                                            continue
                                        
                                        # 特殊处理字段
                                        col_name_lower = str(col_name).lower()
                                        
                                        # 处理list_price字段，使其值等于standard_price
                                        if col_name_lower == 'list_price' and standard_price_value is not None:
                                            row_values[i] = standard_price_value
                                            continue
                                        
                                        # 处理size_map字段，使其值等于size_name
                                        if col_name_lower == 'size_map' and size_name_value is not None:
                                            row_values[i] = size_name_value
                                            continue
                                        
                                        # 直接填充package字段值
                                        if col_name_lower == 'package_height' and package_height_value is not None:
                                            row_values[i] = package_height_value
                                            continue
                                            
                                        if col_name_lower == 'package_length' and package_length_value is not None:
                                            row_values[i] = package_length_value
                                            continue
                                            
                                        if col_name_lower == 'package_weight' and package_weight_value is not None:
                                            row_values[i] = package_weight_value
                                            continue
                                            
                                        if col_name_lower == 'package_width' and package_width_value is not None:
                                            row_values[i] = package_width_value
                                            continue
                                        
                                        # 特殊处理item_字段与package_字段的等于关系
                                        if col_name_lower == 'item_height' and package_height_value is not None:
                                            row_values[i] = package_height_value
                                            continue
                                        
                                        if col_name_lower == 'item_length' and package_length_value is not None:
                                            row_values[i] = package_length_value
                                            continue
                                        
                                        if col_name_lower == 'item_weight' and package_weight_value is not None:
                                            row_values[i] = package_weight_value
                                            continue
                                        
                                        if col_name_lower == 'item_width' and package_width_value is not None:
                                            row_values[i] = package_width_value
                                            continue
                                        
                                        # 处理length_longer_edge为最大值
                                        if col_name_lower == 'length_longer_edge':
                                            # 收集所有可能的尺寸值
                                            dimensions = []
                                            if package_height_value is not None:
                                                dimensions.append(float(package_height_value))
                                            if package_length_value is not None:
                                                dimensions.append(float(package_length_value))
                                            if package_width_value is not None:
                                                dimensions.append(float(package_width_value))
                                            
                                            # 如果有值，取最大值
                                            if dimensions:
                                                max_value = max(dimensions)
                                                row_values[i] = max_value
                                                print(f"【xlwings方法】设置length_longer_edge为最大值: {max_value}")
                                                continue
                                        
                                        # 处理width_shorter_edge为最小值
                                        if col_name_lower == 'width_shorter_edge':
                                            # 收集所有可能的尺寸值
                                            dimensions = []
                                            if package_height_value is not None:
                                                dimensions.append(float(package_height_value))
                                            if package_length_value is not None:
                                                dimensions.append(float(package_length_value))
                                            if package_width_value is not None:
                                                dimensions.append(float(package_width_value))
                                            
                                            # 如果有值，取最小值
                                            if dimensions:
                                                min_value = min(dimensions)
                                                row_values[i] = min_value
                                                print(f"【xlwings方法】设置width_shorter_edge为最小值: {min_value}")
                                                continue
                                        
                                        # 检查是否为默认值字段（不区分大小写）
                                        if col_name_lower in DEFAULT_VALUES:
                                            # 使用默认值填充，无论原值是否为空
                                            row_values[i] = DEFAULT_VALUES[col_name_lower]
                                            continue
                                        
                                        value = None
                                        
                                        # 精确匹配（字段名完全一致）
                                        if col_name in row_data.index:
                                            value = row_data[col_name]
                                        # 手动匹配
                                        elif col_name in MANUAL_MAPPING:
                                            manual_col = MANUAL_MAPPING[col_name]
                                            if manual_col in row_data:
                                                value = row_data[manual_col]
                                        
                                        # 填充有值的单元格
                                        if pd.notna(value) and value != '':
                                            row_values[i] = value
                                    
                                    batch_data.append(row_values)
                                    matched_items += 1
                                
                                # 批量写入数据
                                if batch_data:
                                    print(f"批量写入 {len(batch_data)} 行数据...")
                                    sheet.range(f'A{start_row}').value = batch_data
                                    print(f"数据批量写入完成，共写入 {len(batch_data)} 行")
                                else:
                                    print("没有匹配到可写入的数据")
                                
                                print(f"数据填充完成，共填充 {matched_items}/{len(result_df)} 行数据")
                                
                                # 保存文件
                                print("正在保存文件...")
                                wb.save()
                                print(f"数据已写入到工作表 {template_sheet_name}")
                            
                            finally:
                                # 关闭文件和应用
                                try:
                                    wb.close()
                                    app.quit()
                                except:
                                    pass
                        
                        except (ImportError, Exception) as e:
                            print(f"xlwings不可用或出错 ({str(e)})，使用备用方法...")
                            
                            # 备用方法：使用openpyxl
                            from openpyxl import load_workbook
                            
                            try:
                                # 加载保存后的文件
                                wb = load_workbook(output_file, keep_vba=True)
                                
                                # 获取Template工作表
                                if template_sheet_name in wb.sheetnames:
                                    ws = wb[template_sheet_name]
                                    
                                    # 清除现有数据（从第4行开始）
                                    for row in range(4, ws.max_row + 1):
                                        for col in range(1, ws.max_column + 1):
                                            ws.cell(row=row, column=col).value = None
                                    
                                    # 批量准备数据 - 使用相同的批量处理逻辑
                                    print("使用备用方法批量准备数据...")
                                    batch_data = []
                                    start_row = 4
                                    matched_items = 0
                                    
                                    # 调试：显示当前skc_package_info状态（备用方法）
                                    print(f"\n【备用方法SKC调试】填充前skc_package_info状态:")
                                    print(f"  skc_package_info是否为空: {not skc_package_info}")
                                    print(f"  skc_package_info类型: {type(skc_package_info)}")
                                    if skc_package_info:
                                        print(f"  包含的SKC数量: {len(skc_package_info)}")
                                        print(f"  SKC列表: {list(skc_package_info.keys())[:5]}...")  # 只显示前5个
                                    else:
                                        print(f"  没有SKC包装信息可用")
                                    
                                    for index, row_data in result_df.iterrows():
                                        sku_val = row_data.get('item_sku')
                                        if pd.isna(sku_val) or not sku_val:
                                            continue
                                        
                                        # 为每一行创建一个字典，存储列索引和值
                                        row_cells = {}
                                        
                                        # 收集标准价格，用于list_price特殊处理
                                        standard_price_value = None
                                        # 收集size_name，用于size_map特殊处理
                                        size_name_value = None
                                        
                                        # 添加包装信息获取逻辑（与xlwings方法保持一致）
                                        package_height_value = None
                                        package_length_value = None
                                        package_weight_value = None
                                        package_width_value = None
                                        
                                        # 从产品资料表格数据获取包装信息（使用SKC聚合数据）
                                        if skc_package_info and 'item_sku' in row_data:
                                            sku_val = row_data['item_sku']
                                            if pd.notna(sku_val):
                                                sku_str = str(sku_val).strip().upper()
                                                
                                                print(f"\n【备用方法填充调试】处理目标SKU: {sku_str}")
                                                
                                                # 特别关注问题SKU
                                                is_problem_sku = sku_str == '3DB-SJ08-0-0000355'
                                                if is_problem_sku:
                                                    print(f"  ⭐【重点追踪】问题SKU: {sku_str}")
                                                
                                                # 直接使用SKU到SKC的映射进行匹配
                                                if sku_str in sku_to_skc_mapping:
                                                    best_skc = sku_to_skc_mapping[sku_str]
                                                    print(f"【备用方法填充调试】找到SKU {sku_str} 的直接SKC匹配: {best_skc}")
                                                    
                                                    # 获取SKC的包装信息
                                                    if best_skc in skc_package_info:
                                                        package_info = skc_package_info[best_skc]
                                                        
                                                        print(f"【备用方法填充调试】SKC {best_skc} 的包装信息: {package_info}")
                                                        
                                                        # 如果有包装信息，设置到对应字段
                                                        package_height_value = package_info.get('height')
                                                        package_length_value = package_info.get('length') 
                                                        package_weight_value = package_info.get('weight')
                                                        package_width_value = package_info.get('width')
                                                        
                                                        print(f"【备用方法填充调试】为SKU {sku_str} 从SKC {best_skc} 设置包装信息:")
                                                        print(f"  高度: {package_height_value}")
                                                        print(f"  长度: {package_length_value}")
                                                        print(f"  重量: {package_weight_value}")
                                                        print(f"  宽度: {package_width_value}")
                                                    else:
                                                        print(f"【备用方法填充调试】错误: SKC {best_skc} 不在包装信息中")
                                                else:
                                                    best_skc = None
                                                    print(f"【备用方法填充调试】警告: 未找到SKU {sku_str} 的SKC匹配")
                                            else:
                                                print(f"【备用方法填充调试】跳过无效SKU: {sku_val}")
                                        else:
                                            if not skc_package_info:
                                                print(f"【备用方法填充调试】没有可用的产品资料包装信息")
                                            if 'item_sku' not in row_data:
                                                print(f"【备用方法填充调试】行数据中没有item_sku字段")
                                        
                                        # 首先遍历表头找出特殊字段的值
                                        for cell_idx, cell in enumerate(ws[3], 1):
                                            if not cell.value or pd.isna(cell.value):
                                                continue
                                                
                                            # 查找standard_price
                                            if str(cell.value).lower() == 'standard_price':
                                                # 检查row_data中是否有此字段
                                                if cell.value in row_data.index and pd.notna(row_data[cell.value]):
                                                    standard_price_value = row_data[cell.value]
                                                # 检查手动映射
                                                elif cell.value in MANUAL_MAPPING:
                                                    manual_col = MANUAL_MAPPING[cell.value]
                                                    if manual_col in row_data and pd.notna(row_data[manual_col]):
                                                        standard_price_value = row_data[manual_col]
                                            
                                            # 查找size_name
                                            elif str(cell.value).lower() == 'size_name':
                                                if cell.value in row_data.index and pd.notna(row_data[cell.value]):
                                                    size_name_value = row_data[cell.value]
                                        
                                        if standard_price_value is not None:
                                            print(f"【备用方法】找到standard_price值: {standard_price_value}，将用于填充list_price")
                                        if size_name_value is not None:
                                            print(f"【备用方法】找到size_name值: {size_name_value}，将用于填充size_map")
                                        
                                        # 遍历表头的每个单元格
                                        for cell_idx, cell in enumerate(ws[3], 1):
                                            if not cell.value or pd.isna(cell.value):
                                                continue
                                                
                                            # 忽略字段直接跳过
                                            if str(cell.value).lower() in IGNORED_TEMPLATE_FIELDS:
                                                continue
                                            
                                            # 特殊处理list_price字段，使其值等于standard_price
                                            cell_value_lower = str(cell.value).lower()
                                            if cell_value_lower == 'list_price' and standard_price_value is not None:
                                                row_cells[cell_idx] = standard_price_value
                                                continue
                                            
                                            # 特殊处理size_map字段，使其值等于size_name
                                            if cell_value_lower == 'size_map' and size_name_value is not None:
                                                row_cells[cell_idx] = size_name_value
                                                continue
                                            
                                           
                                            # 直接填充package字段值
                                            if cell_value_lower == 'package_height' and package_height_value is not None:
                                                row_cells[cell_idx] = package_height_value
                                                continue
                                                
                                            if cell_value_lower == 'package_length' and package_length_value is not None:
                                                row_cells[cell_idx] = package_length_value
                                                continue
                                                
                                            if cell_value_lower == 'package_weight' and package_weight_value is not None:
                                                row_cells[cell_idx] = package_weight_value
                                                continue
                                                
                                            if cell_value_lower == 'package_width' and package_width_value is not None:
                                                row_cells[cell_idx] = package_width_value
                                                continue
                                            
                                            # 特殊处理item_字段与package_字段的等于关系
                                            if cell_value_lower == 'item_height' and package_height_value is not None:
                                                row_cells[cell_idx] = package_height_value
                                                continue
                                            
                                            if cell_value_lower == 'item_length' and package_length_value is not None:
                                                row_cells[cell_idx] = package_length_value
                                                continue
                                            
                                            if cell_value_lower == 'item_weight' and package_weight_value is not None:
                                                row_cells[cell_idx] = package_weight_value
                                                continue
                                            
                                            if cell_value_lower == 'item_width' and package_width_value is not None:
                                                row_cells[cell_idx] = package_width_value
                                                continue
                                            
                                            # 处理length_longer_edge为最大值
                                            if cell_value_lower == 'length_longer_edge':
                                                # 收集所有可能的尺寸值
                                                dimensions = []
                                                if package_height_value is not None:
                                                    dimensions.append(float(package_height_value))
                                                if package_length_value is not None:
                                                    dimensions.append(float(package_length_value))
                                                if package_width_value is not None:
                                                    dimensions.append(float(package_width_value))
                                                
                                                # 如果有值，取最大值
                                                if dimensions:
                                                    max_value = max(dimensions)
                                                    row_cells[cell_idx] = max_value
                                                    print(f"【备用方法】设置length_longer_edge为最大值: {max_value}")
                                                    continue
                                            
                                            # 处理width_shorter_edge为最小值
                                            if cell_value_lower == 'width_shorter_edge':
                                                # 收集所有可能的尺寸值
                                                dimensions = []
                                                if package_height_value is not None:
                                                    dimensions.append(float(package_height_value))
                                                if package_length_value is not None:
                                                    dimensions.append(float(package_length_value))
                                                if package_width_value is not None:
                                                    dimensions.append(float(package_width_value))
                                                
                                                # 如果有值，取最小值
                                                if dimensions:
                                                    min_value = min(dimensions)
                                                    row_cells[cell_idx] = min_value
                                                    print(f"【备用方法】设置width_shorter_edge为最小值: {min_value}")
                                                    continue
                                            
                                            # 检查是否为默认值字段（不区分大小写）
                                            if cell_value_lower in DEFAULT_VALUES:
                                                # 使用默认值填充，无论原值是否为空
                                                row_cells[cell_idx] = DEFAULT_VALUES[cell_value_lower]
                                                continue
                                            
                                            value = None
                                            
                                            # 精确匹配（字段名完全一致）
                                            if cell.value in row_data.index:
                                                value = row_data[cell.value]
                                            # 手动匹配
                                            elif cell.value in MANUAL_MAPPING:
                                                manual_col = MANUAL_MAPPING[cell.value]
                                                if manual_col in row_data:
                                                    value = row_data[manual_col]
                                            
                                            # 填充有值的单元格
                                            if pd.notna(value) and value != '':
                                                row_cells[cell_idx] = value
                                        
                                        batch_data.append(row_cells)
                                        matched_items += 1
                                    
                                    # 批量写入数据到Excel
                                    if batch_data:
                                        print(f"批量写入 {len(batch_data)} 行数据...")
                                        for row_idx, row_data in enumerate(batch_data):
                                            actual_row = start_row + row_idx
                                            for col_idx, value in row_data.items():
                                                ws.cell(row=actual_row, column=col_idx).value = value
                                        print(f"数据批量写入完成")
                                    else:
                                        print("没有匹配到可写入的数据")
                                else:
                                    print(f"警告: 在备用方法中未找到模板工作表 {template_sheet_name}")
                            except Exception as e:
                                print(f"备用方法出错: {str(e)}")
                                raise
                        
                        # 完成
                        update_progress(status_label, progress_var, 90, "填充完成，正在检查空值...")
                        
                        # 检查SKU对应行中其他列的空值并导出警告
                        try:
                            print("检查填充后的数据空值情况...")
                            # 优化：限制处理的最大行数，避免处理过多数据导致卡顿
                            MAX_ROWS_TO_CHECK = 1000
                            
                            # 读取已填充的文件
                            filled_wb = openpyxl.load_workbook(output_file, keep_vba=True, read_only=True)  # 使用read_only模式提高性能
                            if template_sheet_name in filled_wb.sheetnames:
                                filled_ws = filled_wb[template_sheet_name]
                                
                                print(f"开始分析空值数据，可能需要几秒钟...")
                                
                                # 查找SKU列索引
                                sku_col_idx = None
                                header_row = 3  # 第3行是表头
                                
                                # 获取所有列头
                                header_cells = list(filled_ws.iter_rows(min_row=header_row, max_row=header_row, values_only=True))[0]
                                for col_idx, cell_value in enumerate(header_cells, 1):
                                    if cell_value and 'sku' in str(cell_value).lower():
                                        sku_col_idx = col_idx
                                        break
                                
                                if sku_col_idx:
                                    print(f"找到SKU列：{get_column_letter(sku_col_idx)}")
                                    
                                    # 收集SKU行和空值列
                                    sku_rows = []
                                    empty_cells = {}  # {row: {col: col_name}}
                                    empty_cols_count = defaultdict(int)  # 记录每列的空值数
                                    total_data_rows = 0
                                    
                                    # 确定最大行数
                                    max_row = min(filled_ws.max_row, MAX_ROWS_TO_CHECK + 3)  # +3是因为从第4行开始是数据
                                    print(f"分析数据行范围：4-{max_row}，共{max_row-3}行")
                                    
                                    # 一次性读取所有数据行到内存中
                                    print("正在读取数据...")
                                    data_rows = list(filled_ws.iter_rows(min_row=4, max_row=max_row, values_only=True))
                                    
                                    # 更新进度
                                    update_progress(status_label, progress_var, 92, "数据读取完成，正在分析空值...")
                                    
                                    # 遍历数据行
                                    print("开始分析空值...")
                                    for row_idx, row_data in enumerate(data_rows, 4):  # 从第4行开始是数据
                                        # 获取SKU值 (注意：sku_col_idx从1开始，但row_data从0开始，所以要减1)
                                        sku_value = row_data[sku_col_idx-1] if len(row_data) >= sku_col_idx else None
                                        
                                        if sku_value:  # 有SKU值的行
                                            total_data_rows += 1
                                            sku_rows.append(row_idx)
                                            empty_cells[row_idx] = {}
                                            
                                            # 检查该行其他列
                                            for col_idx, cell_value in enumerate(row_data, 1):
                                                # 跳过SKU列
                                                if col_idx == sku_col_idx:
                                                    continue
                                                    
                                                # 只检查有列名的列
                                                if col_idx <= len(header_cells) and header_cells[col_idx-1]:
                                                    col_name = header_cells[col_idx-1]
                                                    # 检查是否为空值
                                                    if cell_value is None or cell_value == '':
                                                        empty_cells[row_idx][col_idx] = col_name
                                                        empty_cols_count[col_idx] += 1
                                    
                                    # 更新进度
                                    update_progress(status_label, progress_var, 95, "空值分析完成，正在生成报告...")
                                    
                                    # 如果有SKU行存在空值列
                                    if sku_rows and any(empty_cells.values()):
                                        # 关闭只读模式的工作簿
                                        filled_wb.close()
                                        
                                        # 创建警告报告
                                        warning_wb = openpyxl.Workbook()
                                        warning_ws = warning_wb.active
                                        warning_ws.title = "空值警告"
                                        
                                        # 添加表头信息
                                        warning_ws.append(["SKU填充后空值警告报告"])
                                        warning_ws.append(["生成时间", datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                                        warning_ws.append(["填充文件", output_file])
                                        warning_ws.append(["总数据行数", str(total_data_rows)])
                                        warning_ws.append([])  # 空行
                                        
                                        # 筛选出需要显示的列（至少有一行为空的列）
                                        columns_to_show = []
                                        for col_idx, count in empty_cols_count.items():
                                            # 修改：排除整列都是空值的情况
                                            if count > 0 and count < total_data_rows:  # 有空值但不是所有行都空
                                                columns_to_show.append(col_idx)
                                        
                                        if columns_to_show:
                                            # 排序列以保持原顺序
                                            columns_to_show.sort()
                                            
                                            # 优化：使用简单的表格报告而非复制整个模板
                                            # 添加空值统计信息
                                            warning_ws.append(["字段名", "空值数量", "空值占比"])
                                            for col_idx in columns_to_show:
                                                col_name = header_cells[col_idx-1]
                                                empty_count = empty_cols_count[col_idx]
                                                empty_percent = f"{empty_count/total_data_rows*100:.1f}%"
                                                warning_ws.append([col_name, empty_count, empty_percent])
                                            
                                            # 计算总行数和总空值行数
                                            rows_with_empty = 0
                                            for row_idx in sku_rows:
                                                if row_idx - 4 >= len(data_rows):  # 确保索引有效
                                                    continue
                                                if empty_cells[row_idx]:  # 该行有空值列
                                                    rows_with_empty += 1
                                            
                                            # 设置单元格样式
                                            for row in warning_ws.iter_rows(min_row=6, max_row=6):  # 表头行
                                                for cell in row:
                                                    cell.font = openpyxl.styles.Font(bold=True)
                                            
                                            # 调整列宽
                                            warning_ws.column_dimensions['A'].width = 25
                                            warning_ws.column_dimensions['B'].width = 15
                                            warning_ws.column_dimensions['C'].width = 15
                                            
                                            # 保存警告报告
                                            warning_file = os.path.splitext(output_file)[0] + "_空值警告.xlsx"
                                            warning_wb.save(warning_file)
                                            
                                            print(f"发现 {rows_with_empty} 行数据存在空值，已导出警告报告: {warning_file}")
                                            
                                            # 更新进度到100%
                                            update_progress(status_label, progress_var, 100, "处理完成！")
                                            messagebox.showinfo("填充完成", 
                                                              f"模板填充完成！\n"
                                                              f"共处理 {processed_rows} 行数据\n"
                                                              f"成功匹配 {matched_count} 行\n"
                                                              f"发现 {rows_with_empty} 行数据存在空值\n"
                                                              f"已导出空值警告报告：{os.path.basename(warning_file)}\n"
                                                              f"已保存到：{output_file}")
                                            return
                                        else:
                                            print("未发现需要警告的空值列（排除整列都为空的情况）")
                                    else:
                                        print("未发现需要警告的空值")
                                else:
                                    print("未找到SKU列")
                            else:
                                print(f"未找到工作表 {template_sheet_name}")
                                
                            # 关闭工作簿
                            try:
                                filled_wb.close()
                            except:
                                pass
                                
                        except Exception as e:
                            print(f"检查空值时出错: {str(e)}")
                            traceback.print_exc()
                        
                        # 如果没有生成警告报告，显示原始完成消息
                        # 更新进度到100%
                        update_progress(status_label, progress_var, 100, "处理完成！")
                        messagebox.showinfo("完成", 
                                          f"模板填充完成！\n"
                                          f"共处理 {processed_rows} 行数据\n"
                                          f"成功匹配 {matched_count} 行\n"
                                          f"已保存到：{output_file}")
                    else:
                        messagebox.showwarning("警告", "没有找到任何匹配的数据，无法填充模板。")
                    
                except Exception as e:
                    error_msg = f"处理模板文件失败: {str(e)}\n\n{traceback.format_exc()}"
                    print(error_msg)
                    show_error_details(error_msg)
                    
                finally:
                    # 清理临时文件
                    try:
                        if os.path.exists(temp_template):
                            os.remove(temp_template)
                        if os.path.exists(temp_dir):
                            os.rmdir(temp_dir)
                    except:
                        print("无法清理临时文件")
                    
            except Exception as e:
                error_msg = f"处理文件失败: {str(e)}\n\n{traceback.format_exc()}"
                print(error_msg)
                show_error_details(error_msg)
            
            finally:
                # 关闭进度窗口
                try:
                    progress_window.destroy()
                except:
                    pass
        
        except Exception as e:
            error_msg = f"程序发生错误: {str(e)}\n\n{traceback.format_exc()}"
            print(error_msg)
            show_error_details(error_msg)
    
    # 确认按钮
    tk.Button(
        market_window,
        text="确认",
        command=on_market_selected,
        font=("Arial", 12),
        width=10
    ).pack(pady=20)
    
    # 运行市场选择窗口
    market_window.mainloop()

def show_main_menu():
    """显示主菜单"""
    # 清理可能存在的临时文件
    temp_files = ["test_upload.jpg", "test.jpg"]
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print(f"清理临时文件: {temp_file}")
            except Exception as e:
                print(f"无法删除临时文件 {temp_file}: {e}")
    
    # 主菜单窗口
    root = tk.Tk()
    root.title("亚马逊图片上传图床工具")
    root.geometry("450x550")  # 增加窗口高度，容纳更多按钮
    
    # 设置窗口图标
    try:
        # 尝试设置窗口图标
        import base64
        from io import BytesIO
        from PIL import Image, ImageTk
        # 简单的图标数据
        icon_data = """
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAQ4SURBVFiF7ZddiJVVFIafvfcZR8fRxnHMJitnHIUxsR+pMIyYwpsIpSKowlCKbrrMm4QI6rKbLoqMIii6KDFCg35Iwh9qLDCxH2fUtNSxGVSm0TnO2X1Za++vzr46c2ZQughacO7Wt9dee631rLXPgds4zSJNct+PiAcAtweWgbQge8SoVNlp4HOn9C7Irii8e3IyOv4HgIa1sRXQDlAC2QE6R7QvDXKuFmzjCeT+4+dmfDOVCF37KLaqoK0C7RR+A2WP8t+dNXKgQeg2kI3KvzWc4P5T55YcmlQERn26r5JcWwttY8HgJJFiC9gyZd08ZYM8GMFqEequO9s3lQheK3xDwGeBZy5+sQia1JIo7MP0GIOZlpvDXkWNQyoD6Nrn4clUAMaZrx34x4FvU143gs0Kn6ngUHCwReChkrIjGTenYJEwHEJdCY/4UkZwrQOotKyVxqe6Uj8GwHMnKkkgVOjpw7WFYLKqq9fqLcKpQh4ZUPRR5bY7simhVPaowoIa/5aD3eN74UxGdU/BTgTkTZVwdzZ1GJS9G7nZcz2JgOvgeO3GtIxrHpcotDF7RFvrtR1Q8QXdEOI9y7dGuaRMxpXiCPFhZl1oUOKdJgh9BCnrGFDvP+oYUPlCUfu9xAeYc3MAIrSKWH3IJoDjKgHqmQmBk07rocAQIJdJ7cBiFVZMACDKXGV8z7gBhEdJ/fdAPl7jB/hd+W2uzH8VPG/J3wGyEZgA4PaCrCkgqdazxFvfabZfWGBD+d3s2INDdf4aPxCdl4eFzRn1s4A1DC6aAMCEW4HybMaiBS6ZIT/KaO8YQ8rXOKTcNOsGeHY64bnGVc4XBF4DZhaytA3YDRUAQcZkATAzZXVRqA09ShvXOYcOK5x0wstvJZTu6+G1yQAoXJbkNxC2unI1BIBx6/JjV+ArEVkXYxwvxobyPW6IMNNLYy5cAF65/MWiqY/jKA+jUlH4AOTjukMomUIArLhITwz6Asgc76VqVta0uxP4SOCZIQgXjPB037kle24ugh1PPTKrEt1OC34lcBvwO8hWlL0q3L22m14ReXryAAQXdE+AWRl1O3DVibwnwsoY9Re8fQVlCsEqYOZYBHeS0s4YbIcrfXcNJ/75+Atn5lwr9dMHMG9Jcsi7eNApLyGsBt5AuBh9fB1s3SAAr7wKzKm1IoE+H2OvhXBnqZT2X+uR1BTouy7eb/5uviPZo4q/VYSuYYvvobwULc7JzT4I3AT0ix/I2pbaQ/SluAZmRGElcF91LJKifOXDbCuljpeu/HqXvxFRXdvzm+u3hpnUG1PssE+2ZfV0lTViRxHCzBB8R/Xl4wKwVY1wF/L6lV7mZfWaTyE8NVRvJeO0WbRdwLfD1XhtANXGcVfpkZGMag+y22A+8I/A5UJ9d1+6e0/3RPI3cKvlH36wdT7/ZYO+AAAAAElFTkSuQmCC
        """
        icon_data_decoded = base64.b64decode(icon_data)
        icon_image = Image.open(BytesIO(icon_data_decoded))
        photo_image = ImageTk.PhotoImage(icon_image)
        root.iconphoto(True, photo_image)
    except Exception as e:
        print(f"无法设置窗口图标: {str(e)}")
    
    # 标题标签
    tk.Label(root, text="亚马逊图片上传图床工具", font=("Arial", 16, "bold")).pack(pady=20)
    
    # 描述标签
    description = "本工具用于将产品图片上传到图床并生成亚马逊格式模板。"
    tk.Label(root, text=description, font=("Arial", 10), justify="center").pack(pady=5)
    
    # 创建按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    # 按钮通用参数
    button_width = 18  # 增加按钮宽度以适应较长文字
    button_height = 2
    button_font = ("SimHei", 10)  # 减小字体大小以适应更多文字
    
    # 上传新图片按钮
    upload_button = tk.Button(
        button_frame, 
        text="上传新图片", 
        command=lambda: [root.destroy(), start_upload()],
        font=button_font,
        width=button_width,
        height=button_height
    )
    upload_button.pack(pady=10)
    
    # 隐藏"查看历史映射表"按钮
    # history_button = tk.Button(
    #     button_frame,
    #     text="查看历史映射表",
    #     command=lambda: [root.destroy(), view_history_mappings()],
    #     font=button_font,
    #     width=button_width,
    #     height=button_height
    # )
    # history_button.pack(pady=10)
    
    # 删除"测试API连接"按钮
    # test_button = tk.Button(
    #     button_frame,
    #     text="测试API连接",
    #     command=lambda: [root.destroy(), show_test_results(test_api_connection())],
    #     font=button_font,
    #     width=button_width,
    #     height=button_height
    # )
    # test_button.pack(pady=10)
    
    # 删除"测试文件上传"按钮
    # test_upload_button = tk.Button(
    #     button_frame,
    #     text="测试文件上传",
    #     command=lambda: [root.destroy(), test_file_upload()],
    #     font=button_font,
    #     width=button_width,
    #     height=button_height
    # )
    # test_upload_button.pack(pady=10)
    
    # 新增"获取映射关系"按钮
    mapping_button = tk.Button(
        button_frame,
        text="获取映射关系",
        command=lambda: [root.destroy(), parse_urls_to_amazon_excel()],
        font=button_font,
        width=button_width,
        height=button_height
    )
    mapping_button.pack(pady=10)
    
    # 在button_frame中添加新按钮
    fill_template_button = tk.Button(
        button_frame,
        text="填充亚马逊模板",
        command=lambda: [root.destroy(), fill_amazon_template()],
        font=button_font,
        width=button_width,
        height=button_height
    )
    fill_template_button.pack(pady=10)
    
    # 添加测试按钮 - 使用特殊的字体和宽度设置
    test_report_button = tk.Button(
        button_frame,
        text="测试商品分类报告读取",
        command=lambda: [root.destroy(), test_report_data_extraction()],
        font=("SimHei", 9),  # 使用更小的字体
        width=20,  # 使用更宽的按钮
        height=button_height
    )
    test_report_button.pack(pady=10)
    
    # 退出按钮 - 放在最下面
    exit_button = tk.Button(
        button_frame,
        text="退出程序",
        command=lambda: [clean_temp_files(), root.destroy()],
        font=button_font,
        width=button_width,
        height=button_height
    )
    exit_button.pack(pady=10)
    
    # 定义清理临时文件函数
    def clean_temp_files():
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"退出前清理临时文件: {temp_file}")
                except:
                    pass
    
    # 处理窗口关闭事件
    def on_closing():
        clean_temp_files()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 显示API配置信息
    api_info = f"当前API: {API_URL}\nToken: {API_KEY[:4]}..."
    tk.Label(root, text=api_info, font=("Arial", 8), fg="gray").pack(side=tk.BOTTOM, pady=5)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    
    # 运行主循环
    root.mainloop()

def check_api_availability():
    """检测API接口是否可用，有错误立即提示并返回False"""
    print("检测API接口可用性...")
    
    # 创建一个测试图片 - 增加尺寸，避免服务器拒绝过小图片
    test_image = Image.new('RGB', (50, 50), color = 'white')
    img_byte_arr = io.BytesIO()
    test_image.save(img_byte_arr, format='JPEG')
    img_data = img_byte_arr.getvalue()
    
    # 检查并删除任何存在的测试文件
    temp_files = ["test_upload.jpg", "test.jpg"]
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print(f"清理旧测试文件: {temp_file}")
            except:
                pass
    
    # 准备请求参数 - 使用配置文件中的参数
    data = {
        "api_token": API_KEY,
        "upload_format": UPLOAD_FORMAT,
        "mode": UPLOAD_MODE,
        "uploadPath": "TEST"  # 测试图片固定上传到TEST目录
    }
    
    # 准备文件数据 - 使用配置文件中的参数名
    files = {FILE_PARAM_NAME: ('test.jpg', img_data, 'image/jpeg')}
    
    try:
        # 发送测试请求
        print(f"测试API接口: {API_URL}")
        response = requests.post(API_URL, data=data, files=files, timeout=10)
        
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"API响应内容: {response.text}")
        
        # 检查响应
        if response.status_code != 200:
            messagebox.showerror("API错误", f"API服务器返回错误状态码: {response.status_code}\n\n响应内容: {response.text}")
            return False
        
        # 尝试解析JSON响应
        try:
            result = response.json()
            
            # 检查是否有错误信息
            if 'status' in result and result['status'] == 'error':
                error_msg = result.get('resultData', '未知错误')
                
                # 如果错误包含Token相关信息，提示Token错误
                if 'token' in error_msg.lower() or 'api' in error_msg.lower():
                    messagebox.showerror("API密钥错误", f"API服务器报告密钥错误:\n\n{error_msg}\n\n请检查mohe_config.py文件中的API_KEY是否正确。")
                else:
                    messagebox.showerror("API错误", f"API服务器返回错误:\n\n{error_msg}\n\n请检查API配置是否正确。")
                return False
            
            # 检查是否有URL（表示上传成功）
            success = False
            if 'url' in result:
                success = True
            elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                success = True
            elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                success = True
            
            if not success:
                messagebox.showerror("API错误", f"API测试未返回有效URL\n\n响应内容: {response.text}\n\n请检查API配置是否正确。")
                return False
            
            # 测试成功
            print("✅ API测试成功，接口正常工作")
            
            # 测试结束，确保清理所有临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                        print(f"删除测试文件: {temp_file}")
                    except:
                        pass
            
            return True
            
        except Exception as e:
            messagebox.showerror("API响应解析错误", f"无法解析API响应:\n\n{str(e)}\n\n响应内容: {response.text}")
            return False
        
    except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
            requests.exceptions.ConnectionError, requests.exceptions.SSLError) as e:
        messagebox.showerror("API连接错误", f"无法连接到API服务器:\n\n{str(e)}\n\n请检查网络连接或API地址是否正确。")
        return False

def main():
    # 先清理可能存在的临时文件
    temp_files = ["test_upload.jpg", "test.jpg"]
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print(f"清理临时文件: {temp_file}")
            except Exception as e:
                print(f"无法删除临时文件 {temp_file}: {e}")

    try:
        # 通过对话框选择图片目录
        image_dir = select_image_directory()
        if not image_dir:
            print("用户取消了选择目录，程序终止")
            return
        # 新增：让用户输入一级目录名
        upload_folder = ask_upload_folder()
        if not upload_folder:
            print("用户取消了输入目录名，程序终止")
            return
        # 检测API接口是否可用，有错误立即停止
        if not check_api_availability():
            print("API接口检测失败，程序终止")
            return
        
        # 确保图片目录存在
        if not os.path.exists(image_dir):
            print(f"图片目录不存在: {image_dir}")
            return
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if os.path.isfile(os.path.join(image_dir, f)) and 
                      f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp'))]
        
        if not image_files:
            messagebox.showwarning("警告", f"在选定的目录中没有找到图片文件。\n支持的格式: PNG, JPG, JPEG, GIF, WEBP")
            return
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        # 验证图片文件名
        valid_files, invalid_files = validate_image_files(image_files)
        
        # 新增：图片数量≤350时提示
        if len(valid_files) <= 350:
            if not messagebox.askokcancel("提示", "图片数量≤350可以直接到图床上传"):
                print("用户取消上传")
                return
        
        # 如果有不符合要求的文件，显示警告
        if invalid_files:
            invalid_msg = "以下图片文件名不符合要求，将被跳过：\n\n"
            for file, reason in invalid_files[:10]:  # 最多显示10个无效文件
                invalid_msg += f"• {file}: {reason}\n"
            
            if len(invalid_files) > 10:
                invalid_msg += f"\n...等共 {len(invalid_files)} 个文件不符合要求。"
            
            invalid_msg += "\n\n文件名要求：\n1. 必须以B0开头（Amazon ASIN格式）\n2. 必须包含MAIN、PT或SWATCH关键词\n\n"
            
            if valid_files:
                invalid_msg += f"是否继续上传 {len(valid_files)} 个有效文件？"
                if not messagebox.askyesno("文件名格式警告", invalid_msg):
                    return
            else:
                messagebox.showerror("错误", "没有符合命名要求的图片文件，上传已取消。\n\n" + invalid_msg)
                return
        
        # 提醒用户图片命名规则
        if valid_files:
            reminder_msg = "上传前请确认图片命名规则：\n\n"
            reminder_msg += "1. 文件名以B0开头（Amazon ASIN格式）\n"
            reminder_msg += "2. 主图请包含MAIN关键词\n"
            reminder_msg += "3. 附图请包含PT01-PT08关键词\n"
            reminder_msg += "4. 色卡图请包含SWATCH关键词\n\n"
            reminder_msg += f"即将上传 {len(valid_files)} 个图片文件，使用优化后的上传设置：\n"
            reminder_msg += f"- 并发线程数: {DEFAULT_MAX_WORKERS}\n"
            reminder_msg += f"- 上传延迟: {DEFAULT_UPLOAD_DELAY}秒\n"
            reminder_msg += "- 保持原始图片质量\n\n"
            reminder_msg += "是否继续？"
            
            if not messagebox.askyesno("上传确认", reminder_msg):
                return
        
        # 自动设置最佳参数 - 不再询问用户
        max_workers = 5  # 并发线程数
        upload_delay = 0.2  # 上传延迟
        enable_optimization = False  # 禁用图片优化，直接上传原图
        
        print(f"使用最佳设置 - 并发线程数: {max_workers}, 上传延迟: {upload_delay}秒, 图片优化已禁用")
        
        # 计数器
        total = 0
        success = 0
        failed = 0
        
        # 筛选有效的图片文件
        image_files = valid_files
        
        # 准备待处理文件列表
        file_list = []
        for image_file in image_files:
            total += 1
            file_path = os.path.join(image_dir, image_file)
            file_list.append((image_file, file_path))
        
        # 收集图片数据
        image_data = []
        
        # 创建进度显示窗口
        progress_root = tk.Tk()
        progress_root.title("上传进度")
        progress_root.geometry("400x200")
        
        # 居中显示
        progress_root.update_idletasks()
        width = progress_root.winfo_width()
        height = progress_root.winfo_height()
        x = (progress_root.winfo_screenwidth() // 2) - (width // 2)
        y = (progress_root.winfo_screenheight() // 2) - (height // 2)
        progress_root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # 创建进度标签
        status_label = tk.Label(progress_root, text=f"正在上传图片... (0/{total})", font=("Arial", 12))
        status_label.pack(pady=20)
        
        # 创建进度条
        import tkinter.ttk as ttk
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_root, variable=progress_var, maximum=100)
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        
        # 详细状态标签
        detail_label = tk.Label(progress_root, text="准备上传...", font=("Arial", 10))
        detail_label.pack(pady=10)
        
        # 更新进度函数
        def update_progress():
            progress_var.set((success + failed) / total * 100)
            status_label.config(text=f"正在上传图片... ({success + failed}/{total})")
            detail_label.config(text=f"成功: {success} | 失败: {failed}")
            progress_root.update()
        
        # 初始化进度
        update_progress()
        
        # 使用线程池并行上传
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(process_file_with_optimization, file_info, upload_folder): file_info for file_info in file_list}
            for future in concurrent.futures.as_completed(future_to_file):
                file_info = future_to_file[future]
                image_file = file_info[0]
                try:
                    result = future.result()
                    if result:
                        image_file, url, status = result
                        if status == "success":
                            success += 1
                        elif status == "failed" or status == "error":
                            failed += 1
                        if status != "failed" and status != "error":
                            asin = extract_asin(image_file)
                            image_data.append((asin, image_file, url))
                except Exception as e:
                    print(f"处理 {image_file} 时发生异常: {str(e)}")
                    failed += 1
                update_progress()
                if upload_delay > 0:
                    time.sleep(upload_delay)
        
        # 关闭进度窗口
        progress_root.destroy()
        
        # 如果没有成功上传任何图片，显示错误并退出
        if success == 0:
            messagebox.showerror("上传失败", "没有成功上传任何图片，请检查网络连接和API设置。")
            return
        
        # 处理数据为亚马逊模板格式
        asin_groups = process_images_for_amazon(image_data)
        
        # 生成Markdown内容
        md_content = "# 图片URL映射表\n\n"
        md_content += "| 图片文件名 | 图片URL |\n"
        md_content += "|------------|--------|\n"
        
        for asin, filename, url in image_data:
            md_content += f"| {filename} | {url} |\n"
        
        # 保存到历史记录
        history_path = save_to_history(md_content, image_dir)
        
        # 生成Excel文件
        excel_path = os.path.join(os.path.dirname(history_path), f"亚马逊图片模板_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        save_to_amazon_excel(asin_groups, excel_path)
        
        print(f"完成! 成功上传 {success}/{total} 个文件，失败 {failed} 个")
        print(f"历史记录已保存到: {history_path}")
        print(f"Excel模板已保存到: {excel_path}")
        
        # 显示完成消息
        messagebox.showinfo("上传完成", 
                          f"已处理 {total} 个图片文件:\n"
                          f"✅ 成功: {success} 个\n"
                          f"❌ 失败: {failed} 个\n\n"
                          f"历史记录已保存到:\n{history_path}\n\n"
                          f"Excel模板已保存到:\n{excel_path}")

    finally:
        # 确保在程序退出时清理所有临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"清理临时文件: {temp_file}")
                except Exception as e:
                    print(f"无法删除临时文件 {temp_file}: {e}")

# 配置日志记录
def setup_logging():
    """设置日志记录"""
    # 创建logs目录（如果不存在）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成日志文件名（使用时间戳）
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"category_report_test_{timestamp}.log")
    
    # 配置日志记录器
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return log_file

def test_report_data_extraction():
    """测试从商品分类报告中提取数据的功能"""
    # 设置日志记录
    log_file = setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("开始测试商品分类报告数据提取功能")
    
    try:
        # 选择报告文件
        file_path = filedialog.askopenfilename(
            title="选择商品分类报告文件",
            filetypes=[
                ("Excel文件", "*.xlsx;*.xls;*.xlsm"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            logger.info("未选择文件")
            return
            
        logger.info(f"选择的商品分类报告文件: {file_path}")
        
        # 读取Excel文件
        try:
            # 获取所有工作表
            xls = pd.ExcelFile(file_path)
            sheet_names = xls.sheet_names
            logger.info(f"商品分类报告文件包含的工作表: {sheet_names}")
            
            # 使用Template工作表
            sheet_name = 'Template'
            logger.info(f"使用{sheet_name}工作表")
            
            # 读取前10行数据用于调试
            df_preview = pd.read_excel(file_path, sheet_name=sheet_name, nrows=10)
            logger.info(f"预览工作表 {sheet_name} 的前 10 行数据:")
            
            # 打印每一行的内容
            for idx, row in df_preview.iterrows():
                row_data = {f"列{i+1}:'{val}'" for i, val in enumerate(row) if pd.notna(val)}
                logger.info(f"行 {idx+1} (索引 {idx}): {', '.join(row_data)}")
                # 检查是否包含sku或item
                row_str = ' '.join(str(val).lower() for val in row if pd.notna(val))
                logger.info(f"行 {idx+1} 包含'sku': {'sku' in row_str}, 包含'item': {'item' in row_str}")
            
            # 默认使用第3行作为表头（商品分类报告和亚马逊模版的标准格式，索引为2）
            logger.info("使用第3行作为表头读取数据")
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=2)
            
            # 记录列名
            logger.info(f"列名: {df.columns.tolist()}")
            
            # 检查是否包含必要的列
            required_columns = ['item_sku', 'item_name', 'main_image_url', 'other_image_url1']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                messagebox.showerror("错误", f"报告文件缺少必要的列: {', '.join(missing_columns)}")
                return
                
            # 显示数据预览
            preview_window = tk.Toplevel()
            preview_window.title("商品分类报告数据预览")
            preview_window.geometry("800x600")
            
            # 创建表格
            tree = ttk.Treeview(preview_window)
            tree["columns"] = list(df.columns)
            tree["show"] = "headings"
            
            # 设置列
            for column in df.columns:
                tree.heading(column, text=column)
                tree.column(column, width=100)
            
            # 添加数据
            for idx, row in df.head(10).iterrows():
                tree.insert("", "end", values=list(row))
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            
            # 布局
            tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            logger.info(f"成功读取数据，共 {len(df)} 行")
            
        except Exception as e:
            logger.error(f"读取Excel文件时出错: {str(e)}")
            messagebox.showerror("错误", f"读取Excel文件时出错: {str(e)}")
            return
            
    except Exception as e:
        logger.error(f"商品分类报告测试过程中出错: {str(e)}")
        messagebox.showerror("错误", f"商品分类报告测试过程中出错: {str(e)}")

# 添加智能SKC提取函数
def extract_skc_variants(sku):
    """
    提取SKC的多种变体，提高匹配成功率
    
    Args:
        sku: 原始SKU字符串
        
    Returns:
        list: 可能的SKC变体列表
    """
    if not sku or pd.isna(sku):
        return []
    
    sku_str = str(sku).strip().upper()
    skc_variants = [sku_str]  # 原始SKU作为第一个候选
    
    # 方法1: 按'-'分割，取第一部分
    if '-' in sku_str:
        skc_variants.append(sku_str.split('-')[0])
    
    # 方法2: 按'_'分割，取第一部分
    if '_' in sku_str:
        skc_variants.append(sku_str.split('_')[0])
    
    # 方法3: 移除最后的数字和字母组合（如去掉-BLK, -01, -RED等）
    import re
    patterns = [
        r'-[A-Z]{2,4}$',      # 移除-BLK, -RED, -BLUE等
        r'-\d{1,3}$',         # 移除-01, -02, -100等
        r'-[A-Z]\d*$',        # 移除-A1, -B, -C2等
        r'_[A-Z]{2,4}$',      # 移除_BLK, _RED等
        r'_\d{1,3}$',         # 移除_01, _02等
    ]
    
    for pattern in patterns:
        match = re.search(pattern, sku_str)
        if match:
            skc_candidate = sku_str[:match.start()]
            if skc_candidate and skc_candidate not in skc_variants:
                skc_variants.append(skc_candidate)
    
    # 方法4: 移除最后1-3个字符（处理简单的变体）
    for i in range(1, 4):
        if len(sku_str) > i:
            skc_candidate = sku_str[:-i]
            if skc_candidate and skc_candidate not in skc_variants:
                skc_variants.append(skc_candidate)
    
    return list(set(skc_variants))  # 去重

def find_best_skc_match(sku, available_skcs):
    """
    为给定的SKU找到最佳的SKC匹配
    
    Args:
        sku: 要匹配的SKU
        available_skcs: 可用的SKC集合
        
    Returns:
        str or None: 最佳匹配的SKC，如果没有找到则返回None
    """
    if not sku or not available_skcs:
        return None
    
    # 获取所有可能的SKC变体
    skc_variants = extract_skc_variants(sku)
    
    # 按优先级顺序查找匹配
    for skc_variant in skc_variants:
        if skc_variant in available_skcs:
            return skc_variant
    
    return None

if __name__ == "__main__":
    # 显示主菜单而不是直接开始上传
    show_main_menu() 