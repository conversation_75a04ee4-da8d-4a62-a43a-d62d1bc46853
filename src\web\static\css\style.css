/* 亚马逊图片上传图床工具 - Web版本样式 */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-bg: rgba(255, 255, 255, 0.95);
    --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 15px;
    --transition: all 0.3s ease;
}

body {
    background: var(--primary-gradient);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
}

.main-container {
    background: var(--light-bg);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin: 20px auto;
    max-width: 1200px;
    overflow: hidden;
}

.header {
    background: var(--secondary-gradient);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin: 15px 0;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    border: none;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin: 0 auto 20px;
}

.file-drop-zone {
    border: 3px dashed #667eea;
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    transition: var(--transition);
    cursor: pointer;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.file-drop-zone:hover {
    border-color: #764ba2;
    background: rgba(118, 75, 162, 0.1);
    transform: scale(1.02);
}

.file-drop-zone.dragover {
    border-color: var(--success-color);
    background: rgba(40, 167, 69, 0.1);
    transform: scale(1.05);
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: var(--transition);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

.progress {
    height: 20px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    transition: width 0.6s ease;
}

.result-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid var(--success-color);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.result-item:hover {
    transform: translateX(5px);
}

.result-item.error {
    border-left-color: var(--error-color);
}

.nav-tabs {
    border-bottom: none;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border-radius: 10px 10px 0 0;
    border: none;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    font-weight: 600;
    margin-right: 5px;
    padding: 15px 25px;
    transition: var(--transition);
}

.nav-tabs .nav-link:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

.tab-content {
    background: white;
    border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
    padding: 30px;
    min-height: 500px;
}

.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
}

.status-processing {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.history-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 15px;
    font-weight: 600;
}

.table tbody td {
    padding: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.url-input-area {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--hover-shadow);
}

.list-group-item {
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 5px;
    transition: var(--transition);
}

.list-group-item:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.alert {
    border-radius: 10px;
    border: none;
    padding: 20px;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-outline-secondary:hover {
    background: #6c757d;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        margin: 10px;
        border-radius: var(--border-radius);
    }

    .header {
        padding: 20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .feature-card {
        margin: 10px 0;
        padding: 20px;
    }

    .tab-content {
        padding: 20px;
    }

    .file-drop-zone {
        padding: 30px 20px;
        min-height: 150px;
    }

    .nav-tabs .nav-link {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
        overflow: hidden;
    }
}

@media (max-width: 576px) {
    .header h1 {
        font-size: 1.75rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .btn {
        font-size: 0.875rem;
        padding: 10px 20px;
    }

    .nav-tabs .nav-link {
        padding: 10px 12px;
        font-size: 0.8rem;
        margin-right: 2px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 选择文本样式 */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: inherit;
}

::-moz-selection {
    background: rgba(102, 126, 234, 0.3);
    color: inherit;
}

/* 旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinning {
    animation: spin 1s linear infinite;
}