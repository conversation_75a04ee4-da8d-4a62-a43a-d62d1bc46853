# Excel模板填充工具 📊

一个专门用于填充亚马逊Excel模板文件的Web应用程序，支持智能字段匹配和批量数据处理。

## ✨ 功能特点

- 🎯 **智能读取**：自动识别Excel模板和商品报告结构
- 🔍 **精确匹配**：区分大小写的字段精确匹配，同时支持左右两侧匹配
- ⚡ **批量填充**：高性能批量数据填充，优化的openpyxl操作
- 📊 **限制处理**：仅处理前300行数据，确保性能和稳定性
- 🎨 **格式保持**：保留模板原有格式、公式和宏
- 📈 **实时进度**：详细的处理进度显示和阶段指示器
- 🌐 **Web界面**：现代化的响应式Web界面

## 🚀 快速开始

### 1. 启动工具

双击运行 `启动模板填充工具.ps1` 脚本：

```powershell
.\启动模板填充工具.ps1
```

脚本会自动：
- 检查Python环境
- 安装必要依赖包
- 创建必要目录
- 启动Web服务器

### 2. 访问Web界面

启动成功后，打开浏览器访问：
```
http://localhost:5000
```

### 3. 使用方式

#### 方式一：上传文件模式
1. 选择"上传文件模式"
2. 上传模板文件（.xlsm）
3. 上传商品报告文件（.xlsm）
4. 设置最大处理行数（默认300）
5. 点击"开始填充模板"

#### 方式二：指定路径模式（推荐）
1. 选择"指定路径模式"
2. 使用预设的默认路径或手动输入：
   - 模板文件路径：`D:\华为家庭存储\工作\运营\Listing相关\模板\DECORATIVE_RIBBON_TRIM_US.xlsm`
   - 商品报告路径：`D:\华为家庭存储\工作\运营\Listing相关\Listing\US\分类商品报告+05-27-2025.xlsm`
3. 设置处理选项
4. 点击"开始填充模板"

## 📋 技术要求

### 系统要求
- Windows 11 (或Windows 10)
- Python 3.7+
- 至少500MB可用内存

### 依赖包
- Flask (Web框架)
- pandas (数据处理)
- openpyxl (Excel文件操作)
- flask-cors (跨域支持)

### 文件要求
- **模板文件**：必须包含"Template"工作表，表头在第3行
- **商品报告**：必须包含SKU相关列（item_sku、sku、ITEM_SKU等）
- **文件格式**：支持.xlsx、.xlsm、.xls格式

## 🔧 处理流程

1. **初始化**：检查文件和环境
2. **读取报告**：智能定位表头，读取商品数据
3. **分析模板**：读取Template工作表结构
4. **匹配数据**：精确+模糊字段匹配
5. **填充数据**：批量写入数据到模板
6. **保存文件**：生成带时间戳的输出文件

## 📊 性能指标

- **处理速度**：300行数据约5-10秒
- **内存占用**：通常不超过500MB
- **并发支持**：支持多个任务并发处理
- **文件大小**：支持最大100MB文件上传

## 🎯 字段匹配规则

### 精确匹配（优先）
- 列名完全相同（区分大小写）
- 例：`item_name` ↔ `item_name`

### 左右两侧匹配（备选）
- 模板列名包含在报告列名中
- 报告列名包含在模板列名中
- 例：`item_name` ↔ `product_item_name`

### SKU字段识别
自动识别以下SKU相关列名：
- `item_sku`
- `sku` 
- `ITEM_SKU`
- `SKU`
- `Item_Sku`

## 📁 输出文件

填充完成的文件会保存到 `填充后的模板/` 目录，文件名格式：
```
原模板名_已填充_YYYYMMDD_HHMMSS.xlsm
```

例如：
```
DECORATIVE_RIBBON_TRIM_US_已填充_20241206_143022.xlsm
```

## ⚠️ 注意事项

1. **行数限制**：建议不超过300行以确保性能
2. **文件占用**：处理期间请勿打开模板文件
3. **字段名称**：确保报告文件包含必要的SKU列
4. **网络端口**：确保5000端口未被占用
5. **内存管理**：大文件处理时注意内存使用情况

## 🛠️ 故障排除

### 常见问题

#### 无法启动服务
- 检查Python环境是否正确安装
- 确认5000端口未被占用
- 检查依赖包是否完整安装

#### 文件读取失败
- 确认文件路径正确
- 检查文件是否被其他程序占用
- 验证文件格式是否受支持

#### 字段匹配失败
- 检查商品报告是否包含SKU列
- 验证模板文件Template工作表结构
- 确认表头位置是否正确（第3行）

#### 内存不足
- 减少处理行数
- 关闭其他占用内存的程序
- 检查文件大小是否过大

## 📞 技术支持

如遇到问题，请检查：
1. 控制台输出的错误信息
2. 浏览器开发者工具的网络请求
3. 文件格式和结构是否符合要求

## 🔄 更新日志

### v1.0.0
- ✅ 基础模板填充功能
- ✅ Web界面支持
- ✅ 实时进度显示
- ✅ 双模式文件处理
- ✅ 批量高性能操作 