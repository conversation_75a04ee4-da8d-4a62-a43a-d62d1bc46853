# 🚀 亚马逊图片上传图床工具

> 一站式解决亚马逊商品图片上传和模板生成问题，支持批量上传、智能优化、自动分类

## ✨ 项目特点

- 🎯 **专为亚马逊设计**：完美支持ASIN格式文件名和Amazon图片分类
- 🚀 **高性能上传**：多线程并发+动态端口，支持2GB大批量文件
- 🤖 **智能图片优化**：自动压缩调整，保持最佳质量和尺寸比例
- 📊 **一键模板生成**：自动生成标准Amazon Excel模板，直接导入后台
- 🌐 **双界面支持**：Web版本+桌面版本，满足不同使用习惯
- 📈 **模块化架构**：新版项目结构，便于维护和扩展

## 🎯 快速开始

### 📦 环境准备

1. **安装Python 3.7+**
   ```bash
   # 检查Python版本
   python --version
   ```

2. **安装依赖包**
   ```bash
   # 使用国内镜像源（推荐）
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   
   # 或使用默认源
   pip install -r requirements.txt
   ```

3. **配置API密钥**
   ```bash
   # 复制配置模板
   copy config\mohe_config.example.py config\mohe_config.py
   
   # 编辑配置文件，填入您的API密钥
   notepad config\mohe_config.py
   ```

### 🚀 启动应用

#### 💡 推荐方式：一键启动
```bash
# Windows用户：双击运行
启动Web服务.bat                   # 开发环境启动器（推荐）

# 或使用完整功能启动器
scripts\windows\启动Web服务-新版.bat

# 命令行运行
python scripts/start_web.py        # Web版本（推荐）
python src/web_app.py              # 直接启动
```

#### 🔧 其他启动方式
```bash
# 新版模块化架构
python src/web/app.py              # 新版Web应用

# 传统兼容模式  
python src/web_app.py              # 兼容版Web应用（当前主要版本）
```

#### 🌐 访问地址
Web版本启动后，通常可在以下地址访问：
- **主要地址**：http://localhost:5000
- **动态端口**：http://localhost:5001（端口冲突时自动切换）
- **局域网访问**：支持同网络设备访问

## 📋 项目结构

```
亚马逊图片上传图床工具/
├── 📁 src/                          # 源代码目录
│   ├── 📁 web/                      # Web应用模块
│   │   ├── 📄 app.py               # Flask主应用
│   │   ├── 📁 routes/              # 路由蓝图
│   │   ├── 📁 templates/           # HTML模板
│   │   └── 📁 static/              # 静态资源
│   ├── 📁 core/                     # 核心业务逻辑
│   │   ├── 📄 config.py            # 统一配置管理
│   │   ├── 📄 upload_manager.py    # 上传管理器
│   │   ├── 📄 image_processor.py   # 图像处理
│   │   ├── 📄 excel_processor.py   # Excel处理
│   │   └── 📄 port_manager.py      # 端口管理
│   └── 📁 desktop/                  # 桌面应用模块
├── 📁 config/                       # 配置文件
├── 📁 scripts/                      # 启动和部署脚本
├── 📁 data/                         # 数据目录
│   └── 📁 uploads/                 # 上传文件临时存储
├── 📁 output/                       # 输出目录
│   ├── 📁 results/                 # 处理结果
│   ├── 📁 logs/                    # 日志文件
│   └── 📁 temp/                    # 临时文件
└── 📁 docs/                         # 文档目录
```

## 🎮 功能指南

### 🖼️ 图片上传功能

1. **文件命名规则**（重要）
   ```
   B0XXXXXXXX_MAIN.jpg     # 主图
   B0XXXXXXXX_PT01.jpg     # 第1张卖点图
   B0XXXXXXXX_PT02.jpg     # 第2张卖点图
   ...
   B0XXXXXXXX_PT08.jpg     # 第8张卖点图  
   B0XXXXXXXX_SWATCH.jpg   # 色卡图
   ```

2. **批量上传操作**
   - 选择包含图片的文件夹
   - 可选上传图片任务列表Excel（包含ASIN、SKU、MSKU信息）
   - 自动验证文件名格式
   - 高性能并发上传（支持30线程）

3. **图片优化**
   - 自动压缩过大图片（最大2000像素）
   - JPEG质量90%，PNG无损压缩
   - 仅在优化后文件更小时使用优化版本

### 📊 模板填充功能

1. **支持的文件类型**
   - Amazon模板文件（.xlsx/.xlsm）
   - 商品分类报告（.xlsx/.xlsm）
   - 图片映射表格（.xlsx/.xlsm）
   - 产品资料表格（.xlsx/.xlsm）

2. **智能匹配**
   - 自动匹配ASIN、SKU、MSKU字段
   - 支持多市场（US、UK、DE等）
   - 智能处理表头行位置

### 📈 历史记录管理

- 自动保存所有上传结果
- 生成标准Amazon Excel模板
- 支持下载历史文件
- 详细的上传日志记录

### 图片重命名功能

图片重命名功能可以将图片文件按照亚马逊标准格式重命名，支持以下功能：

1. **主图重命名**：将白底图重命名为`ASIN_MAIN.jpg`格式
2. **场景图重命名**：将场景图重命名为`ASIN_PT01.jpg`等格式
3. **色块图重命名**：将色块图重命名为`ASIN_SWCH.jpg`格式
4. **通用类型场景图**：根据宽度范围分类场景图

使用方法：
1. 准备包含SKU、ASIN和Color映射关系的Excel文件
2. 准备包含"白底图"、"场景图"、"卖点图"或"色块"子目录的图片文件夹
3. 在Web界面中选择"图片重命名"选项卡
4. 上传Excel文件和图片文件夹
5. 选择需要处理的图片类型
6. 点击"开始处理"按钮
7. 下载处理后的ZIP文件

## ⚙️ 高级配置

### 🔧 性能优化

```python
# 上传性能配置
DEFAULT_MAX_WORKERS = 30        # 最大并发线程数
DEFAULT_UPLOAD_DELAY = 0.01     # 上传间隔（秒）
MAX_RETRIES = 3                 # 最大重试次数
ENABLE_IMAGE_OPTIMIZATION = True # 启用图片优化
```

### 🌐 网络配置

```python
# API配置
API_URL = "您的图床API地址"
API_KEY = "您的API密钥"
UPLOAD_MODE = "cdn"
UPLOAD_FORMAT = "json"
```

### 📁 目录配置

项目会自动创建以下标准化目录：
- `data/uploads/` - 上传文件临时存储
- `output/results/` - 处理结果和Excel文件
- `output/temp/` - 临时文件
- `output/logs/` - 日志文件

## 🛠️ 故障排除

### 🚨 Bootstrap加载错误

如果在浏览器中看到 **"bootstrap is not defined"** 错误，请使用以下方法修复：

#### 🔧 一键修复工具（推荐）
```bash
# Windows用户 - 双击运行
scripts\windows\修复Bootstrap问题.bat

# 或者命令行运行
python scripts\check_bootstrap_status.py
```

#### 🛠️ 手动修复步骤
1. **检查静态文件**
   ```bash
   # 下载缺失的静态资源
   python scripts\download_offline_resources.py
   ```

2. **重启服务器**
   ```bash
   # 重新启动Web服务
   python src\web_app.py
   ```

3. **清除浏览器缓存**
   - 按 `Ctrl+Shift+R` 强制刷新页面
   - 或在开发者工具中清除缓存

#### 🔍 诊断方法
- 按 `F12` 打开浏览器开发者工具
- 查看 `Network` 标签页中的错误
- 检查 `Console` 标签页中的具体错误信息

#### 💡 备用方案
如果本地文件加载失败，页面会自动使用CDN版本的Bootstrap，确保功能正常使用。

### 启动问题

如果遇到启动失败，请按以下步骤解决：

#### 快速修复 ⭐
```bash
# 使用自动修复脚本
启动Web服务-修复版.bat
```

#### 手动修复
```bash
# 恢复必要文件
copy "archive\legacy\web_app.py" "."
copy "archive\legacy\amazon_image_uploader.py" "."
copy "archive\legacy\fill_template_web.py" "."

# 然后启动
python src/web_app.py
```

#### 常见错误

1. **模块导入失败**：运行修复脚本或手动恢复文件
2. **配置文件缺失**：检查 `config/mohe_config.py`
3. **端口被占用**：应用会自动寻找可用端口

详细故障排除请参考：[启动问题修复指南](docs/启动问题修复指南.md)

## 📖 更新日志

### v2.1.0 (最新)
- ✅ 全面重构项目结构，采用模块化架构
- ✅ 新增统一配置管理系统
- ✅ 优化启动脚本，支持多种启动方式
- ✅ 标准化目录结构，提升维护性
- ✅ 完善错误处理和兼容性

### v2.0.0
- ✅ 新增Web版本界面
- ✅ 支持动态端口管理
- ✅ 大幅提升上传性能
- ✅ 完善模板填充功能

## 📄 许可证

本项目基于MIT许可证开源，详见 [LICENSE](LICENSE) 文件。

---

💝 **感谢使用亚马逊图片上传图床工具！如有问题或建议，欢迎反馈。** 

## 功能特性

- 🖼️ 图片上传：支持批量上传图片到图床，自动生成映射表
- 📊 URL解析：解析已有URL，生成映射表
- 📝 模板填充：自动填充亚马逊模板
- 🔄 图片重命名：根据SKU-ASIN映射关系，自动重命名图片为亚马逊标准格式
- 📦 完整记录：保存所有历史映射表，方便查询 