# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - 核心模块
提供统一的核心功能接口
"""

import os
import sys
import threading
import requests

# 添加配置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
config_path = os.path.join(project_root, 'config')
if config_path not in sys.path:
    sys.path.append(config_path)

# 导入核心模块
from .config import Config
from .utils import *
from .image_optimizer import ImageOptimizer
from .image_uploader import ImageUploader
from .excel_processor import ExcelProcessor
from .template_filler_web import get_amazon_template_web as fill_amazon_template_web

# 全局配置实例
config = Config()

# 默认常量（向后兼容）
DEFAULT_MAX_WORKERS = 10
DEFAULT_UPLOAD_DELAY = 0.1
ENABLE_IMAGE_OPTIMIZATION = True
MAX_RETRIES = 3

# 创建全局实例（向后兼容）
try:
    from mohe_config import API_KEY, API_URL, FILE_PARAM_NAME, UPLOAD_MODE, UPLOAD_FORMAT
    
    # 创建全局上传器
    global_uploader = ImageUploader(
        api_key=API_KEY,
        api_url=API_URL,
        file_param_name=FILE_PARAM_NAME,
        upload_mode=UPLOAD_MODE,
        upload_format=UPLOAD_FORMAT,
        max_workers=DEFAULT_MAX_WORKERS,
        upload_delay=DEFAULT_UPLOAD_DELAY,
        max_retries=MAX_RETRIES
    )
    
    # 创建全局session和锁（向后兼容）
    session = global_uploader.session
    upload_lock = global_uploader.upload_lock
    
    # 向后兼容的函数接口
    def optimize_image(file_path):
        """优化图片（向后兼容接口）"""
        optimizer = ImageOptimizer()
        return optimizer.optimize_image(file_path)
    
    def upload_image_to_mohecdn(file_path, upload_folder=None):
        """上传图片到薄荷图床（向后兼容接口）"""
        return global_uploader.upload_image(file_path, upload_folder=upload_folder, optimize=ENABLE_IMAGE_OPTIMIZATION)
    
    def test_api_connection():
        """测试API连接（向后兼容接口）"""
        return global_uploader.test_connection()
    
    def save_to_amazon_excel(data_dict, output_path, title="亚马逊图片模板"):
        """保存到Excel文件（向后兼容接口）"""
        processor = ExcelProcessor()
        return processor.save_to_amazon_excel(data_dict, output_path, title)
    
    print("✅ 核心模块初始化成功")
    
except ImportError as e:
    print(f"⚠️ 核心模块初始化部分功能失败: {e}")
    # 创建基本的session和锁
    session = requests.Session()
    upload_lock = threading.Lock()
    
    # 提供基本的函数存根
    def optimize_image(file_path):
        """图片优化功能不可用"""
        raise NotImplementedError("图片优化功能需要完整的配置")
    
    def upload_image_to_mohecdn(file_path, upload_folder=None):
        """上传功能不可用"""
        raise NotImplementedError("上传功能需要完整的配置")
    
    def test_api_connection():
        """API测试功能不可用"""
        raise NotImplementedError("API测试功能需要完整的配置")
    
    def save_to_amazon_excel(data_dict, output_path, title="亚马逊图片模板"):
        """Excel保存功能基本可用"""
        try:
            processor = ExcelProcessor()
            return processor.save_to_amazon_excel(data_dict, output_path, title)
        except:
            raise NotImplementedError("Excel处理功能需要openpyxl库")

# 导出图片重命名功能
try:
    from .image_renamer import ImageRenamer, process_rename_task, analyze_color_matching
except ImportError:
    print("⚠️ 图片重命名模块导入失败")

# 导出所有函数和类
__all__ = [
    'Config', 'config',
    'ImageOptimizer', 'ImageUploader', 'ExcelProcessor',
    'optimize_image', 'upload_image_to_mohecdn', 'test_api_connection', 'save_to_amazon_excel',
    'fill_amazon_template_web',
    'session', 'upload_lock',
    'DEFAULT_MAX_WORKERS', 'DEFAULT_UPLOAD_DELAY', 'ENABLE_IMAGE_OPTIMIZATION', 'MAX_RETRIES',
    # 从utils导入的函数
    'determine_image_type', 'extract_asin', 'validate_image_filename', 
    'ensure_history_dir', 'process_images_for_amazon'
] 