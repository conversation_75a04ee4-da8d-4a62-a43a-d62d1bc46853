# 亚马逊图片上传图床工具 - Web服务启动器 (动态端口版本)
# PowerShell版本

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# 设置窗口标题
$Host.UI.RawUI.WindowTitle = "亚马逊图片上传图床工具 - Web服务 (动态端口)"

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "  亚马逊图片上传图床工具 - Web服务启动器" -ForegroundColor White
Write-Host "  🚀 支持动态端口分配，自动避免端口冲突" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python环境
Write-Host "🔍 检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 已找到Python环境" -ForegroundColor Green
        Write-Host "📊 Python版本: $pythonVersion" -ForegroundColor Blue
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ 未找到Python，请确保已安装Python并添加到PATH环境变量" -ForegroundColor Red
    Write-Host "   您可以从 https://www.python.org/downloads/ 下载安装Python" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查PowerShell执行策略
Write-Host ""
Write-Host "🔍 检查PowerShell执行策略..." -ForegroundColor Yellow
$executionPolicy = Get-ExecutionPolicy
if ($executionPolicy -eq "Restricted") {
    Write-Host "⚠️ PowerShell执行策略受限，可能影响某些功能" -ForegroundColor Yellow
    Write-Host "   建议运行: Set-ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Yellow
} else {
    Write-Host "✅ PowerShell执行策略: $executionPolicy" -ForegroundColor Green
}

# 检查依赖
Write-Host ""
Write-Host "🔍 检查项目依赖..." -ForegroundColor Yellow
if (Test-Path "requirements.txt") {
    Write-Host "📦 正在安装依赖，请稍候..." -ForegroundColor Blue
    try {
        python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 依赖安装完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 部分依赖可能安装失败，但将继续尝试启动服务" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ 依赖安装过程中出现问题，但将继续启动" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ 未找到requirements.txt文件，跳过依赖检查" -ForegroundColor Yellow
}

# 检查配置文件
Write-Host ""
Write-Host "🔍 检查配置文件..." -ForegroundColor Yellow
$configPath = "config\mohe_config.py"
$exampleConfigPath = "config\mohe_config.example.py"

if (-not (Test-Path $configPath)) {
    if (Test-Path $exampleConfigPath) {
        Write-Host "📝 未找到配置文件，正在从示例创建..." -ForegroundColor Blue
        Copy-Item $exampleConfigPath $configPath
        Write-Host "✅ 已创建配置文件 $configPath" -ForegroundColor Green
        Write-Host "   请根据需要修改配置文件中的API密钥等信息" -ForegroundColor Yellow
    } else {
        Write-Host "⚠️ 未找到配置文件和示例文件，服务可能无法正常工作" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ 已找到配置文件 $configPath" -ForegroundColor Green
}

# 确保必要的目录存在
Write-Host ""
Write-Host "🔍 检查必要目录..." -ForegroundColor Yellow
$directories = @("uploads", "temp", "历史映射表", "填充后的模板", "data", "data\uploads", "output", "output\logs")

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}
Write-Host "✅ 已确认必要目录存在" -ForegroundColor Green

# 检查端口管理器
Write-Host ""
Write-Host "🔍 检查动态端口管理器..." -ForegroundColor Yellow
try {
    $output = python -c "from core.port_manager import get_available_port; print('✅ 动态端口管理器可用')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 动态端口管理器已准备就绪" -ForegroundColor Green
    } else {
        throw "Port manager not available"
    }
} catch {
    Write-Host "⚠️ 动态端口管理器不可用，将使用默认端口" -ForegroundColor Yellow
}

# 检查当前端口占用情况
Write-Host ""
Write-Host "🔍 检查端口占用情况..." -ForegroundColor Yellow
try {
    $portCheck = netstat -an | Select-String ":5000"
    if ($portCheck) {
        Write-Host "⚠️ 端口5000已被占用，将自动分配其他可用端口" -ForegroundColor Yellow
        # 显示占用端口的进程信息
        $processInfo = netstat -ano | Select-String ":5000" | Select-String "LISTENING"
        if ($processInfo) {
            Write-Host "📊 端口占用详情:" -ForegroundColor Blue
            $processInfo | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
        }
    } else {
        Write-Host "✅ 端口5000可用" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 无法检查端口状态" -ForegroundColor Yellow
}

# 启动Web服务
Write-Host ""
Write-Host "🚀 正在启动Web服务..." -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "  ✨ 动态端口分配功能：" -ForegroundColor White
Write-Host "  ├─ 自动检测可用端口" -ForegroundColor Green
Write-Host "  ├─ 避免端口冲突" -ForegroundColor Green
Write-Host "  ├─ 显示所有访问地址" -ForegroundColor Green
Write-Host "  └─ 自动打开浏览器" -ForegroundColor Green
Write-Host ""
Write-Host "  💡 服务启动后将显示实际访问地址" -ForegroundColor Blue
Write-Host "  📱 支持局域网访问" -ForegroundColor Blue
Write-Host "  🛑 按Ctrl+C可以停止服务" -ForegroundColor Blue
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

# 启动Python Web服务
try {
    python web_app.py
} catch {
    Write-Host "❌ Web服务启动失败" -ForegroundColor Red
    Write-Host "错误信息: $_" -ForegroundColor Red
} finally {
    # 如果Python服务退出，显示提示
    Write-Host ""
    Write-Host "👋 Web服务已停止运行" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 提示:" -ForegroundColor Blue
    Write-Host "   ├─ 如果遇到端口冲突，程序已自动处理" -ForegroundColor Green
    Write-Host "   ├─ 如果需要指定端口，请修改web_app.py" -ForegroundColor Green
    Write-Host "   └─ 查看日志获取更多信息" -ForegroundColor Green
    Write-Host ""
    Read-Host "按回车键退出"
} 