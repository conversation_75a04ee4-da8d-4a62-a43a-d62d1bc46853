# 桌面版参考代码 📖

## 📋 说明

这个目录保存了亚马逊图片上传图床工具的桌面版核心源代码，用于参考原始程序的逻辑结构。

**注意**: 当前项目主要使用Web版本，桌面版代码仅作为逻辑参考保留。

## 📁 文件说明

### `main.py` - 桌面版核心逻辑
- **文件大小**: 211KB (4004行代码)
- **功能**: 桌面版图片上传工具的完整实现
- **用途**: 作为Web版本开发的逻辑参考

## 🔍 主要功能模块

### 1. 图片文件验证
- Amazon ASIN格式验证（B0开头）
- 图片类型识别（MAIN、PT01-PT08、SWATCH）
- 文件格式支持（PNG、JPG、JPEG、GIF、WebP）

### 2. 图片上传逻辑
- 多线程并发上传
- 图片优化和压缩
- 上传进度显示
- 错误处理和重试机制

### 3. 用户界面
- Tkinter GUI界面
- 文件选择对话框
- 进度条显示
- 结果统计展示

### 4. 配置管理
- API接口配置
- 上传参数设置
- 历史记录管理

## 💡 与Web版本的对比

| 特性 | 桌面版 | Web版本 |
|------|--------|---------|
| 界面 | Tkinter GUI | Web界面 |
| 部署 | 独立exe文件 | Flask Web应用 |
| 使用方式 | 桌面应用程序 | 浏览器访问 |
| 文件选择 | 系统对话框 | 拖拽上传 |
| 进度显示 | 桌面弹窗 | Web页面 |

## 🎯 参考价值

1. **业务逻辑**: 图片验证、上传流程、错误处理
2. **算法实现**: 文件名解析、图片类型判断
3. **性能优化**: 并发控制、内存管理
4. **用户体验**: 进度反馈、错误提示

## 📝 开发历史

- 桌面版是项目的第一个版本
- 包含完整的图片上传业务流程
- Web版本基于桌面版逻辑重构
- 保留用于新功能开发时的逻辑参考

---

**最后更新**: 2025-06-17  
**维护状态**: 仅作参考，不再维护  
**当前版本**: 使用Web版本 (`src/web_app.py`) 