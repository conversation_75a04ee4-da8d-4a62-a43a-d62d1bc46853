<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel模板填充工具 📊</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1000px;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .file-drop-zone {
            border: 3px dashed #28a745;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            background: rgba(40, 167, 69, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            margin: 15px 0;
        }

        .file-drop-zone:hover {
            border-color: #20c997;
            background: rgba(32, 201, 151, 0.1);
        }

        .file-drop-zone.dragover {
            border-color: #fd7e14;
            background: rgba(253, 126, 20, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .progress {
            height: 25px;
            border-radius: 15px;
            background: rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 15px;
            transition: width 0.3s ease;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin: 15px 0;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-info {
            background: rgba(13, 202, 240, 0.1);
            color: #055160;
            border-left: 4px solid #0dcaf0;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }

        .file-info {
            background: rgba(40, 167, 69, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }

        .output-files {
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .file-item:hover {
            border-color: #28a745;
            transform: translateX(5px);
        }

        .stage-indicator {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
        }

        .stage-active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .stage-completed {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        .stage-pending {
            background: rgba(108, 117, 125, 0.2);
            color: #495057;
        }

        .custom-path-section {
            background: rgba(255, 193, 7, 0.1);
            border: 2px dashed #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-file-earmark-excel"></i> Excel模板填充工具</h1>
                <p class="mb-0">智能填充亚马逊Excel模板，支持批量处理300行数据 🚀</p>
            </div>

            <!-- 主要内容 -->
            <div class="p-4">
                <!-- 功能说明 -->
                <div class="feature-card">
                    <h4><i class="bi bi-info-circle text-primary"></i> 功能特点</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> 智能读取Excel模板和商品报告</li>
                                <li><i class="bi bi-check-circle text-success"></i> 精确字段匹配（区分大小写）</li>
                                <li><i class="bi bi-check-circle text-success"></i> 批量高性能数据填充</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> 仅处理前300行数据</li>
                                <li><i class="bi bi-check-circle text-success"></i> 保持原有格式和公式</li>
                                <li><i class="bi bi-check-circle text-success"></i> 实时进度显示</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="feature-card">
                    <h4><i class="bi bi-cloud-upload text-primary"></i> 文件上传</h4>
                    
                    <!-- 路径模式选择 -->
                    <div class="mb-3">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="pathMode" id="uploadMode" value="upload" checked>
                            <label class="form-check-label" for="uploadMode">
                                <i class="bi bi-upload"></i> 上传文件模式
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="pathMode" id="pathMode" value="path">
                            <label class="form-check-label" for="pathMode">
                                <i class="bi bi-folder"></i> 指定路径模式
                            </label>
                        </div>
                    </div>

                    <!-- 上传文件区域 -->
                    <div id="uploadSection">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label"><i class="bi bi-file-earmark-excel"></i> 模板文件 (.xlsm)</label>
                                <div class="file-drop-zone" onclick="document.getElementById('templateFile').click()">
                                    <i class="bi bi-file-earmark-excel" style="font-size: 2rem; color: #28a745;"></i>
                                    <p class="mt-2">点击选择或拖拽模板文件</p>
                                    <input type="file" id="templateFile" accept=".xlsx,.xlsm,.xls" style="display: none;">
                                </div>
                                <div id="templateFileInfo" class="file-info" style="display: none;"></div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><i class="bi bi-file-earmark-spreadsheet"></i> 商品报告文件 (.xlsm)</label>
                                <div class="file-drop-zone" onclick="document.getElementById('reportFile').click()">
                                    <i class="bi bi-file-earmark-spreadsheet" style="font-size: 2rem; color: #28a745;"></i>
                                    <p class="mt-2">点击选择或拖拽报告文件</p>
                                    <input type="file" id="reportFile" accept=".xlsx,.xlsm,.xls" style="display: none;">
                                </div>
                                <div id="reportFileInfo" class="file-info" style="display: none;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 自定义路径区域 -->
                    <div id="pathSection" class="custom-path-section" style="display: none;">
                        <h5><i class="bi bi-folder text-warning"></i> 自定义文件路径</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">模板文件路径</label>
                                <input type="text" class="form-control" id="templatePath" 
                                       placeholder="D:\路径\模板文件.xlsm">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">商品报告路径</label>
                                <input type="text" class="form-control" id="reportPath" 
                                       placeholder="D:\路径\商品报告.xlsm">
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                默认路径已预填充，您可以直接使用或修改
                            </small>
                        </div>
                    </div>

                    <!-- 处理选项 -->
                    <div class="mt-4">
                        <h5><i class="bi bi-gear text-primary"></i> 处理选项</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">最大处理行数</label>
                                <input type="number" class="form-control" id="maxRows" value="300" min="1" max="1000">
                                <small class="text-muted">建议不超过300行以确保性能</small>
                            </div>
                        </div>
                    </div>

                    <!-- 开始按钮 -->
                    <div class="mt-4 text-center">
                        <button class="btn btn-primary btn-lg" id="startButton" onclick="startFilling()">
                            <i class="bi bi-play-circle"></i> 开始填充模板
                        </button>
                    </div>
                </div>

                <!-- 进度显示区域 -->
                <div id="progressSection" class="feature-card" style="display: none;">
                    <h4><i class="bi bi-hourglass-split text-primary"></i> 处理进度</h4>
                    
                    <!-- 阶段指示器 -->
                    <div class="mb-3">
                        <span class="stage-indicator stage-pending" id="stage-init">初始化</span>
                        <span class="stage-indicator stage-pending" id="stage-read">读取报告</span>
                        <span class="stage-indicator stage-pending" id="stage-analyze">分析模板</span>
                        <span class="stage-indicator stage-pending" id="stage-match">匹配数据</span>
                        <span class="stage-indicator stage-pending" id="stage-fill">填充数据</span>
                        <span class="stage-indicator stage-pending" id="stage-save">保存文件</span>
                        <span class="stage-indicator stage-pending" id="stage-complete">完成</span>
                    </div>

                    <!-- 进度条 -->
                    <div class="progress mb-3">
                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">
                            <span id="progressText">0%</span>
                        </div>
                    </div>

                    <!-- 状态消息 -->
                    <div id="statusMessage" class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 准备开始处理...
                    </div>
                </div>

                <!-- 结果区域 -->
                <div id="resultSection" class="feature-card" style="display: none;">
                    <h4><i class="bi bi-check-circle text-success"></i> 处理结果</h4>
                    <div id="resultContent"></div>
                </div>

                <!-- 输出文件列表 -->
                <div class="feature-card">
                    <h4><i class="bi bi-download text-primary"></i> 输出文件</h4>
                    <button class="btn btn-secondary btn-sm mb-3" onclick="refreshFileList()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新列表
                    </button>
                    <div id="outputFilesList" class="output-files">
                        <div class="text-center text-muted">
                            <i class="bi bi-folder-x" style="font-size: 2rem;"></i>
                            <p>暂无输出文件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentTaskId = null;
        let progressInterval = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            setupDefaultPaths();
            refreshFileList();
        });

        function setupEventListeners() {
            // 路径模式切换
            document.querySelectorAll('input[name="pathMode"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'upload') {
                        document.getElementById('uploadSection').style.display = 'block';
                        document.getElementById('pathSection').style.display = 'none';
                    } else {
                        document.getElementById('uploadSection').style.display = 'none';
                        document.getElementById('pathSection').style.display = 'block';
                    }
                });
            });

            // 文件选择事件
            document.getElementById('templateFile').addEventListener('change', function() {
                showFileInfo(this, 'templateFileInfo');
            });

            document.getElementById('reportFile').addEventListener('change', function() {
                showFileInfo(this, 'reportFileInfo');
            });

            // 拖拽事件
            setupDragAndDrop();
        }

        function setupDefaultPaths() {
            // 设置默认路径
            document.getElementById('templatePath').value = 'D:\\华为家庭存储\\工作\\运营\\Listing相关\\模板\\DECORATIVE_RIBBON_TRIM_US.xlsm';
            document.getElementById('reportPath').value = 'D:\\华为家庭存储\\工作\\运营\\Listing相关\\Listing\\US\\分类商品报告+05-27-2025.xlsm';
        }

        function setupDragAndDrop() {
            const dropZones = document.querySelectorAll('.file-drop-zone');
            
            dropZones.forEach(zone => {
                zone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                zone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                zone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const fileInput = this.parentElement.querySelector('input[type="file"]');
                        fileInput.files = files;
                        fileInput.dispatchEvent(new Event('change'));
                    }
                });
            });
        }

        function showFileInfo(input, infoId) {
            const infoDiv = document.getElementById(infoId);
            if (input.files.length > 0) {
                const file = input.files[0];
                const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
                infoDiv.innerHTML = `
                    <i class="bi bi-file-check text-success"></i>
                    <strong>${file.name}</strong> (${sizeInMB} MB)
                `;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        }

        function startFilling() {
            const pathMode = document.querySelector('input[name="pathMode"]:checked').value;
            const maxRows = parseInt(document.getElementById('maxRows').value);

            if (maxRows < 1 || maxRows > 1000) {
                showAlert('error', '最大处理行数必须在1-1000之间');
                return;
            }

            const formData = new FormData();
            formData.append('max_rows', maxRows);

            if (pathMode === 'upload') {
                // 上传文件模式
                const templateFile = document.getElementById('templateFile').files[0];
                const reportFile = document.getElementById('reportFile').files[0];

                if (!templateFile || !reportFile) {
                    showAlert('error', '请选择模板文件和商品报告文件');
                    return;
                }

                formData.append('template_file', templateFile);
                formData.append('report_file', reportFile);
                formData.append('use_custom_paths', 'false');
            } else {
                // 自定义路径模式
                const templatePath = document.getElementById('templatePath').value.trim();
                const reportPath = document.getElementById('reportPath').value.trim();

                if (!templatePath || !reportPath) {
                    showAlert('error', '请输入有效的文件路径');
                    return;
                }

                formData.append('template_path', templatePath);
                formData.append('report_path', reportPath);
                formData.append('use_custom_paths', 'true');
                
                // 为路径模式创建空的文件对象（避免后端检查报错）
                const emptyFile = new File([''], '', { type: 'application/octet-stream' });
                formData.append('template_file', emptyFile);
                formData.append('report_file', emptyFile);
                
                console.log('路径模式参数:', {
                    template_path: templatePath,
                    report_path: reportPath,
                    use_custom_paths: 'true'
                });
            }

            // 显示进度区域
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('startButton').disabled = true;
            document.getElementById('startButton').innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>处理中...';

            // 重置进度
            resetProgress();

            // 发送请求
            fetch('/api/fill-template', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showAlert('info', data.message);
                    startProgressMonitoring();
                } else {
                    showAlert('error', data.error);
                    resetStartButton();
                }
            })
            .catch(error => {
                showAlert('error', '请求失败：' + error.message);
                resetStartButton();
            });
        }

        function resetProgress() {
            // 重置所有阶段
            const stages = ['init', 'read', 'analyze', 'match', 'fill', 'save', 'complete'];
            stages.forEach(stage => {
                const element = document.getElementById(`stage-${stage}`);
                element.className = 'stage-indicator stage-pending';
            });

            // 重置进度条
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = '0%';
            document.getElementById('progressText').textContent = '0%';
        }

        function startProgressMonitoring() {
            if (progressInterval) {
                clearInterval(progressInterval);
            }

            progressInterval = setInterval(() => {
                if (currentTaskId) {
                    checkProgress(currentTaskId);
                }
            }, 1000);
        }

        function checkProgress(taskId) {
            fetch(`/api/progress/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProgress(data);
                    
                    if (data.completed) {
                        clearInterval(progressInterval);
                        progressInterval = null;
                        currentTaskId = null;
                        resetStartButton();
                        
                        if (data.stage === '完成') {
                            showResult(true, data.message);
                            refreshFileList();
                        } else if (data.stage === '错误') {
                            showResult(false, data.error || data.message);
                        }
                    }
                }
            })
            .catch(error => {
                console.error('检查进度失败:', error);
            });
        }

        function updateProgress(data) {
            // 更新进度条
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = data.progress + '%';
            document.getElementById('progressText').textContent = data.progress + '%';

            // 更新状态消息
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = `<i class="bi bi-info-circle"></i> ${data.message}`;

            // 更新阶段指示器
            updateStageIndicators(data.stage);
        }

        function updateStageIndicators(currentStage) {
            const stageMap = {
                '初始化': 'init',
                '读取报告': 'read',
                '处理数据': 'read',
                '分析模板': 'analyze',
                '匹配数据': 'match',
                '填充数据': 'fill',
                '保存文件': 'save',
                '完成': 'complete',
                '错误': 'complete'
            };

            const stages = ['init', 'read', 'analyze', 'match', 'fill', 'save', 'complete'];
            const currentStageKey = stageMap[currentStage];
            
            if (currentStageKey) {
                const currentIndex = stages.indexOf(currentStageKey);
                
                stages.forEach((stage, index) => {
                    const element = document.getElementById(`stage-${stage}`);
                    if (index < currentIndex) {
                        element.className = 'stage-indicator stage-completed';
                    } else if (index === currentIndex) {
                        element.className = 'stage-indicator stage-active';
                    } else {
                        element.className = 'stage-indicator stage-pending';
                    }
                });
            }
        }

        function resetStartButton() {
            document.getElementById('startButton').disabled = false;
            document.getElementById('startButton').innerHTML = '<i class="bi bi-play-circle"></i> 开始填充模板';
        }

        function showResult(success, message) {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            
            if (success) {
                resultContent.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> ${message}
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> ${message}
                    </div>
                `;
            }
            
            resultSection.style.display = 'block';
        }

        function showAlert(type, message) {
            const alertClass = type === 'error' ? 'alert-danger' : 'alert-info';
            const icon = type === 'error' ? 'bi-exclamation-triangle' : 'bi-info-circle';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.innerHTML = `<i class="bi ${icon}"></i> ${message}`;
            
            document.querySelector('.main-container .p-4').insertBefore(alertDiv, document.querySelector('.feature-card'));
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        function refreshFileList() {
            fetch('/api/output-files')
            .then(response => response.json())
            .then(data => {
                const filesList = document.getElementById('outputFilesList');
                
                if (data.success && data.files.length > 0) {
                    let html = '';
                    data.files.forEach(file => {
                        const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
                        html += `
                            <div class="file-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><i class="bi bi-file-earmark-excel text-success"></i> ${file.name}</h6>
                                        <small class="text-muted">大小: ${sizeInMB} MB | 修改时间: ${file.modified}</small>
                                    </div>
                                    <a href="/api/download/${encodeURIComponent(file.name)}" class="btn btn-primary btn-sm">
                                        <i class="bi bi-download"></i> 下载
                                    </a>
                                </div>
                            </div>
                        `;
                    });
                    filesList.innerHTML = html;
                } else {
                    filesList.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-folder-x" style="font-size: 2rem;"></i>
                            <p>暂无输出文件</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('获取文件列表失败:', error);
            });
        }
    </script>
</body>

</html>