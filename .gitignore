# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE配置文件
.vscode/
.idea/
*.swp
*.swo
*~
.cursor/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
# 配置文件（包含敏感信息）
config/mohe_config.py

# 数据和缓存目录
data/uploads/*
data/cache/*
data/temp/*
output/temp/*
output/logs/*
output/results/*
!data/uploads/.gitkeep
!data/cache/.gitkeep
!data/temp/.gitkeep
!output/temp/.gitkeep
!output/logs/.gitkeep
!output/results/.gitkeep

# 历史文件模式（防止重新生成）
data/filled_template_*.xlsm
data/亚马逊图片模板_*.xlsx
data/映射表_*.md
data/report_test_*.log
历史映射表/
url_mapping_*.xlsm

# 测试文件
test_*.py
*_test.py
*.pytest_cache/

# 旧目录（已清理）
temp/
uploads/

# 归档文件
archive/cleanup_backup/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# Excel和办公文档的临时文件
~$*.xlsx
~$*.xlsm
~$*.xls
~$*.doc
~$*.docx
~$*.ppt
~$*.pptx

# 开发调试文件（防止重新添加）
check_files_quickly.py
debug_template_filling.py
start.py 