/**
 * 亚马逊图片上传图床工具 - Web版本
 * 前端JavaScript应用
 */

class AmazonImageUploader {
    constructor() {
        this.selectedFiles = [];
        this.currentTaskId = null;
        this.progressInterval = null;
        this.apiBaseUrl = '';

        this.init();
    }

    init() {
        // 检查Bootstrap是否正确加载
        if (typeof bootstrap === 'undefined') {
            console.error('❌ Bootstrap JavaScript未加载！');
            console.log('💡 请检查Bootstrap文件是否正确引用');
        } else {
            console.log('✅ Bootstrap JavaScript已加载');
        }
        
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // 文件上传相关事件
        const fileDropZone = document.getElementById('fileDropZone');
        const fileInput = document.getElementById('fileInput');  // 文件夹选择
        const filesInput = document.getElementById('filesInput'); // 单个文件选择
        const selectFolderBtn = document.getElementById('selectFolderBtn');
        const selectFilesBtn = document.getElementById('selectFilesBtn');
        const confirmFolderBtn = document.getElementById('confirmFolderBtn');
        const startUploadBtn = document.getElementById('startUploadBtn');
        const uploadFolderInput = document.getElementById('uploadFolder');

        if (fileDropZone) {
            fileDropZone.addEventListener('dragover', this.handleDragOver.bind(this));
            fileDropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            fileDropZone.addEventListener('drop', this.handleDrop.bind(this));
        }

        if (selectFolderBtn) {
            selectFolderBtn.addEventListener('click', () => fileInput.click());
        }

        if (selectFilesBtn) {
            selectFilesBtn.addEventListener('click', () => filesInput.click());
        }

        if (fileInput) {
            fileInput.addEventListener('change', this.handleFolderSelect.bind(this));
        }

        if (filesInput) {
            filesInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        if (uploadFolderInput) {
            uploadFolderInput.addEventListener('input', this.validateUploadFolder.bind(this));
        }

        if (confirmFolderBtn) {
            confirmFolderBtn.addEventListener('click', this.confirmUploadFolder.bind(this));
        }

        if (startUploadBtn) {
            startUploadBtn.addEventListener('click', this.startUpload.bind(this));
        }

        // URL映射相关事件
        const parseUrlBtn = document.getElementById('parseUrlBtn');
        const clearUrlBtn = document.getElementById('clearUrlBtn');
        const generateUrlExcelBtn = document.getElementById('generateUrlExcelBtn');

        if (parseUrlBtn) {
            parseUrlBtn.addEventListener('click', this.parseUrls.bind(this));
        }

        if (generateUrlExcelBtn) {
            generateUrlExcelBtn.addEventListener('click', this.generateUrlExcel.bind(this));
        }

        if (clearUrlBtn) {
            clearUrlBtn.addEventListener('click', this.clearUrls.bind(this));
        }

        // 历史记录相关事件
        const refreshHistoryBtn = document.getElementById('refreshHistory');
        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', this.loadHistoryFiles.bind(this));
        }

        // 系统测试相关事件
        const testApiBtn = document.getElementById('testApiBtn');
        if (testApiBtn) {
            testApiBtn.addEventListener('click', this.testApiConnection.bind(this));
        }

        // 模板填充相关事件
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');
        const clearTemplateBtn = document.getElementById('clearTemplateBtn');
        const templateFile = document.getElementById('templateFile');
        const reportFile = document.getElementById('reportFile');
        const mappingFile = document.getElementById('mappingFile');
        const productInfoFile = document.getElementById('productInfoFile');

        if (fillTemplateBtn) {
            fillTemplateBtn.addEventListener('click', this.handleFillTemplate.bind(this));
        }

        if (clearTemplateBtn) {
            clearTemplateBtn.addEventListener('click', this.clearTemplateFiles.bind(this));
        }

        // 文件选择监听，用于启用/禁用填充按钮
        [templateFile, reportFile, mappingFile].forEach(fileInput => {
            if (fileInput) {
                fileInput.addEventListener('change', this.validateTemplateFiles.bind(this));
            }
        });

        // Tab切换事件
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        console.log('[初始化调试] 找到Tab按钮数量:', tabButtons.length);
        tabButtons.forEach((button, index) => {
            console.log(`[初始化调试] Tab按钮 ${index + 1}:`, button.getAttribute('data-bs-target'));
            // 使用Bootstrap 5的正确事件名称
            button.addEventListener('shown.bs.tab', this.handleTabChange.bind(this));
            button.addEventListener('click', (e) => {
                console.log('[Tab调试] Tab按钮点击:', e.target.getAttribute('data-bs-target'));
            });
        });
    }

    loadInitialData() {
        // 加载历史文件列表
        this.loadHistoryFiles();
    }

    // 文件拖拽处理
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files);
        this.processSelectedFiles(files, '拖拽的文件');
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processSelectedFiles(files, '选择的文件');
    }

    handleFolderSelect(e) {
        const files = Array.from(e.target.files);
        this.processSelectedFiles(files, '文件夹中的图片');
    }

    processSelectedFiles(files, source = '选择的文件') {

        // 筛选有效的图片文件
        const validFiles = [];
        const invalidFiles = [];

        files.forEach(file => {
            const isImage = file.type.startsWith('image/') ||
                file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);

            if (!isImage) {
                invalidFiles.push({ file, reason: '不是图片格式' });
                return;
            }

            // 检查单个文件大小（不超过20MB）
            if (file.size > 20 * 1024 * 1024) {
                invalidFiles.push({ file, reason: '单个文件不能超过20MB，请先压缩图片' });
                return;
            }

            const validationResult = this.validateFilename(file.name);
            if (!validationResult.valid) {
                console.log(`[前端调试] 文件名验证失败: ${file.name} - ${validationResult.reason}`);
                invalidFiles.push({ file, reason: validationResult.reason });
                return;
            } else {
                console.log(`[前端调试] 文件名验证通过: ${file.name}`);
            }

            validFiles.push(file);
        });

        // 计算总文件大小（仅用于显示，不限制）
        const totalSize = validFiles.reduce((total, file) => total + file.size, 0);
        console.log(`[前端调试] 验证结果 - 有效文件: ${validFiles.length}, 无效文件: ${invalidFiles.length}`);
        console.log(`[前端调试] 总文件大小: ${this.formatFileSize(totalSize)}`);

        // 移除总文件大小限制，图床支持任意大小的文件夹上传
        // 但仍保留单个文件20MB的限制以确保上传稳定性

        // 显示处理结果
        if (invalidFiles.length > 0) {
            console.log(`[前端调试] 发现 ${invalidFiles.length} 个无效文件`);
            let message = `${source}中有 ${invalidFiles.length} 个文件不符合要求：\n`;
            invalidFiles.slice(0, 5).forEach(item => {
                message += `• ${item.file.name}: ${item.reason}\n`;
            });
            if (invalidFiles.length > 5) {
                message += `... 等共 ${invalidFiles.length} 个文件\n`;
            }
            message += '\n文件名要求：ASIN_类型.扩展名 (如: B07XXXXX_MAIN.jpg)';

            if (validFiles.length > 0) {
                message += `\n\n是否继续处理 ${validFiles.length} 个有效文件？`;
                console.log(`[前端调试] ⚠️ 即将弹出确认对话框：是否继续处理有效文件`);
                const userChoice = confirm(message);
                console.log(`[前端调试] 用户选择：${userChoice ? '是' : '否'}`);
                if (!userChoice) {
                    console.log(`[前端调试] ❌ 用户选择'否'，不继续处理，函数返回`);
                    alert('已取消文件选择。请重新选择符合要求的文件。');
                    return;
                }
                console.log(`[前端调试] ✅ 用户选择'是'，继续处理 ${validFiles.length} 个有效文件`);
            } else {
                console.log(`[前端调试] ❌ 没有有效文件，显示错误提示`);
                alert(message);
                return;
            }
        }

        this.selectedFiles = validFiles;
        console.log(`[前端调试] 设置选中文件列表，共 ${validFiles.length} 个文件`);

        if (validFiles.length > 0) {
            console.log(`[前端调试] ✅ 文件选择成功，准备显示成功提示`);
            this.showNotification('success', `成功选择 ${validFiles.length} 个有效图片文件`);

            // 如果文件数量较少，给出提示
            if (validFiles.length <= 350) {
                this.showNotification('info', `图片数量≤350，建议直接到图床上传以获得更好的体验`);
            }
        }

        console.log(`[前端调试] 调用 displaySelectedFiles() 显示文件列表`);
        this.displaySelectedFiles();
        console.log(`[前端调试] 调用 showUploadSettings() 显示上传设置`);
        this.showUploadSettings();
    }

    showUploadSettings() {
        if (this.selectedFiles.length > 0) {
            document.getElementById('uploadSettings').style.display = 'block';
            document.getElementById('uploadFolder').focus();

            // 显示文件选择完成的提示
            this.showNotification('info', `已选择 ${this.selectedFiles.length} 个文件，请在下方设置上传目录`);
        } else {
            document.getElementById('uploadSettings').style.display = 'none';
            document.getElementById('taskListSection').style.display = 'none';
        }
    }

    validateUploadFolder() {
        const uploadFolder = document.getElementById('uploadFolder').value.trim();
        const confirmBtn = document.getElementById('confirmFolderBtn');

        // 验证目录名：不能为空，不能包含 / 或 \
        const isValid = uploadFolder && !uploadFolder.includes('/') && !uploadFolder.includes('\\');

        if (confirmBtn) {
            confirmBtn.disabled = !isValid;
        }

        return isValid;
    }

    confirmUploadFolder() {
        if (!this.validateUploadFolder()) {
            alert('目录名不能为空，且不能包含 / 或 \\ 字符！');
            return;
        }

        const uploadFolder = document.getElementById('uploadFolder').value.trim();

        // 确认目录名 - 提供更详细的信息
        const confirmMessage = `确认设置：\n\n` +
            `• 上传目录：${uploadFolder}\n` +
            `• 图片数量：${this.selectedFiles.length} 个\n` +
            `• 文件总大小：${this.getTotalFileSize()}\n\n` +
            `点击"确定"进入下一步（选择任务列表文件）`;

        if (confirm(confirmMessage)) {
            document.getElementById('uploadSettings').style.display = 'none';
            document.getElementById('taskListSection').style.display = 'block';
            this.showNotification('success', `✅ 上传目录已设置：${uploadFolder}，现在可以选择任务列表文件（可选）或直接开始上传`);

            // 滚动到任务列表区域
            document.getElementById('taskListSection').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    validateFilename(filename) {
        // 亚马逊图片文件名格式验证 - 完全与桌面版本保持一致
        const upperFilename = filename.toUpperCase();

        // 1. 必须以B0开头（Amazon ASIN格式）
        if (!upperFilename.startsWith('B0')) {
            return { valid: false, reason: "文件名必须以B0开头（Amazon ASIN格式）" };
        }

        // 2. 必须包含MAIN、PT或SWATCH关键词
        const hasMain = upperFilename.includes('MAIN');
        const hasSwatch = upperFilename.includes('SWATCH');
        // 修改PT验证逻辑，使其与桌面版本完全一致
        const hasPT = upperFilename.includes('PT');

        if (!(hasMain || hasSwatch || hasPT)) {
            return { valid: false, reason: "文件名必须包含MAIN、PT或SWATCH关键词以标识图片类型" };
        }

        // 3. 必须是支持的图片格式
        const supportedExtensions = ['.PNG', '.JPG', '.JPEG', '.GIF', '.WEBP', '.BMP'];
        const hasValidExtension = supportedExtensions.some(ext => upperFilename.endsWith(ext));

        if (!hasValidExtension) {
            return { valid: false, reason: "不支持的图片格式" };
        }

        return { valid: true, reason: "文件名格式正确" };
    }

    displaySelectedFiles() {
        const selectedFilesDiv = document.getElementById('selectedFiles');
        const fileListDiv = document.getElementById('fileList');

        if (this.selectedFiles.length === 0) {
            selectedFilesDiv.style.display = 'none';
            return;
        }

        selectedFilesDiv.style.display = 'block';
        fileListDiv.innerHTML = '';

        // 添加统计信息
        const statsDiv = document.createElement('div');
        statsDiv.className = 'alert alert-info mb-3';
        statsDiv.innerHTML = `
            <strong><i class="bi bi-info-circle"></i> 已选择 ${this.selectedFiles.length} 个图片文件</strong>
            <small class="d-block mt-1">总大小: ${this.getTotalFileSize()}</small>
        `;
        fileListDiv.appendChild(statsDiv);

        this.selectedFiles.forEach((file, index) => {
            const fileItem = this.createFileListItem(file, index);
            fileListDiv.appendChild(fileItem);
        });
    }

    createFileListItem(file, index) {
        const fileItem = document.createElement('div');
        fileItem.className = 'list-group-item d-flex justify-content-between align-items-center';

        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <strong>${file.name}</strong>
            <small class="text-muted d-block">${this.formatFileSize(file.size)}</small>
        `;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.onclick = () => this.removeFile(index);

        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeBtn);

        return fileItem;
    }

    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.displaySelectedFiles();
        this.showUploadSettings();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getTotalFileSize() {
        const totalBytes = this.selectedFiles.reduce((total, file) => total + file.size, 0);
        return this.formatFileSize(totalBytes);
    }

    // 上传处理
    async startUpload() {
        if (this.selectedFiles.length === 0) {
            this.showNotification('warning', '请先选择要上传的文件');
            return;
        }

        // 上传前确认 - 与桌面版本保持一致
        const uploadFolder = document.getElementById('uploadFolder').value || 'DEFAULT';
        const taskListFile = document.getElementById('taskListFile').files[0];

        let confirmMessage = `上传前请确认图片命名规则：\n\n`;
        confirmMessage += `1. 文件名以B0开头（Amazon ASIN格式）\n`;
        confirmMessage += `2. 主图请包含MAIN关键词\n`;
        confirmMessage += `3. 附图请包含PT01-PT08关键词\n`;
        confirmMessage += `4. 色卡图请包含SWATCH关键词\n\n`;
        confirmMessage += `即将上传 ${this.selectedFiles.length} 个图片文件到目录: ${uploadFolder}\n\n`;
        confirmMessage += `使用高性能设置：\n`;
        confirmMessage += `- 智能并发线程数: 8-12个（根据文件数量动态调整）\n`;
        confirmMessage += `- 超快上传延迟: 0.05秒\n`;
        confirmMessage += `- 保持原始图片质量\n`;
        confirmMessage += `- 保留原始文件名（UPLOAD_MODE=2）\n`;
        if (taskListFile) {
            confirmMessage += `- 任务列表文件: ${taskListFile.name}\n`;
        }
        confirmMessage += `\n是否继续？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        this.showLoading(true);

        const formData = new FormData();
        this.selectedFiles.forEach(file => {
            formData.append('files', file);
        });

        formData.append('upload_folder', uploadFolder);

        // 添加图片任务列表文件（如果选择了）
        if (taskListFile) {
            formData.append('task_list_file', taskListFile);
        }

        try {
            const response = await fetch('/api/upload-images', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentTaskId = result.task_id;
                this.showProgressCard();
                this.startProgressTracking();
                this.showNotification('success', '上传任务已开始');
            } else {
                this.showNotification('error', '上传失败: ' + result.error);
            }
        } catch (error) {
            this.showNotification('error', '上传请求失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    showProgressCard() {
        const progressCard = document.getElementById('progressCard');
        const resultCard = document.getElementById('resultCard');

        if (progressCard) progressCard.style.display = 'block';
        if (resultCard) resultCard.style.display = 'none';

        // 初始化进度显示
        this.updateProgressDisplay({
            total: this.selectedFiles.length,
            completed: 0,
            failed: 0
        });
    }

    startProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/upload-progress/${this.currentTaskId}`);
                const result = await response.json();

                if (result.success) {
                    this.updateProgressDisplay(result.progress);

                    const status = result.progress.status;
                    if (status === 'completed' || status === 'completed_with_errors') {
                        clearInterval(this.progressInterval);
                        this.showUploadResults(result.progress);
                    } else if (status === 'failed') {
                        clearInterval(this.progressInterval);
                        this.showNotification('error', '上传任务失败: ' + (result.progress.error || '未知错误'));
                    }
                }
            } catch (error) {
                console.error('获取进度失败:', error);
            }
        }, 2000); // 改为2秒查询一次，减少服务器压力
    }

    updateProgressDisplay(progress) {
        const completed = progress.completed + progress.failed;
        const total = progress.total;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

        // 更新进度条
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        // 更新进度文本
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = `处理中... ${completed}/${total}`;
        }

        // 更新计数器
        const successCount = document.getElementById('successCount');
        const failCount = document.getElementById('failCount');
        const totalCount = document.getElementById('totalCount');

        if (successCount) successCount.textContent = progress.completed;
        if (failCount) failCount.textContent = progress.failed;
        if (totalCount) totalCount.textContent = progress.total;
    }

    showUploadResults(progress) {
        const progressCard = document.getElementById('progressCard');
        const resultCard = document.getElementById('resultCard');

        if (progressCard) progressCard.style.display = 'none';
        if (resultCard) resultCard.style.display = 'block';

        const resultsDiv = document.getElementById('uploadResults');
        if (!resultsDiv) return;

        resultsDiv.innerHTML = '';

        // 显示总结
        const summary = this.createResultSummary(progress);
        resultsDiv.appendChild(summary);

        // 显示详细结果
        if (progress.results && progress.results.length > 0) {
            progress.results.forEach(result => {
                const resultItem = this.createResultItem(result);
                resultsDiv.appendChild(resultItem);
            });
        }

        // 处理下载按钮
        this.handleDownloadButton(progress);

        // 清空文件选择
        this.clearFileSelection();

        // 刷新历史记录
        this.loadHistoryFiles();
    }

    createResultSummary(progress) {
        const summary = document.createElement('div');
        summary.className = 'alert alert-info';
        summary.innerHTML = `
            <h6><i class="bi bi-info-circle"></i> 上传完成</h6>
            <p class="mb-0">成功: ${progress.completed} | 失败: ${progress.failed} | 总计: ${progress.total}</p>
        `;
        return summary;
    }

    createResultItem(result) {
        const resultItem = document.createElement('div');
        resultItem.className = `result-item ${result.success ? '' : 'error'}`;

        const statusBadge = result.success ?
            '<span class="status-badge status-success">上传成功</span>' :
            '<span class="status-badge status-error">上传失败</span>';

        const detail = result.success ?
            `<small class="text-muted d-block mt-1">URL: <a href="${result.url}" target="_blank">${result.url}</a></small>` :
            `<small class="text-danger d-block mt-1">错误: ${result.error}</small>`;

        resultItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${result.filename}</strong>
                    <div class="mt-1">${statusBadge}</div>
                    ${detail}
                </div>
            </div>
        `;

        return resultItem;
    }

    handleDownloadButton(progress) {
        const downloadSection = document.getElementById('downloadSection');
        const downloadBtn = document.getElementById('downloadExcel');

        if (progress.excel_file && downloadSection && downloadBtn) {
            downloadSection.style.display = 'block';
            downloadBtn.onclick = () => {
                window.open(`/api/download-excel/${this.currentTaskId}`, '_blank');
            };
        }
    }

    clearFileSelection() {
        this.selectedFiles = [];
        const fileInput = document.getElementById('fileInput');
        const filesInput = document.getElementById('filesInput');
        if (fileInput) fileInput.value = '';
        if (filesInput) filesInput.value = '';
        this.displaySelectedFiles();
        this.showUploadSettings();

        // 重置所有设置界面
        document.getElementById('uploadSettings').style.display = 'none';
        document.getElementById('taskListSection').style.display = 'none';
        document.getElementById('uploadFolder').value = '';
        document.getElementById('taskListFile').value = '';
    }

    // URL映射处理 - 第一步：解析URL数据
    async parseUrls() {
        const urlInput = document.getElementById('urlInput');
        if (!urlInput) return;

        const urlText = urlInput.value.trim();
        if (!urlText) {
            this.showNotification('warning', '请输入URL列表');
            return;
        }

        this.showLoading(true);

        try {
            const lines = urlText.split('\n').filter(line => line.trim());
            if (lines.length === 0) {
                this.showLoading(false);
                this.showNotification('warning', '未检测到有效URL');
                return;
            }

            const urls = [];
            const invalidUrls = [];

            // 在客户端解析URL，提取ASIN和图片类型
            for (const line of lines) {
                try {
                    // 检查是否包含|分隔符
                    const parts = line.split('|');
                    if (parts.length === 2) {
                        // 使用filename|url格式
                        urls.push({
                            filename: parts[0].trim(),
                            url: parts[1].trim()
                        });
                        console.log(`添加 filename|url 格式: ${parts[0].trim()} | ${parts[1].trim()}`);
                    } else if (line.trim().startsWith('http')) {
                        // 直接使用URL，智能提取ASIN和图片类型
                        const url = line.trim();
                        let asin = null;
                        let imageType = "MAIN";

                        // 尝试从URL路径中提取ASIN
                        const urlParts = url.split('/');
                        const lastPart = urlParts[urlParts.length - 1];

                        // 检查最后部分是否包含B0开头的ASIN
                        const asinMatch = lastPart.match(/B0[A-Z0-9]{8}/);
                        if (asinMatch) {
                            asin = asinMatch[0];
                            console.log(`从URL提取到ASIN: ${asin}`);

                            // 提取图片类型 - 支持多种格式
                            if (lastPart.includes('_PT')) {
                                // 支持 _PT1, _PT01 等格式
                                const ptMatch = lastPart.match(/_PT(\d+)/);
                                if (ptMatch) {
                                    const ptNum = parseInt(ptMatch[1]);
                                    imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                    console.log(`从URL提取到图片类型: ${imageType}`);
                                }
                            } else if (lastPart.includes('.PT')) {
                                // 支持 .PT1, .PT01 等格式（如 B0XXXXXXXX.PT01.jpg）
                                const ptMatch = lastPart.match(/\.PT(\d+)/);
                                if (ptMatch) {
                                    const ptNum = parseInt(ptMatch[1]);
                                    imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                    console.log(`从URL提取到图片类型: ${imageType}`);
                                }
                            } else if (lastPart.includes('_MAIN') || lastPart.includes('.MAIN')) {
                                imageType = 'MAIN';
                            } else if (lastPart.includes('_SWATCH') || lastPart.includes('.SWATCH')) {
                                imageType = 'SWATCH';
                            } else {
                                // 如果没有明确的类型标识，尝试从文件名末尾推断
                                if (lastPart.match(/\.M\d*/)) {
                                    // 以.M开头的可能是MAIN图
                                    imageType = 'MAIN';
                                } else if (lastPart.match(/\.P\d+/)) {
                                    // 以.P开头的可能是PT图，尝试提取数字
                                    const pMatch = lastPart.match(/\.P(\d+)/);
                                    if (pMatch && pMatch[1]) {
                                        const ptNum = parseInt(pMatch[1]);
                                        if (ptNum >= 1 && ptNum <= 8) {
                                            imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                        } else {
                                            imageType = 'PT01'; // 默认为PT01
                                        }
                                    } else {
                                        imageType = 'PT01';
                                    }
                                    console.log(`从文件名格式推断图片类型: ${imageType}`);
                                } else if (lastPart.match(/\.S\d*/)) {
                                    // 以.S开头的可能是SWATCH图
                                    imageType = 'SWATCH';
                                } else {
                                    // 默认为MAIN
                                    imageType = 'MAIN';
                                }
                            }
                        } else {
                            // 从URL其他部分尝试提取ASIN
                            for (const part of urlParts) {
                                if (part.startsWith('B0') && part.length >= 10) {
                                    asin = part.substring(0, 10);
                                    console.log(`从URL路径部分提取到ASIN: ${asin}`);
                                    break;
                                }
                            }

                            // 如果仍然没有找到ASIN，从URL本身再次尝试
                            if (!asin) {
                                const fullUrlMatch = url.match(/B0[A-Z0-9]{8}/);
                                if (fullUrlMatch) {
                                    asin = fullUrlMatch[0];
                                    console.log(`从完整URL提取到ASIN: ${asin}`);
                                }
                            }

                            // 最后实在找不到，生成一个基于URL的唯一标识
                            if (!asin) {
                                const urlHash = this.generateUrlHash(url);
                                asin = `B0${urlHash}`;
                                console.log(`为URL生成哈希作为ASIN: ${asin}`);
                            }
                        }

                        // 构造规范的文件名
                        const fileName = `${asin}_${imageType}.jpg`;

                        urls.push({
                            filename: fileName,
                            url: url
                        });
                        console.log(`添加URL格式: ${url} -> ${fileName}`);
                    } else {
                        // 无效URL
                        invalidUrls.push(line);
                        console.log(`无效URL格式: ${line}`);
                    }
                } catch (lineError) {
                    console.error(`处理URL行时出错: ${line}`, lineError);
                    invalidUrls.push(line);
                }
            }

            console.log(`处理结果: ${urls.length}个有效, ${invalidUrls.length}个无效`);

            if (urls.length === 0) {
                this.showLoading(false);
                this.showNotification('warning', '没有有效的URL数据，请检查格式');
                return;
            }

            // 统计ASIN数量
            const asinSet = new Set();
            urls.forEach(item => {
                if (item.filename.includes('_')) {
                    const asin = item.filename.split('_')[0];
                    asinSet.add(asin);
                }
            });

            // 保存解析结果，供第二步使用
            this.parsedUrls = urls;
            
            // 显示解析结果
            this.showUrlParseResult(urls.length, asinSet.size, invalidUrls.length);
            
            this.showNotification('success', `URL解析完成！共解析 ${urls.length} 个URL，涉及 ${asinSet.size} 个ASIN`);

            // 如果有无效URL，显示警告
            if (invalidUrls.length > 0) {
                this.showNotification('warning', `有${invalidUrls.length}个URL格式无效，已跳过`);
            }

        } catch (error) {
            console.error('URL解析错误:', error);
            this.showNotification('error', `解析出错: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    // 显示URL解析结果
    showUrlParseResult(urlCount, asinCount, invalidCount) {
        const urlParseSteps = document.getElementById('urlParseSteps');
        const parsedUrlCount = document.getElementById('parsedUrlCount');
        const parsedAsinCount = document.getElementById('parsedAsinCount');

        if (parsedUrlCount) parsedUrlCount.textContent = urlCount;
        if (parsedAsinCount) parsedAsinCount.textContent = asinCount;
        if (urlParseSteps) urlParseSteps.style.display = 'block';
    }

    // URL映射处理 - 第二步：生成Excel文件
    async generateUrlExcel() {
        if (!this.parsedUrls || this.parsedUrls.length === 0) {
            this.showNotification('error', '没有解析的URL数据，请先执行第一步');
            return;
        }

        this.showLoading(true);

        try {
            // 准备发送到服务器的数据
            const formData = new FormData();
            formData.append('urls', JSON.stringify(this.parsedUrls));

            // 添加任务列表文件（如果选择了）
            const taskListFile = document.getElementById('urlTaskListFile').files[0];
            if (taskListFile) {
                formData.append('task_list_file', taskListFile);
                console.log(`使用任务列表文件: ${taskListFile.name}`);
            }

            // 发送请求到服务器生成Excel
            const response = await fetch('/api/generate-url-excel', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('服务器响应:', result);

            if (result.success) {
                this.showUrlResults(result);
                this.showNotification('success', 'Excel映射表生成成功');
                
                // 隐藏解析步骤，显示结果
                const urlParseSteps = document.getElementById('urlParseSteps');
                if (urlParseSteps) urlParseSteps.style.display = 'none';
            } else {
                throw new Error(result.error || '服务器处理失败');
            }

        } catch (error) {
            console.error('生成Excel失败:', error);
            this.showNotification('error', `生成Excel失败: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    showUrlResults(result) {
        const urlResultCard = document.getElementById('urlResultCard');
        const urlAsinCount = document.getElementById('urlAsinCount');
        const urlTotalCount = document.getElementById('urlTotalCount');
        const downloadUrlExcel = document.getElementById('downloadUrlExcel');

        if (urlAsinCount) urlAsinCount.textContent = result.total_asins;
        if (urlTotalCount) urlTotalCount.textContent = result.total_urls;
        if (urlResultCard) urlResultCard.style.display = 'block';

        if (downloadUrlExcel) {
            downloadUrlExcel.onclick = () => {
                window.open(`/api/download-history/${result.excel_file}`, '_blank');
            };
        }

        // 刷新历史记录
        this.loadHistoryFiles();
    }

    clearUrls() {
        const urlInput = document.getElementById('urlInput');
        const urlParseSteps = document.getElementById('urlParseSteps');
        const urlResultCard = document.getElementById('urlResultCard');
        const urlTaskListFile = document.getElementById('urlTaskListFile');

        if (urlInput) urlInput.value = '';
        if (urlParseSteps) urlParseSteps.style.display = 'none';
        if (urlResultCard) urlResultCard.style.display = 'none';
        if (urlTaskListFile) urlTaskListFile.value = '';
        
        // 清理解析结果数据
        this.parsedUrls = null;
    }

    // 历史记录处理
    async loadHistoryFiles() {
        try {
            const response = await fetch('/api/history-files');
            const result = await response.json();

            const tbody = document.getElementById('historyTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            if (result.success && result.files.length > 0) {
                result.files.forEach(file => {
                    const row = this.createHistoryTableRow(file);
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无历史文件</td></tr>';
            }
        } catch (error) {
            console.error('加载历史文件失败:', error);
            const tbody = document.getElementById('historyTableBody');
            if (tbody) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">加载失败</td></tr>';
            }
        }
    }

    createHistoryTableRow(file) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${file.filename}</td>
            <td>${this.formatFileSize(file.size)}</td>
            <td>${file.modified}</td>
            <td>
                <a href="/api/download-history/${file.filename}" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        `;
        return row;
    }

    // 系统测试处理
    async testApiConnection() {
        this.showLoading(true);

        try {
            const response = await fetch('/api/test-connection');
            const result = await response.json();

            this.showTestResults(result);
        } catch (error) {
            this.showTestResults({
                success: false,
                error: error.message
            });
        } finally {
            this.showLoading(false);
        }
    }

    showTestResults(result) {
        const testResultCard = document.getElementById('testResultCard');
        const testResults = document.getElementById('testResults');

        if (!testResultCard || !testResults) return;

        testResultCard.style.display = 'block';

        if (result.success) {
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> 连接成功</h6>
                    <pre class="mb-0 small">${JSON.stringify(result.result, null, 2)}</pre>
                </div>
            `;
            this.showNotification('success', 'API连接测试成功');
        } else {
            testResults.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-x-circle"></i> 连接失败</h6>
                    <p class="mb-0">${result.error}</p>
                </div>
            `;
            this.showNotification('error', 'API连接测试失败');
        }
    }

    // Tab切换处理
    handleTabChange(event) {
        const targetTab = event.target.getAttribute('data-bs-target');
        console.log('[Tab调试] 切换到Tab:', targetTab);

        // 根据不同Tab执行相应的初始化操作
        switch (targetTab) {
            case '#history-tab-pane':
                console.log('[Tab调试] 初始化历史记录面板');
                this.loadHistoryFiles();
                break;
            case '#upload-tab-pane':
                console.log('[Tab调试] 初始化上传面板');
                // 可以在这里初始化上传相关功能
                break;
            case '#url-tab-pane':
                console.log('[Tab调试] 初始化URL解析面板');
                // URL解析面板初始化
                break;
            case '#template-tab-pane':
                console.log('[Tab调试] 初始化模板填充面板');
                // 模板填充面板初始化
                break;
            case '#rename-tab-pane':
                console.log('[Tab调试] 初始化图片重命名面板');
                // 图片重命名面板初始化
                break;
            default:
                console.log('[Tab调试] 未知Tab:', targetTab);
        }
    }

    // 手动Tab切换的备用方案
    manualTabSwitch(targetTabId) {
        console.log('[手动Tab] 切换到:', targetTabId);
        
        // 隐藏所有Tab内容
        const allTabPanes = document.querySelectorAll('.tab-pane');
        allTabPanes.forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // 移除所有Tab按钮的active状态
        const allTabButtons = document.querySelectorAll('.nav-link');
        allTabButtons.forEach(button => {
            button.classList.remove('active');
            button.setAttribute('aria-selected', 'false');
        });
        
        // 显示目标Tab内容
        const targetPane = document.querySelector(targetTabId);
        if (targetPane) {
            targetPane.classList.add('show', 'active');
        }
        
        // 激活对应的Tab按钮
        const targetButton = document.querySelector(`[data-bs-target="${targetTabId}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
            targetButton.setAttribute('aria-selected', 'true');
        }
        
        // 触发Tab切换事件
        this.handleTabChange({ target: { getAttribute: () => targetTabId } });
    }

    // 工具函数
    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }

    showNotification(type, message) {
        // 移除现有的通知（避免重叠）
        const existingNotifications = document.querySelectorAll('.custom-notification');
        existingNotifications.forEach(notification => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });

        // 简单的通知实现，可以替换为更复杂的通知组件
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const icon = {
            'success': 'bi-check-circle',
            'error': 'bi-x-circle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        }[type] || 'bi-info-circle';

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed custom-notification`;
        notification.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 99999 !important;
            min-width: 300px !important;
            max-width: 400px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            border: 1px solid rgba(0,0,0,0.1) !important;
            margin: 0 !important;
            padding: 12px 16px !important;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.innerHTML = `
            <i class="bi ${icon}"></i> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        // 确保添加到body最后
        document.body.appendChild(notification);

        // 添加显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动删除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);

        // 调试信息
        console.log(`[通知调试] 显示${type}通知: ${message}`);
    }

    // 生成URL的哈希值
    generateUrlHash(url) {
        // 简单的哈希函数，生成8位字符
        let hash = 0;
        for (let i = 0; i < url.length; i++) {
            const char = url.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        // 转换为正数并格式化为8位十六进制
        const positiveHash = Math.abs(hash).toString(16).padStart(8, '0').toUpperCase();
        return positiveHash.substring(0, 8);
    }

    // 模板填充相关方法
    validateTemplateFiles() {
        const templateFile = document.getElementById('templateFile');
        const reportFile = document.getElementById('reportFile');
        const mappingFile = document.getElementById('mappingFile');
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');

        // 检查必需文件是否都已选择
        const hasTemplateFile = templateFile && templateFile.files.length > 0;
        const hasReportFile = reportFile && reportFile.files.length > 0;
        const hasMappingFile = mappingFile && mappingFile.files.length > 0;

        // 只有三个必需文件都选择了才启用按钮
        if (fillTemplateBtn) {
            fillTemplateBtn.disabled = !(hasTemplateFile && hasReportFile && hasMappingFile);
        }

        console.log('[模板填充] 文件验证:', {
            template: hasTemplateFile,
            report: hasReportFile,
            mapping: hasMappingFile,
            buttonEnabled: !fillTemplateBtn?.disabled
        });
    }

    clearTemplateFiles() {
        // 清空所有文件选择
        const fileInputs = ['templateFile', 'reportFile', 'mappingFile', 'productInfoFile'];
        fileInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = '';
            }
        });

        // 禁用填充按钮
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');
        if (fillTemplateBtn) {
            fillTemplateBtn.disabled = true;
        }

        // 隐藏结果卡片
        const templateResultCard = document.getElementById('templateResultCard');
        if (templateResultCard) {
            templateResultCard.style.display = 'none';
        }

        console.log('[模板填充] 已清空所有文件选择');
        this.showNotification('info', '已清空所有文件选择');
    }

    async handleFillTemplate() {
        try {
            console.log('[模板填充] 开始处理');
            
            // 安全获取文件输入元素
            const templateFileElement = document.getElementById('templateFile');
            const reportFileElement = document.getElementById('reportFile');
            const mappingFileElement = document.getElementById('mappingFile');
            const productInfoFileElement = document.getElementById('productInfoFile');

            // 检查元素是否存在
            if (!templateFileElement || !reportFileElement || !mappingFileElement) {
                console.error('[模板填充] 找不到文件输入元素');
                this.showNotification('error', '页面元素加载异常，请刷新页面重试');
                return;
            }

            // 获取文件
            const templateFile = templateFileElement.files[0];
            const reportFile = reportFileElement.files[0];
            const mappingFile = mappingFileElement.files[0];
            const productInfoFile = productInfoFileElement ? productInfoFileElement.files[0] : null;

            // 验证必需文件
            if (!templateFile || !reportFile || !mappingFile) {
                this.showNotification('error', '请选择所有必需文件（亚马逊模板、商品报告、图片映射）');
                return;
            }

            console.log('[模板填充] 文件验证通过:', {
                template: templateFile.name,
                report: reportFile.name,
                mapping: mappingFile.name,
                productInfo: productInfoFile ? productInfoFile.name : '未选择'
            });

            // 获取设置选项
            const marketSelect = document.getElementById('marketSelect');
            const useProductInfo = document.getElementById('useProductInfo');

            // 创建FormData
            const formData = new FormData();
            formData.append('template_file', templateFile);
            formData.append('report_file', reportFile);
            formData.append('mapping_file', mappingFile);
            if (productInfoFile) {
                formData.append('product_info_file', productInfoFile);
            }

            // 添加设置参数
            formData.append('market', marketSelect ? marketSelect.value : 'US');
            formData.append('use_product_info', useProductInfo ? useProductInfo.checked : true);

            // 显示加载状态
            this.showLoading(true);
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');
            if (fillTemplateBtn) {
                fillTemplateBtn.innerHTML = '<i class="bi bi-gear-fill spinning"></i> 处理中...';
                fillTemplateBtn.disabled = true;
            }

            console.log('[模板填充] 发送请求到服务器...');

            // 发送请求
            const response = await fetch('/api/fill-template', {
                method: 'POST',
                body: formData
            });

            console.log('[模板填充] 服务器响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('[模板填充] 服务器响应数据:', result);

            if (result.success) {
                this.showTemplateResults(result);
                this.showNotification('success', `模板填充完成！${result.message || ''}`);
            } else {
                this.showNotification('error', `模板填充失败: ${result.error}`);
            }

        } catch (error) {
            console.error('[模板填充] 处理失败:', error);
            this.showNotification('error', `处理失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            this.showLoading(false);
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');
            if (fillTemplateBtn) {
                fillTemplateBtn.innerHTML = '<i class="bi bi-gear"></i> 开始填充模板';
                fillTemplateBtn.disabled = false;
            }
        }
    }

    showTemplateResults(result) {
        const templateResultCard = document.getElementById('templateResultCard');
        const templateResults = document.getElementById('templateResults');
        const templateDownloadSection = document.getElementById('templateDownloadSection');

        if (!templateResultCard || !templateResults) {
            console.error('[模板填充] 找不到结果显示元素');
            return;
        }

        // 构建结果HTML
        let resultHtml = '<div class="list-group list-group-flush">';

        if (result.files && result.files.length > 0) {
            result.files.forEach(file => {
                resultHtml += `
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${file.output_name}</h6>
                            <small class="text-success">
                                <i class="bi bi-check-circle"></i> 完成
                            </small>
                        </div>
                        <p class="mb-1 text-muted">来源: ${file.original_name}</p>
                        ${file.sheets ? `<small>工作表: ${file.sheets.join(', ')}</small>` : ''}
                        <div class="mt-2">
                            <a href="${file.download_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="bi bi-download"></i> 下载文件
                            </a>
                        </div>
                    </div>
                `;
            });
        } else if (result.result_file) {
            // 处理单个文件结果（向后兼容）
            resultHtml += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${result.result_file}</h6>
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i> 完成
                        </small>
                    </div>
                    <p class="mb-1 text-muted">模板填充完成</p>
                    <div class="mt-2">
                        <a href="${result.download_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="bi bi-download"></i> 下载文件
                        </a>
                    </div>
                </div>
            `;
        }

        if (result.image_results && result.image_results.length > 0) {
            const successCount = result.image_results.filter(r => r.success).length;
            resultHtml += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">图片上传结果</h6>
                        <small class="text-info">
                            <i class="bi bi-images"></i> ${successCount}/${result.image_results.length}
                        </small>
                    </div>
                    <p class="mb-1 text-muted">成功上传 ${successCount} 张图片</p>
                </div>
            `;
        }

        resultHtml += '</div>';
        templateResults.innerHTML = resultHtml;

        // 显示结果卡片和下载区域
        templateResultCard.style.display = 'block';
        if (templateDownloadSection && result.files && result.files.length > 0) {
            templateDownloadSection.style.display = 'block';
            
            // 设置下载按钮事件（下载最新的文件）
            const downloadBtn = document.getElementById('downloadTemplateResult');
            if (downloadBtn) {
                const latestFile = result.files[result.files.length - 1];
                downloadBtn.onclick = () => {
                    window.open(`/api/download/${latestFile.output_name}`, '_blank');
                };
            }
        }

        // 刷新历史文件列表
        this.loadHistoryFiles();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function () {
    window.amazonUploader = new AmazonImageUploader();
});

// 全局错误处理
window.addEventListener('error', function (event) {
    console.error('全局错误:', event.error);
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function (event) {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
});

// 图片重命名功能 - 使用原生JavaScript和jQuery混合方式
$(document).ready(function() {
    console.log('📝 初始化图片重命名功能');
    
    // 重命名表单提交处理
    $('#renameForm').on('submit', function(e) {
        console.log('🚀 重命名表单提交事件触发');
        e.preventDefault();
        e.stopPropagation();
        
        // 验证表单
        const excelFile = $('#rename_excel_file')[0].files[0];
        if (!excelFile) {
            $('#rename_excel_file').addClass('is-invalid');
            return false;
        }
        
        const imageFiles = $('#rename_image_folder')[0].files;
        if (imageFiles.length === 0) {
            $('#rename_image_folder').addClass('is-invalid');
            return false;
        }
        
        // 验证至少选择了一个处理选项
        const mainChecked = $('#process_main').prop('checked');
        const sceneChecked = $('#process_scene').prop('checked');
        const swatchChecked = $('#process_swatch').prop('checked');
        
        if (!mainChecked && !sceneChecked && !swatchChecked) {
            $('#rename-error').text('请至少选择一个处理选项').show();
            return false;
        }
        
        // 显示加载状态
        showLoading(true);
        
        // 准备表单数据
        const formData = new FormData(this);
        
        // 发送请求
        $.ajax({
            url: '/api/rename-images',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.responseType = 'blob';
                return xhr;
            },
            success: function(blob, status, xhr) {
                hideLoading();
                
                // 获取文件名
                const contentDisposition = xhr.getResponseHeader('content-disposition');
                let filename = 'renamed_images.zip';
                if (contentDisposition && contentDisposition.indexOf('filename=') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 显示成功提示
                showToast('处理成功', '图片重命名处理完成，已下载ZIP文件', 'success');
            },
            error: function(xhr) {
                hideLoading();
                
                // 显示错误信息
                try {
                    const response = JSON.parse(xhr.responseText);
                    $('#rename-error').text(response.error || '处理失败').show();
                } catch (e) {
                    $('#rename-error').text('处理失败，请检查文件和选项').show();
                }
            }
        });
    });
    
    // 测试颜色匹配按钮点击处理
    $('#testColorMatchBtn').on('click', function() {
        // 验证表单
        const excelFile = $('#rename_excel_file')[0].files[0];
        if (!excelFile) {
            $('#rename_excel_file').addClass('is-invalid');
            return false;
        }
        
        const imageFiles = $('#rename_image_folder')[0].files;
        if (imageFiles.length === 0) {
            $('#rename_image_folder').addClass('is-invalid');
            return false;
        }
        
        // 显示加载状态
        showLoading(true);
        
        // 准备表单数据
        const formData = new FormData();
        formData.append('excel_file', excelFile);
        
        // 添加所有图片文件
        for (let i = 0; i < imageFiles.length; i++) {
            formData.append('image_folder', imageFiles[i]);
        }
        
        // 发送请求
        $.ajax({
            url: '/api/test-color-matching',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                hideLoading();
                
                // 显示结果区域
                $('#rename-results-section').show();
                
                // 处理新的响应格式
                if (response.status === 'error') {
                    $('#color-match-results').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${response.message}
                        </div>
                    `);
                    return;
                }
                
                if (response.status === 'warning') {
                    $('#color-match-results').html(`
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${response.message}
                        </div>
                    `);
                    return;
                }
                
                // 成功状态 - 显示友好的报告
                let alertClass = 'success';
                if (response.summary && response.summary.match_rate < 70) {
                    alertClass = 'warning';
                } else if (response.summary && response.summary.match_rate < 90) {
                    alertClass = 'info';
                }
                
                // 构建简洁明了的结果HTML
                let html = `
                    <div class="alert alert-${alertClass}">
                        <div style="white-space: pre-line; font-family: monospace; font-size: 14px;">
                            ${response.message}
                        </div>
                    </div>
                `;
                
                // 如果有统计信息，添加简要的数据卡片
                if (response.summary) {
                    const summary = response.summary;
                    const matchRate = summary.match_rate;
                    let progressBarClass = 'bg-danger';
                    if (matchRate >= 90) progressBarClass = 'bg-success';
                    else if (matchRate >= 70) progressBarClass = 'bg-warning';
                    
                    html += `
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6 class="card-title">📊 快速统计</h6>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-2">
                                            <small class="text-muted">匹配成功率</small>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar ${progressBarClass}" role="progressbar" 
                                                    style="width: ${matchRate}%;" 
                                                    aria-valuenow="${matchRate}" 
                                                    aria-valuemin="0" aria-valuemax="100">
                                                    ${matchRate}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <div class="h5 mb-0">${summary.matched_count}/${summary.total_excel_colors}</div>
                                            <small class="text-muted">匹配成功</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                $('#color-match-results').html(html);
            },
            error: function(xhr) {
                hideLoading();
                
                // 显示错误信息
                try {
                    const response = JSON.parse(xhr.responseText);
                    $('#rename-error').text(response.error || '颜色匹配测试失败').show();
                } catch (e) {
                    $('#rename-error').text('颜色匹配测试失败').show();
                }
                
                // 隐藏结果区域
                $('#rename-results-section').hide();
            }
        });
    });
    
    // 表单字段验证重置
    $('#rename_excel_file').on('change', function() {
        $(this).removeClass('is-invalid');
    });
    
    $('#rename_image_folder').on('change', function() {
        $(this).removeClass('is-invalid');
    });
    
    $('input[name="process_main"], input[name="process_scene"], input[name="process_swatch"]').on('change', function() {
        $('#rename-error').hide();
    });
});

// 辅助函数
function hideLoading() {
    $('#loadingOverlay').hide();
}

function showToast(title, message, type = 'success') {
    // 创建并显示Toast通知
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }
    
    // 添加toast并显示
    const toastElement = document.createElement('div');
    toastElement.innerHTML = toastHtml;
    toastContainer.appendChild(toastElement.firstElementChild);
    
    // 显示toast
    const toast = new bootstrap.Toast(toastContainer.lastElementChild, {
        autohide: true,
        delay: 5000
    });
    toast.show();
    
    // 自动清理
    setTimeout(() => {
        if (toastContainer.lastElementChild) {
            toastContainer.removeChild(toastContainer.lastElementChild);
        }
    }, 6000);
}