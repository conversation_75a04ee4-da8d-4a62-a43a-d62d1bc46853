#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🚀 快速测试商品分类报告格式
用于验证新格式（2025年）的商品分类报告兼容性
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'src'))

def quick_test_report(file_path):
    """
    🔍 快速测试商品分类报告格式兼容性
    
    Args:
        file_path (str): 商品分类报告文件路径
    """
    print("🔍 快速测试商品分类报告格式兼容性")
    print("=" * 60)
    print(f"📁 测试文件：{file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print("❌ 文件不存在，请检查路径是否正确")
        return False
    
    try:
        # 导入格式检测函数
        from core.template_filler_web import detect_report_format
        
        print("\n🔍 开始格式检测...")
        
        # 执行格式检测
        header_row, sku_field_name, format_info = detect_report_format(file_path)
        
        print("\n✅ 格式检测成功！")
        print("-" * 40)
        print(f"📊 检测结果：")
        print(f"   🏷️  格式类型：{format_info}")
        print(f"   📍 表头位置：第{header_row+1}行")
        print(f"   🔑 SKU字段名：{sku_field_name}")
        
        # 尝试读取实际数据
        import pandas as pd
        print(f"\n📋 尝试读取数据...")
        
        df = pd.read_excel(file_path, sheet_name='Template', header=header_row, nrows=5, engine='openpyxl')
        print(f"✅ 数据读取成功！")
        print(f"   📊 共发现 {len(df.columns)} 个字段")
        print(f"   📊 读取了 {len(df)} 行数据（示例）")
        
        # 显示字段列表
        print(f"\n📝 字段列表（前10个）：")
        for i, col in enumerate(df.columns[:10], 1):
            mark = "🎯" if col == sku_field_name else "📌"
            print(f"   {mark} {i:2d}. {col}")
        
        if len(df.columns) > 10:
            print(f"   ... 还有 {len(df.columns) - 10} 个字段")
        
        # 显示SKU字段的示例数据
        if sku_field_name in df.columns:
            print(f"\n📋 {sku_field_name}字段示例数据：")
            sku_samples = df[sku_field_name].head(3).tolist()
            for i, sku in enumerate(sku_samples, 1):
                print(f"   {i}. {sku}")
        
        print("\n" + "=" * 60)
        print("🎉 测试完成！您的文件格式完全兼容")
        
        # 给出使用建议
        print("\n💡 使用建议：")
        if "新格式" in format_info:
            print("✅ 这是新格式（2025年）的商品分类报告")
            print("   系统已自动适配，可以正常使用所有功能")
        else:
            print("✅ 这是旧格式（2024年及之前）的商品分类报告")  
            print("   系统保持完全兼容，所有功能正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 格式检测失败：{str(e)}")
        print("\n🔧 排查建议：")
        print("1. 确认文件是亚马逊商品分类报告")
        print("2. 检查文件是否包含Template工作表")
        print("3. 验证文件没有损坏")
        print("4. 确保文件格式为.xlsx或.xlsm")
        
        return False

def main():
    """
    🚀 主函数
    """
    print("🚀 亚马逊图片上传图床工具 - 商品分类报告格式测试")
    print("=" * 80)
    
    # 获取用户输入的文件路径
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        print("📁 请输入商品分类报告文件路径：")
        print("   提示：可以直接拖拽文件到此窗口")
        file_path = input(">>> ").strip().strip('"')
    
    if not file_path:
        print("❌ 未提供文件路径")
        return
    
    # 执行测试
    success = quick_test_report(file_path)
    
    if success:
        print("\n🎯 结论：您的商品分类报告格式完全兼容！")
    else:
        print("\n⚠️ 建议：如需技术支持，请联系开发团队")
    
    print("\n" + "=" * 80)

if __name__ == '__main__':
    main() 