#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - 快速启动脚本
临时解决方案，确保应用能够正常启动
"""

import os
import sys

def main():
    """主启动函数"""
    print("🚀 亚马逊图片上传图床工具 - 快速启动")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)
    
    # 添加当前目录到Python路径
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print(f"📁 工作目录: {current_dir}")
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查必要文件
    required_files = [
        'amazon_image_uploader.py',
        'fill_template_web.py',
        'config/mohe_config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("🔧 尝试恢复缺失文件...")
        
        # 从归档目录恢复文件
        for file in missing_files:
            archive_file = os.path.join('archive', 'legacy', os.path.basename(file))
            if os.path.exists(archive_file):
                import shutil
                shutil.copy2(archive_file, file)
                print(f"✅ 已恢复: {file}")
            else:
                print(f"⚠️ 无法恢复: {file}")
    
    # 尝试启动应用
    try:
        print("\n🔄 正在启动Web应用...")
        
        # 导入Flask应用
        if os.path.exists('amazon_image_uploader.py'):
            # 使用桌面版本启动Web服务
            import subprocess
            result = subprocess.run([
                sys.executable, 
                'amazon_image_uploader.py', 
                '--web'
            ], capture_output=False)
            
        elif os.path.exists('web_app.py'):
            # 直接启动Web版本
            import web_app
            
        else:
            print("❌ 找不到可启动的应用文件")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，应用已退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查 config/mohe_config.py 配置文件")
        print("2. 确保已安装所有依赖: pip install -r requirements.txt")
        print("3. 检查Python环境是否正确")
        sys.exit(1)

if __name__ == '__main__':
    main() 