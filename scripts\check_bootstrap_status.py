#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔍 Bootstrap状态检查和修复工具
快速诊断和解决Bootstrap "not defined" 错误
"""

import os
import sys
from pathlib import Path
import requests
from urllib.parse import urljoin

def check_static_files():
    """检查静态文件的完整性"""
    print("🔍 Bootstrap状态检查工具")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    static_dir = project_root / "src" / "web" / "static"
    
    print(f"📂 项目根目录: {project_root}")
    print(f"📁 静态文件目录: {static_dir}")
    
    # 检查目录是否存在
    if not static_dir.exists():
        print("❌ 静态文件目录不存在！")
        return False
    
    # 需要检查的关键文件
    required_files = [
        {
            "path": "js/bootstrap.bundle.min.js",
            "description": "Bootstrap JavaScript",
            "min_size": 40000,  # 40KB (考虑压缩优化版本)
            "critical": True
        },
        {
            "path": "js/jquery.min.js", 
            "description": "jQuery JavaScript",
            "min_size": 30000,  # 30KB
            "critical": True
        },
        {
            "path": "js/app.js",
            "description": "应用主JavaScript",
            "min_size": 1000,   # 1KB
            "critical": True
        },
        {
            "path": "css/bootstrap.min.css",
            "description": "Bootstrap CSS",
            "min_size": 100000,  # 100KB
            "critical": True
        },
        {
            "path": "fonts/bootstrap-icons.css",
            "description": "Bootstrap Icons CSS",
            "min_size": 10000,   # 10KB
            "critical": False
        },
        {
            "path": "fonts/bootstrap-icons.woff",
            "description": "Bootstrap Icons 字体",
            "min_size": 50000,   # 50KB
            "critical": False
        },
        {
            "path": "fonts/bootstrap-icons.woff2",
            "description": "Bootstrap Icons 字体2",
            "min_size": 40000,   # 40KB
            "critical": False
        }
    ]
    
    print("\n📋 静态文件检查结果：")
    print("-" * 60)
    
    missing_files = []
    corrupted_files = []
    valid_files = []
    
    for file_info in required_files:
        file_path = static_dir / file_info["path"]
        
        if not file_path.exists():
            status = "❌ 缺失"
            missing_files.append(file_info)
        else:
            file_size = file_path.stat().st_size
            size_kb = file_size / 1024
            
            if file_size < file_info["min_size"]:
                status = f"⚠️  文件损坏 ({size_kb:.1f}KB，低于最小要求)"
                corrupted_files.append(file_info)
            else:
                status = f"✅ 正常 ({size_kb:.1f}KB)"
                valid_files.append(file_info)
        
        critical_mark = " 🔥" if file_info["critical"] else ""
        print(f"{file_info['description']:<25} {status}{critical_mark}")
    
    print("-" * 60)
    print(f"📊 检查总结: ✅ {len(valid_files)} 个正常, ❌ {len(missing_files)} 个缺失, ⚠️ {len(corrupted_files)} 个损坏")
    
    # 如果有关键文件缺失或损坏
    critical_missing = [f for f in missing_files if f["critical"]]
    critical_corrupted = [f for f in corrupted_files if f["critical"]]
    
    if critical_missing or critical_corrupted:
        print("\n🚨 发现关键文件问题，这可能导致Bootstrap无法正常工作！")
        
        if critical_missing:
            print("\n缺失的关键文件:")
            for file_info in critical_missing:
                print(f"  • {file_info['description']} ({file_info['path']})")
        
        if critical_corrupted:
            print("\n损坏的关键文件:")
            for file_info in critical_corrupted:
                print(f"  • {file_info['description']} ({file_info['path']})")
        
        print("\n💡 修复建议:")
        print("1. 运行命令: python scripts/download_offline_resources.py")
        print("2. 或者手动下载所需文件")
        print("3. 检查网络连接和防火墙设置")
        
        return False
    
    return True

def check_template_configuration():
    """检查模板中的Bootstrap引用配置"""
    print("\n🔧 检查模板配置...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    template_file = project_root / "src" / "web" / "templates" / "index.html"
    
    if not template_file.exists():
        print("❌ 模板文件不存在！")
        return False
    
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查Bootstrap引用
    checks = [
        {
            "name": "Bootstrap CSS引用",
            "pattern": "bootstrap.min.css",
            "required": True
        },
        {
            "name": "Bootstrap JS引用", 
            "pattern": "bootstrap.bundle.min.js",
            "required": True
        },
        {
            "name": "jQuery引用",
            "pattern": "jquery.min.js", 
            "required": True
        },
        {
            "name": "CDN备用方案",
            "pattern": "loadBootstrapFromCDN",
            "required": False
        },
        {
            "name": "手动实现备用方案",
            "pattern": "initializeManualBootstrapFeatures",
            "required": False
        }
    ]
    
    print("📋 模板配置检查:")
    for check in checks:
        if check["pattern"] in content:
            status = "✅ 已配置"
        else:
            status = "❌ 缺失" if check["required"] else "⚠️  未配置"
        
        print(f"  {check['name']:<20} {status}")
    
    return True

def test_web_server_access():
    """测试Web服务器静态文件访问"""
    print("\n🌐 测试Web服务器访问...")
    
    # 常见的本地服务器端口
    test_urls = [
        "http://localhost:5000",
        "http://127.0.0.1:5000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ]
    
    static_files_to_test = [
        "/static/js/bootstrap.bundle.min.js",
        "/static/css/bootstrap.min.css"
    ]
    
    for base_url in test_urls:
        print(f"\n🔍 测试服务器: {base_url}")
        
        try:
            # 测试主页
            response = requests.get(base_url, timeout=3)
            if response.status_code == 200:
                print(f"  ✅ 主页访问正常")
                
                # 测试静态文件
                for static_file in static_files_to_test:
                    static_url = urljoin(base_url, static_file)
                    try:
                        static_response = requests.get(static_url, timeout=3)
                        if static_response.status_code == 200:
                            size_kb = len(static_response.content) / 1024
                            print(f"  ✅ {static_file} 正常访问 ({size_kb:.1f}KB)")
                        else:
                            print(f"  ❌ {static_file} 访问失败 (HTTP {static_response.status_code})")
                    except requests.RequestException:
                        print(f"  ❌ {static_file} 无法访问")
                
                return True  # 找到一个可用的服务器就返回
                
            else:
                print(f"  ❌ 主页访问失败 (HTTP {response.status_code})")
                
        except requests.RequestException:
            print(f"  ❌ 服务器无法连接")
    
    print("\n⚠️  未找到运行中的Web服务器")
    print("💡 请确保Web服务器正在运行，然后重新测试")
    return False

def provide_fix_suggestions():
    """提供修复建议"""
    print("\n🛠️  修复建议和解决方案")
    print("=" * 60)
    
    print("1️⃣ 立即修复方案 (临时解决):")
    print("   • 在浏览器中按 F12 打开开发者工具")
    print("   • 查看 Network(网络) 标签页中的错误")
    print("   • 如果Bootstrap文件加载失败，页面会自动使用CDN备用方案")
    
    print("\n2️⃣ 根本修复方案 (永久解决):")
    print("   • 运行: python scripts/download_offline_resources.py")
    print("   • 重启Web服务器")
    print("   • 清除浏览器缓存 (Ctrl+Shift+R)")
    
    print("\n3️⃣ 手动修复方案:")
    print("   • 检查src/web/static/目录是否完整")
    print("   • 确保Bootstrap文件大小正常(50KB+)")
    print("   • 验证文件权限是否正确")
    
    print("\n4️⃣ 网络问题排查:")
    print("   • 检查企业防火墙是否阻止CDN访问")
    print("   • 尝试使用其他网络环境")
    print("   • 确保本地服务器端口(5000)未被占用")
    
    print("\n5️⃣ 高级诊断:")
    print("   • 查看浏览器控制台的具体错误信息")
    print("   • 检查服务器日志中的静态文件请求")
    print("   • 验证Flask应用的static_folder配置")

def main():
    """主函数"""
    print("🎯 亚马逊图片上传图床工具 - Bootstrap诊断工具")
    print("专门解决 'bootstrap is not defined' 错误")
    print()
    
    # 步骤1: 检查静态文件
    files_ok = check_static_files()
    
    # 步骤2: 检查模板配置
    template_ok = check_template_configuration()
    
    # 步骤3: 测试Web服务器（可选）
    print("\n" + "="*60)
    user_input = input("是否要测试Web服务器访问? (需要服务器正在运行) [y/N]: ").strip().lower()
    
    if user_input in ['y', 'yes']:
        server_ok = test_web_server_access()
    else:
        print("⏭️  跳过Web服务器测试")
        server_ok = None
    
    # 步骤4: 提供修复建议
    provide_fix_suggestions()
    
    print("\n" + "="*60)
    if files_ok and template_ok:
        print("🎉 静态文件和模板配置检查通过！")
        print("如果仍有问题，请:")
        print("1. 重启Web服务器")
        print("2. 清除浏览器缓存")
        print("3. 检查浏览器开发者工具中的具体错误")
    else:
        print("⚠️  发现配置问题，请按照上述建议进行修复")
    
    return files_ok and template_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 